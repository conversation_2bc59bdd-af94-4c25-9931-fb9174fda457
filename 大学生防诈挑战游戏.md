
# 📝 大学生防诈挑战游戏 – 开发需求文档（PRD V1.1）

## 一、项目概述

### 1.1 项目名称

**大学生防诈挑战游戏（问卷增强模块）**

### 1.2 项目定位

作为**大学生就业调查问卷系统的功能扩展模块**，本项目通过一套独立的防诈骗教育游戏页实现，采用**网页方式匿名注册参与**，结合**答题挑战、内容共建、错误分析与趣味分享机制**，提升大学生防骗意识。

### 1.3 核心特征

* **独立运行：** 完全独立部署，无需对接高校或政府系统
* **匿名机制：** 用户通过 A+B 登录（如邮箱+A码，或邀请码+B昵称）参与，避免隐私问题
* **内容共建：** 用户可投稿题目，经多层审核后进入题库
* **每日参与：** 每天仅能参与一次挑战，增加趣味性与期待感
* **社交传播：** 成绩可生成 **PNG 图片**方便社交平台分享

---

## 二、用户端功能需求

### 2.1 匿名参与机制

* 无需实名注册，使用“A+B”匿名机制完成身份标识

  * A：任意邀请码或邀请码生成器（如问卷页分配）
  * B：用户自定义昵称
* 可选“保存进度”功能，绑定本地缓存或邮件验证码恢复成绩记录

---

### 2.2 答题模块

#### 2.2.1 入门题模式（10题）

* 由平台精选的10道原始题构成
* 所有题均为单选题，选项包括错误解析说明
* 完成后显示：

  * 正确数/总题数
  * 错题解析
  * 学习建议

#### 2.2.2 防诈挑战模式

* 解锁条件：完成入门题
* 持续答题，直到答错或中断
* 每日仅可参与一次挑战
* 记录成绩：答题数量 + 用时（至第一题错误）
* 排行榜按成绩排序（答题数优先，其次用时）

#### 2.2.3 题目标签分类

* 出题与挑战题均支持分类标签：

  * 常见类别如：**求职骗局、租房陷阱、网贷陷阱、培训诈骗、电信诈骗、冒充公检法、兼职诈骗**
* 用户在挑战时可选择某个标签主题作为本轮题目来源

---

### 2.3 用户出题功能

#### 2.3.1 出题流程

* 用户通过页面表单提交以下字段：

  * 题干、4个选项（含正确答案）、每项解析、标签分类、案例来源说明
* 内容提交后，**进入初审系统**：

  * 关键词过滤、不良内容检测（如广告、暴力、敏感词）
  * 未通过初审直接驳回并提示修改原因

#### 2.3.2 人工审核

* 通过初审后，题目进入后台人工审核池
* 审核通过后，题目才可进入正式题库并用于挑战

#### 2.3.3 贡献展示

* 出题成功可获得“贡献值”
* 设立“出题达人”排行榜（匿名昵称展示）

---

### 2.4 成绩与分享机制

* 每日挑战结束后，用户可一键生成“成绩海报 PNG”：

  * 显示成绩、昵称、题目标签、挑战时间等
  * 加入平台 logo 或二维码，鼓励分享
* 限制每天参与 1 次挑战，可设置倒计时提示
* 所有成绩与历史答题记录仅保存在本地缓存（或可选性邮箱备份）

---

## 三、后台管理功能需求

### 3.1 审核与题库管理

* 初审模块（关键词识别）
* 人工审核队列（修改/删除/通过）
* 题目标签管理：支持标签合并/拆分
* 题目难度标记系统（基础/进阶）

### 3.2 数据可视化统计

* 每日参与人数、答题正确率
* 热门题目、错题排行榜
* 用户出题量、审核通过率

---

## 四、页面模块结构

| 页面       | 功能                      |
| -------- | ----------------------- |
| 登录/进入页面  | 匿名 A+B 登录，引导完成问卷后进入反诈挑战 |
| 入门挑战页面   | 固定10道题，展示错题解析           |
| 防诈挑战页面   | 持续答题 + 计时，标签选择器         |
| 排行榜页面    | 显示成绩、用户昵称、标签选择          |
| 用户出题页面   | 出题表单、审核状态查看             |
| 成绩分享页    | 成绩 + 挑战标签 + 生成 PNG 按钮   |
| 学习中心（可选） | 展示防骗案例/题型归类（后期扩展）       |

---

## 五、技术需求与安全机制

### 5.1 前端技术

* HTML5 + Vue.js 或 React（建议使用 TailwindCSS）
* 移动端适配优先

### 5.2 后端技术

* Node.js / Python Flask / Django
* 数据库存储：MySQL / MongoDB
* 内容审核系统：

  * 自动审查（关键词）
  * 人工审查接口
* 防刷机制：

  * 限制每日答题次数（通过 IP + cookie + localStorage 多维识别）
  * 服务器节流策略，防止恶意刷榜

---

## 六、开发建议里程碑（修订）

| 时间       | 任务               |
| -------- | ---------------- |
| Week 1–2 | UI草图 + A/B匿名机制开发 |
| Week 3–4 | 基础答题 + 挑战机制开发    |
| Week 5   | 出题系统 + 自动审核开发    |
| Week 6   | 人工审核后台 + 排行榜展示   |
| Week 7   | 成绩分享海报生成模块       |
| Week 8   | 内部测试 + 优化部署      |

---

## 七、未来可能扩展（可选）

* AI辅助出题/错题分析建议
* “诈骗模拟聊天”训练模块
* 生成个性化防骗学习报告
* 与Telegram/B站等社媒账号打通发布案例

---

## 结语

这个平台可以在不依赖任何组织的前提下，自成生态，逐渐成长为一个集互动、学习与预防于一体的青年反诈学习社区。
