'use client'

import { useState, useEffect, useRef } from 'react'
import { Share2, Download, ArrowLeft, Palette } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

export default function DemoSharePage() {
  const router = useRouter()
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [demoResult, setDemoResult] = useState<any>(null)
  const [selectedTemplate, setSelectedTemplate] = useState<'classic' | 'modern' | 'minimal'>('classic')
  const [isGenerating, setIsGenerating] = useState(false)

  useEffect(() => {
    // 获取体验结果
    const resultStr = localStorage.getItem('demoResult')
    if (!resultStr) {
      router.push('/demo-quiz')
      return
    }
    setDemoResult(JSON.parse(resultStr))
  }, [router])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const generatePoster = async () => {
    if (!canvasRef.current || !demoResult) return

    setIsGenerating(true)
    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 设置画布尺寸
    canvas.width = 600
    canvas.height = 800

    // 根据模板绘制不同样式
    switch (selectedTemplate) {
      case 'classic':
        drawClassicTemplate(ctx, canvas.width, canvas.height)
        break
      case 'modern':
        drawModernTemplate(ctx, canvas.width, canvas.height)
        break
      case 'minimal':
        drawMinimalTemplate(ctx, canvas.width, canvas.height)
        break
    }

    setIsGenerating(false)
  }

  const drawClassicTemplate = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    // 背景渐变
    const gradient = ctx.createLinearGradient(0, 0, 0, height)
    gradient.addColorStop(0, '#3B82F6')
    gradient.addColorStop(1, '#1E40AF')
    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, width, height)

    // 标题
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 36px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('防诈挑战游戏', width / 2, 80)

    // 体验标识
    ctx.font = 'bold 24px Arial'
    ctx.fillText('免费体验版', width / 2, 120)

    // 成绩卡片背景
    ctx.fillStyle = 'rgba(255, 255, 255, 0.95)'
    ctx.roundRect(50, 180, width - 100, 350, 20)
    ctx.fill()

    // 成绩信息
    ctx.fillStyle = '#1F2937'
    ctx.font = 'bold 48px Arial'
    ctx.fillText(`${demoResult.score}/${demoResult.total}`, width / 2, 280)

    ctx.font = '24px Arial'
    ctx.fillText('正确题数', width / 2, 310)

    ctx.font = 'bold 32px Arial'
    ctx.fillText(`${Math.round((demoResult.score / demoResult.total) * 100)}%`, width / 2, 370)

    ctx.font = '20px Arial'
    ctx.fillText('正确率', width / 2, 400)

    ctx.font = 'bold 24px Arial'
    ctx.fillText(formatTime(demoResult.timeUsed), width / 2, 450)
    
    ctx.font = '18px Arial'
    ctx.fillText('用时', width / 2, 475)

    // 日期
    ctx.font = '18px Arial'
    ctx.fillStyle = '#6B7280'
    ctx.fillText(new Date(demoResult.completedAt).toLocaleDateString(), width / 2, 510)

    // 底部文字
    ctx.fillStyle = '#FFFFFF'
    ctx.font = '20px Arial'
    ctx.fillText('提升防诈骗意识，保护自己远离陷阱', width / 2, 650)
    
    ctx.font = '16px Arial'
    ctx.fillText('扫码体验完整版功能', width / 2, 700)
  }

  const drawModernTemplate = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    // 现代风格背景
    ctx.fillStyle = '#F8FAFC'
    ctx.fillRect(0, 0, width, height)

    // 顶部色块
    const gradient = ctx.createLinearGradient(0, 0, width, 0)
    gradient.addColorStop(0, '#8B5CF6')
    gradient.addColorStop(1, '#EC4899')
    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, width, 150)

    // 标题
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 32px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('🛡️ 防诈挑战成绩', width / 2, 60)
    ctx.fillText('(体验版)', width / 2, 110)

    // 成绩卡片
    ctx.fillStyle = '#FFFFFF'
    ctx.shadowColor = 'rgba(0, 0, 0, 0.1)'
    ctx.shadowBlur = 20
    ctx.roundRect(40, 200, width - 80, 300, 15)
    ctx.fill()
    ctx.shadowBlur = 0

    // 成绩数据
    ctx.fillStyle = '#1F2937'
    ctx.font = 'bold 56px Arial'
    ctx.fillText(`${demoResult.score}/${demoResult.total}`, width / 2, 300)

    ctx.font = '24px Arial'
    ctx.fillStyle = '#6B7280'
    ctx.fillText('正确题数', width / 2, 330)

    // 其他信息
    ctx.fillStyle = '#3B82F6'
    ctx.font = 'bold 28px Arial'
    ctx.fillText(`${Math.round((demoResult.score / demoResult.total) * 100)}%`, width / 2, 390)

    ctx.fillStyle = '#6B7280'
    ctx.font = '20px Arial'
    ctx.fillText('正确率', width / 2, 415)

    ctx.fillStyle = '#8B5CF6'
    ctx.font = 'bold 22px Arial'
    ctx.fillText(formatTime(demoResult.timeUsed), width / 2, 460)
    
    ctx.fillStyle = '#6B7280'
    ctx.font = '18px Arial'
    ctx.fillText('用时', width / 2, 485)
  }

  const drawMinimalTemplate = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    // 简约风格白色背景
    ctx.fillStyle = '#FFFFFF'
    ctx.fillRect(0, 0, width, height)

    // 简单边框
    ctx.strokeStyle = '#E5E7EB'
    ctx.lineWidth = 2
    ctx.strokeRect(20, 20, width - 40, height - 40)

    // 标题
    ctx.fillStyle = '#1F2937'
    ctx.font = 'bold 28px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('防诈挑战成绩单', width / 2, 100)
    
    ctx.font = '20px Arial'
    ctx.fillStyle = '#6B7280'
    ctx.fillText('(免费体验版)', width / 2, 130)

    // 分割线
    ctx.strokeStyle = '#D1D5DB'
    ctx.lineWidth = 1
    ctx.beginPath()
    ctx.moveTo(80, 160)
    ctx.lineTo(width - 80, 160)
    ctx.stroke()

    // 成绩信息
    ctx.font = 'bold 48px Arial'
    ctx.fillStyle = '#059669'
    ctx.fillText(`${demoResult.score}/${demoResult.total}`, width / 2, 250)

    ctx.font = '20px Arial'
    ctx.fillStyle = '#6B7280'
    ctx.fillText('正确题数', width / 2, 280)

    ctx.font = 'bold 32px Arial'
    ctx.fillStyle = '#3B82F6'
    ctx.fillText(`${Math.round((demoResult.score / demoResult.total) * 100)}%`, width / 2, 340)

    ctx.font = '18px Arial'
    ctx.fillStyle = '#6B7280'
    ctx.fillText('正确率', width / 2, 365)

    // 底部信息
    ctx.font = '16px Arial'
    ctx.fillText(new Date(demoResult.completedAt).toLocaleDateString(), width / 2, 450)
    
    ctx.font = '18px Arial'
    ctx.fillStyle = '#1F2937'
    ctx.fillText('继续学习，提升防诈骗能力', width / 2, 550)
  }

  const downloadPoster = () => {
    if (!canvasRef.current) return

    const link = document.createElement('a')
    link.download = `防诈挑战体验成绩-${new Date().toLocaleDateString()}.png`
    link.href = canvasRef.current.toDataURL()
    link.click()
  }

  const sharePoster = async () => {
    if (!canvasRef.current) return

    try {
      const blob = await new Promise<Blob>((resolve) => {
        canvasRef.current!.toBlob((blob) => {
          resolve(blob!)
        })
      })

      if (navigator.share) {
        const file = new File([blob], 'score.png', { type: 'image/png' })
        await navigator.share({
          title: '我的防诈挑战体验成绩',
          text: `我在防诈挑战中答对了${demoResult.score}/${demoResult.total}题！一起来体验吧！`,
          files: [file]
        })
      } else {
        // 降级到复制链接
        await navigator.clipboard.writeText(window.location.origin)
        alert('链接已复制到剪贴板')
      }
    } catch (error) {
      console.error('分享失败:', error)
      alert('分享失败，请尝试下载图片手动分享')
    }
  }

  useEffect(() => {
    if (demoResult) {
      generatePoster()
    }
  }, [demoResult, selectedTemplate])

  if (!demoResult) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-4">
      {/* Header */}
      <header className="bg-white shadow-sm border-b mb-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-3">
            <Link href="/demo-quiz" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </Link>
            <Share2 className="w-8 h-8 text-primary-600" />
            <div>
              <h1 className="text-xl font-bold text-gray-900">分享体验成绩</h1>
              <p className="text-sm text-gray-600">生成精美的成绩海报</p>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 控制面板 */}
          <div className="space-y-4">
            {/* 模板选择 */}
            <div className="card">
              <div className="flex items-center gap-2 mb-4">
                <Palette className="w-5 h-5 text-primary-600" />
                <h3 className="text-lg font-semibold text-gray-900">选择模板</h3>
              </div>
              <div className="grid grid-cols-3 gap-3">
                {[
                  { id: 'classic', name: '经典', desc: '蓝色渐变' },
                  { id: 'modern', name: '现代', desc: '彩色卡片' },
                  { id: 'minimal', name: '简约', desc: '黑白风格' }
                ].map((template) => (
                  <button
                    key={template.id}
                    onClick={() => setSelectedTemplate(template.id as any)}
                    className={`p-3 border-2 rounded-lg text-center transition-all ${
                      selectedTemplate === template.id
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-gray-200 hover:border-primary-300'
                    }`}
                  >
                    <div className="font-medium text-gray-900">{template.name}</div>
                    <div className="text-xs text-gray-600">{template.desc}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* 成绩信息 */}
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">体验成绩</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">正确题数</span>
                  <span className="font-semibold text-primary-600">{demoResult.score}/{demoResult.total}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">正确率</span>
                  <span className="font-semibold text-success-600">
                    {Math.round((demoResult.score / demoResult.total) * 100)}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">用时</span>
                  <span className="font-semibold text-blue-600">{formatTime(demoResult.timeUsed)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">完成时间</span>
                  <span className="font-semibold text-gray-900">
                    {new Date(demoResult.completedAt).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="space-y-3">
              <button
                onClick={downloadPoster}
                disabled={isGenerating}
                className="w-full btn-primary flex items-center justify-center gap-2 disabled:opacity-50"
              >
                <Download className="w-5 h-5" />
                下载海报
              </button>
              <button
                onClick={sharePoster}
                disabled={isGenerating}
                className="w-full btn-secondary flex items-center justify-center gap-2 disabled:opacity-50"
              >
                <Share2 className="w-5 h-5" />
                分享海报
              </button>
            </div>

            {/* 升级提示 */}
            <div className="card bg-gradient-to-r from-primary-500 to-purple-600 text-white">
              <h3 className="text-lg font-bold mb-2">想要更多功能？</h3>
              <p className="text-primary-100 mb-4 text-sm">
                注册完整账户，解锁每日挑战、排行榜、社区交流等功能！
              </p>
              <Link href="/login" className="btn-primary bg-white text-primary-600 hover:bg-gray-100 text-sm">
                立即注册
              </Link>
            </div>
          </div>

          {/* 海报预览 */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">海报预览</h3>
            <div className="flex justify-center">
              <div className="relative">
                <canvas
                  ref={canvasRef}
                  className="max-w-full h-auto border border-gray-200 rounded-lg shadow-lg"
                  style={{ maxHeight: '500px' }}
                />
                {isGenerating && (
                  <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
