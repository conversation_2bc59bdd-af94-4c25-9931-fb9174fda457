'use client'

import { useState } from 'react'
import { Brain, ArrowLeft, Sparkles, Target, BarChart3, Users, Lightbulb, Zap } from 'lucide-react'
import Link from 'next/link'

export default function AIFeaturesPage() {
  const [activeDemo, setActiveDemo] = useState<'question-gen' | 'personalized' | 'analytics' | 'team'>('question-gen')

  const demos = {
    'question-gen': {
      title: 'AI 智能出题',
      icon: <Lightbulb className="w-6 h-6" />,
      description: '基于最新诈骗案例和用户答题数据，AI 自动生成高质量题目',
      features: [
        '实时案例分析：从新闻、社交媒体抓取最新诈骗案例',
        '难度智能调节：根据用户水平自动调整题目难度',
        '多样化题型：支持单选、多选、情景判断等多种题型',
        '质量评估：AI 自动评估题目质量和教育价值'
      ],
      mockData: {
        generatedQuestions: 1247,
        qualityScore: 94.2,
        categories: ['求职骗局', '网贷陷阱', '电信诈骗'],
        lastUpdate: '2分钟前'
      }
    },
    'personalized': {
      title: '个性化推荐',
      icon: <Target className="w-6 h-6" />,
      description: '基于用户行为和学习进度，提供个性化的学习路径和题目推荐',
      features: [
        '学习路径规划：根据用户薄弱环节制定学习计划',
        '智能题目推荐：推荐最适合当前水平的题目',
        '知识图谱构建：构建个人防诈骗知识体系',
        '进度跟踪：实时跟踪学习进度和效果'
      ],
      mockData: {
        userLevel: '中级',
        weakPoints: ['租房陷阱', '培训诈骗'],
        recommendedTopics: 3,
        learningProgress: 67
      }
    },
    'analytics': {
      title: '智能数据分析',
      icon: <BarChart3 className="w-6 h-6" />,
      description: '深度分析用户行为和学习效果，提供数据驱动的优化建议',
      features: [
        '学习效果分析：多维度评估学习效果',
        '行为模式识别：识别用户学习习惯和偏好',
        '风险预警：预测用户可能遇到的诈骗风险',
        '优化建议：基于数据提供个性化改进建议'
      ],
      mockData: {
        totalUsers: 15420,
        avgImprovement: 23.5,
        riskReduction: 78,
        satisfactionRate: 92.3
      }
    },
    'team': {
      title: '团队协作挑战',
      icon: <Users className="w-6 h-6" />,
      description: '支持团队组建、协作学习和集体挑战，增强学习互动性',
      features: [
        '智能组队：基于能力水平自动匹配队友',
        '协作任务：设计需要团队合作完成的挑战',
        '集体竞赛：组织跨团队的知识竞赛',
        '社交学习：促进用户间的知识分享和讨论'
      ],
      mockData: {
        activeTeams: 342,
        avgTeamSize: 4.2,
        completionRate: 89,
        collaborationScore: 8.7
      }
    }
  }

  const currentDemo = demos[activeDemo]

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-3">
            <Link href="/dashboard" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </Link>
            <Brain className="w-8 h-8 text-purple-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">AI 功能演示</h1>
              <p className="text-sm text-gray-600">体验未来的智能防诈骗教育</p>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 功能导航 */}
        <div className="card mb-8">
          <div className="flex items-center gap-2 mb-6">
            <Sparkles className="w-6 h-6 text-purple-600" />
            <h2 className="text-xl font-bold text-gray-900">AI 功能模块</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Object.entries(demos).map(([key, demo]) => (
              <button
                key={key}
                onClick={() => setActiveDemo(key as any)}
                className={`p-4 rounded-lg border-2 text-left transition-all ${
                  activeDemo === key
                    ? 'border-purple-500 bg-purple-50'
                    : 'border-gray-200 hover:border-purple-300'
                }`}
              >
                <div className="flex items-center gap-3 mb-2">
                  <div className={`p-2 rounded-lg ${
                    activeDemo === key ? 'bg-purple-100' : 'bg-gray-100'
                  }`}>
                    {demo.icon}
                  </div>
                  <h3 className="font-semibold text-gray-900">{demo.title}</h3>
                </div>
                <p className="text-sm text-gray-600">{demo.description}</p>
              </button>
            ))}
          </div>
        </div>

        {/* 功能详情 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 功能介绍 */}
          <div className="space-y-6">
            <div className="card">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-3 bg-purple-100 rounded-lg">
                  {currentDemo.icon}
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">{currentDemo.title}</h3>
                  <p className="text-gray-600">{currentDemo.description}</p>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900">核心功能：</h4>
                <ul className="space-y-3">
                  {currentDemo.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mt-0.5">
                        <Zap className="w-4 h-4 text-purple-600" />
                      </div>
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* 开发状态 */}
            <div className="card bg-gradient-to-r from-purple-500 to-indigo-600 text-white">
              <h4 className="text-lg font-bold mb-2">🚀 开发状态</h4>
              <p className="text-purple-100 mb-4">
                此功能正在开发中，预计在下个版本中发布。
                当前展示的是功能原型和模拟数据。
              </p>
              <div className="bg-white/10 rounded-lg p-3">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm">开发进度</span>
                  <span className="text-sm font-semibold">65%</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2">
                  <div className="bg-white h-2 rounded-full" style={{ width: '65%' }}></div>
                </div>
              </div>
            </div>
          </div>

          {/* 数据展示 */}
          <div className="space-y-6">
            <div className="card">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">实时数据 (模拟)</h4>
              <div className="grid grid-cols-2 gap-4">
                {Object.entries(currentDemo.mockData).map(([key, value]) => (
                  <div key={key} className="bg-gray-50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-purple-600 mb-1">
                      {typeof value === 'number' ? value.toLocaleString() : value}
                    </div>
                    <div className="text-sm text-gray-600 capitalize">
                      {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 功能演示 */}
            <div className="card">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">功能演示</h4>
              
              {activeDemo === 'question-gen' && (
                <div className="space-y-4">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h5 className="font-semibold text-blue-900 mb-2">AI 生成题目示例：</h5>
                    <p className="text-blue-800 text-sm mb-3">
                      "你收到一条短信，声称你的快递因地址不详被退回，需要点击链接重新填写地址信息。你应该："
                    </p>
                    <div className="text-xs text-blue-600">
                      ✨ 基于最新快递诈骗案例自动生成
                    </div>
                  </div>
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                    <div className="text-sm text-green-800">
                      质量评分: 94.2/100 | 难度: 中等 | 分类: 电信诈骗
                    </div>
                  </div>
                </div>
              )}

              {activeDemo === 'personalized' && (
                <div className="space-y-4">
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h5 className="font-semibold text-yellow-900 mb-2">个性化推荐：</h5>
                    <p className="text-yellow-800 text-sm mb-2">
                      基于你的答题记录，建议重点学习：
                    </p>
                    <ul className="text-yellow-700 text-sm space-y-1">
                      <li>• 租房陷阱识别技巧</li>
                      <li>• 培训诈骗防范方法</li>
                      <li>• 合同条款注意事项</li>
                    </ul>
                  </div>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <div className="text-sm text-blue-800">
                      预计学习时间: 15分钟 | 提升效果: +18%
                    </div>
                  </div>
                </div>
              )}

              {activeDemo === 'analytics' && (
                <div className="space-y-4">
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <h5 className="font-semibold text-purple-900 mb-2">智能分析报告：</h5>
                    <div className="space-y-2 text-purple-800 text-sm">
                      <p>• 用户整体防诈骗意识提升 23.5%</p>
                      <p>• 最容易混淆的诈骗类型：网贷陷阱</p>
                      <p>• 建议增加实际案例练习</p>
                    </div>
                  </div>
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                    <div className="text-sm text-green-800">
                      风险降低: 78% | 用户满意度: 92.3%
                    </div>
                  </div>
                </div>
              )}

              {activeDemo === 'team' && (
                <div className="space-y-4">
                  <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <h5 className="font-semibold text-indigo-900 mb-2">团队挑战示例：</h5>
                    <p className="text-indigo-800 text-sm mb-2">
                      "防诈小分队" 正在进行协作挑战：
                    </p>
                    <ul className="text-indigo-700 text-sm space-y-1">
                      <li>• 队员A: 负责求职诈骗案例分析</li>
                      <li>• 队员B: 负责租房陷阱识别</li>
                      <li>• 队员C: 负责综合防范策略</li>
                    </ul>
                  </div>
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                    <div className="text-sm text-orange-800">
                      团队进度: 67% | 协作评分: 8.7/10
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 反馈区域 */}
        <div className="card mt-8 text-center">
          <h3 className="text-xl font-bold text-gray-900 mb-4">期待您的反馈</h3>
          <p className="text-gray-600 mb-6">
            这些 AI 功能还在开发中，您的意见对我们很重要！
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="btn-primary" disabled>
              提交反馈 (开发中)
            </button>
            <button className="btn-secondary" disabled>
              申请内测 (开发中)
            </button>
            <Link href="/dashboard" className="btn-secondary">
              返回主页
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
