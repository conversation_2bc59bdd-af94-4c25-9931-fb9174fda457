'use client'

import { useState } from 'react'
import { Users, ArrowLeft, MessageCircle, ThumbsUp, Clock, AlertTriangle, CheckCircle } from 'lucide-react'
import Link from 'next/link'

interface CommunityPost {
  id: string
  author: string
  title: string
  content: string
  category: string
  likes: number
  comments: number
  createdAt: string
  isVerified: boolean
}

// 模拟社区帖子数据
const MOCK_POSTS: CommunityPost[] = [
  {
    id: '1',
    author: '防诈达人',
    title: '刚刚遇到的求职诈骗，差点上当！',
    content: '今天接到一个电话说是某知名公司HR，要我去面试。但是要求我先交500元的"资料审核费"，我想起在防诈游戏里学到的知识，立即拒绝了。正规公司绝对不会要求求职者交费！',
    category: '求职骗局',
    likes: 23,
    comments: 8,
    createdAt: '2024-01-15T10:30:00Z',
    isVerified: true
  },
  {
    id: '2',
    author: '谨慎学子',
    title: '租房遇到假房东，幸好及时发现',
    content: '在网上看到一个价格很便宜的房子，房东说要先交定金。我要求看房产证，对方各种推脱。后来发现照片是盗用的，差点被骗！大家租房一定要实地看房，核实房东身份。',
    category: '租房陷阱',
    likes: 18,
    comments: 12,
    createdAt: '2024-01-14T15:20:00Z',
    isVerified: false
  },
  {
    id: '3',
    author: '理性思考',
    title: '网贷诈骗套路深，大家要小心',
    content: '最近收到很多"无抵押快速放款"的短信，点进去看要求先交手续费。记住：正规贷款绝对不会要求预付费用！需要贷款的同学一定要通过银行等正规渠道。',
    category: '网贷陷阱',
    likes: 31,
    comments: 15,
    createdAt: '2024-01-13T09:45:00Z',
    isVerified: true
  },
  {
    id: '4',
    author: '警觉青年',
    title: '冒充公检法电话，差点被吓到',
    content: '昨天接到电话说我涉嫌洗钱，要求我配合调查转账到"安全账户"。刚开始真的被吓到了，但想起游戏里的知识，公检法绝对不会电话要求转账！立即挂断并报警。',
    category: '冒充公检法',
    likes: 27,
    comments: 9,
    createdAt: '2024-01-12T14:10:00Z',
    isVerified: true
  },
  {
    id: '5',
    author: '细心观察',
    title: '兼职刷单诈骗经历分享',
    content: '朋友介绍了一个"刷单兼职"，说日赚200很轻松。要求先垫付商品款，承诺立即返还并给佣金。幸好我在防诈游戏里学过，所有要求垫付的兼职都是诈骗！',
    category: '兼职诈骗',
    likes: 22,
    comments: 11,
    createdAt: '2024-01-11T16:30:00Z',
    isVerified: false
  }
]

export default function CommunityPage() {
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [posts] = useState<CommunityPost[]>(MOCK_POSTS)

  const categories = ['all', '求职骗局', '租房陷阱', '网贷陷阱', '培训诈骗', '电信诈骗', '冒充公检法', '兼职诈骗']
  const categoryLabels: Record<string, string> = {
    'all': '全部',
    '求职骗局': '求职骗局',
    '租房陷阱': '租房陷阱',
    '网贷陷阱': '网贷陷阱',
    '培训诈骗': '培训诈骗',
    '电信诈骗': '电信诈骗',
    '冒充公检法': '冒充公检法',
    '兼职诈骗': '兼职诈骗'
  }

  const filteredPosts = selectedCategory === 'all' 
    ? posts 
    : posts.filter(post => post.category === selectedCategory)

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return '刚刚'
    if (diffInHours < 24) return `${diffInHours}小时前`
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}天前`
    return date.toLocaleDateString()
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-3">
            <Link href="/dashboard" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </Link>
            <Users className="w-8 h-8 text-primary-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">社区</h1>
              <p className="text-sm text-gray-600">分享防诈骗经验，互相学习</p>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 分类筛选 */}
        <div className="card mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">分类筛选</h3>
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                  selectedCategory === category
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {categoryLabels[category]}
              </button>
            ))}
          </div>
        </div>

        {/* 社区公告 */}
        <div className="card mb-8 bg-gradient-to-r from-blue-500 to-purple-600 text-white">
          <div className="flex items-start gap-3">
            <AlertTriangle className="w-6 h-6 text-yellow-300 mt-1" />
            <div>
              <h3 className="text-lg font-bold mb-2">社区公告</h3>
              <p className="text-blue-100 mb-4">
                欢迎大家分享防诈骗经验！请注意保护个人隐私，不要透露具体的个人信息。
                让我们一起建设一个安全、友善的学习社区。
              </p>
              <div className="text-sm text-blue-200">
                • 分享真实经历，帮助他人避免诈骗<br/>
                • 保护个人隐私，不透露敏感信息<br/>
                • 理性讨论，文明交流
              </div>
            </div>
          </div>
        </div>

        {/* 帖子列表 */}
        <div className="space-y-6">
          {filteredPosts.length === 0 ? (
            <div className="card text-center py-12">
              <MessageCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">暂无相关帖子</p>
            </div>
          ) : (
            filteredPosts.map((post) => (
              <div key={post.id} className="card hover:shadow-lg transition-shadow">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                      <Users className="w-5 h-5 text-primary-600" />
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-semibold text-gray-900">{post.author}</span>
                        {post.isVerified && (
                          <CheckCircle className="w-4 h-4 text-blue-500" />
                        )}
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <Clock className="w-4 h-4" />
                        {formatTimeAgo(post.createdAt)}
                        <span className="px-2 py-1 bg-gray-100 rounded-full text-xs">
                          {post.category}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <h3 className="text-lg font-semibold text-gray-900 mb-3">{post.title}</h3>
                <p className="text-gray-700 mb-4 leading-relaxed">{post.content}</p>

                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div className="flex items-center gap-4">
                    <button className="flex items-center gap-2 text-gray-500 hover:text-primary-600 transition-colors">
                      <ThumbsUp className="w-4 h-4" />
                      <span className="text-sm">{post.likes}</span>
                    </button>
                    <button className="flex items-center gap-2 text-gray-500 hover:text-primary-600 transition-colors">
                      <MessageCircle className="w-4 h-4" />
                      <span className="text-sm">{post.comments}</span>
                    </button>
                  </div>
                  <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
                    查看详情
                  </button>
                </div>
              </div>
            ))
          )}
        </div>

        {/* 发帖提示 */}
        <div className="card mt-8 text-center">
          <MessageCircle className="w-12 h-12 text-primary-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">分享你的经验</h3>
          <p className="text-gray-600 mb-4">
            遇到过诈骗或有防诈骗心得？分享给大家，帮助更多人提高警惕！
          </p>
          <button className="btn-primary" disabled>
            发布帖子 (开发中)
          </button>
        </div>
      </div>
    </div>
  )
}
