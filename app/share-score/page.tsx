'use client'

import { useState, useEffect, useRef } from 'react'
import { Share2, Download, <PERSON><PERSON><PERSON><PERSON>, Palette, <PERSON> } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { CATEGORY_LABELS } from '@/types'

interface ScoreData {
  score: number
  timeUsed: number
  category?: string
  date: string
  questionsAnswered: number
}

export default function ShareScorePage() {
  const router = useRouter()
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [scoreData, setScoreData] = useState<ScoreData | null>(null)
  const [userData, setUserData] = useState<any>(null)
  const [selectedTemplate, setSelectedTemplate] = useState<'classic' | 'modern' | 'minimal'>('classic')
  const [isGenerating, setIsGenerating] = useState(false)

  useEffect(() => {
    // 检查用户登录状态
    const userDataStr = localStorage.getItem('userData')
    if (!userDataStr) {
      router.push('/login')
      return
    }
    setUserData(JSON.parse(userDataStr))

    // 获取最近的挑战结果
    const lastResultStr = localStorage.getItem('lastChallengeResult')
    if (!lastResultStr) {
      alert('没有找到挑战结果')
      router.push('/dashboard')
      return
    }
    setScoreData(JSON.parse(lastResultStr))
  }, [router])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const generatePoster = async () => {
    if (!canvasRef.current || !scoreData || !userData) return

    setIsGenerating(true)
    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 设置画布尺寸
    canvas.width = 600
    canvas.height = 800

    // 根据模板绘制不同样式
    switch (selectedTemplate) {
      case 'classic':
        drawClassicTemplate(ctx, canvas.width, canvas.height)
        break
      case 'modern':
        drawModernTemplate(ctx, canvas.width, canvas.height)
        break
      case 'minimal':
        drawMinimalTemplate(ctx, canvas.width, canvas.height)
        break
    }

    setIsGenerating(false)
  }

  const drawClassicTemplate = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    // 背景渐变
    const gradient = ctx.createLinearGradient(0, 0, 0, height)
    gradient.addColorStop(0, '#3B82F6')
    gradient.addColorStop(1, '#1E40AF')
    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, width, height)

    // 标题
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 36px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('防诈挑战游戏', width / 2, 80)

    // 用户昵称
    ctx.font = 'bold 28px Arial'
    ctx.fillText(userData.nickname, width / 2, 140)

    // 成绩卡片背景
    ctx.fillStyle = 'rgba(255, 255, 255, 0.95)'
    ctx.roundRect(50, 200, width - 100, 400, 20)
    ctx.fill()

    // 成绩信息
    ctx.fillStyle = '#1F2937'
    ctx.font = 'bold 48px Arial'
    ctx.fillText(`${scoreData!.score} 题`, width / 2, 300)

    ctx.font = '24px Arial'
    ctx.fillText('正确答题数', width / 2, 330)

    ctx.font = 'bold 32px Arial'
    ctx.fillText(formatTime(scoreData!.timeUsed), width / 2, 400)

    ctx.font = '20px Arial'
    ctx.fillText('用时', width / 2, 430)

    if (scoreData!.category) {
      ctx.font = 'bold 24px Arial'
      ctx.fillText(CATEGORY_LABELS[scoreData!.category as keyof typeof CATEGORY_LABELS], width / 2, 480)
      ctx.font = '18px Arial'
      ctx.fillText('挑战类型', width / 2, 505)
    }

    // 日期
    ctx.font = '18px Arial'
    ctx.fillStyle = '#6B7280'
    ctx.fillText(new Date(scoreData!.date).toLocaleDateString(), width / 2, 560)

    // 底部文字
    ctx.fillStyle = '#FFFFFF'
    ctx.font = '20px Arial'
    ctx.fillText('提升防诈骗意识，保护自己远离陷阱', width / 2, 700)
  }

  const drawModernTemplate = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    // 现代风格背景
    ctx.fillStyle = '#F8FAFC'
    ctx.fillRect(0, 0, width, height)

    // 顶部色块
    const gradient = ctx.createLinearGradient(0, 0, width, 0)
    gradient.addColorStop(0, '#8B5CF6')
    gradient.addColorStop(1, '#EC4899')
    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, width, 150)

    // 标题
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 32px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('🛡️ 防诈挑战成绩', width / 2, 60)
    ctx.fillText(userData.nickname, width / 2, 110)

    // 成绩卡片
    ctx.fillStyle = '#FFFFFF'
    ctx.shadowColor = 'rgba(0, 0, 0, 0.1)'
    ctx.shadowBlur = 20
    ctx.roundRect(40, 200, width - 80, 350, 15)
    ctx.fill()
    ctx.shadowBlur = 0

    // 成绩数据
    ctx.fillStyle = '#1F2937'
    ctx.font = 'bold 56px Arial'
    ctx.fillText(scoreData!.score.toString(), width / 2, 320)

    ctx.font = '24px Arial'
    ctx.fillStyle = '#6B7280'
    ctx.fillText('正确题数', width / 2, 350)

    // 其他信息
    ctx.fillStyle = '#3B82F6'
    ctx.font = 'bold 28px Arial'
    ctx.fillText(formatTime(scoreData!.timeUsed), width / 2, 420)

    ctx.fillStyle = '#6B7280'
    ctx.font = '20px Arial'
    ctx.fillText('挑战用时', width / 2, 445)

    if (scoreData!.category) {
      ctx.fillStyle = '#8B5CF6'
      ctx.font = 'bold 22px Arial'
      ctx.fillText(CATEGORY_LABELS[scoreData!.category as keyof typeof CATEGORY_LABELS], width / 2, 500)
    }
  }

  const drawMinimalTemplate = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    // 简约风格白色背景
    ctx.fillStyle = '#FFFFFF'
    ctx.fillRect(0, 0, width, height)

    // 简单边框
    ctx.strokeStyle = '#E5E7EB'
    ctx.lineWidth = 2
    ctx.strokeRect(20, 20, width - 40, height - 40)

    // 标题
    ctx.fillStyle = '#1F2937'
    ctx.font = 'bold 28px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('防诈挑战成绩单', width / 2, 100)

    // 分割线
    ctx.strokeStyle = '#D1D5DB'
    ctx.lineWidth = 1
    ctx.beginPath()
    ctx.moveTo(80, 130)
    ctx.lineTo(width - 80, 130)
    ctx.stroke()

    // 用户信息
    ctx.fillStyle = '#374151'
    ctx.font = '24px Arial'
    ctx.fillText(`参与者：${userData.nickname}`, width / 2, 180)

    // 成绩信息
    ctx.font = 'bold 48px Arial'
    ctx.fillStyle = '#059669'
    ctx.fillText(scoreData!.score.toString(), width / 2, 280)

    ctx.font = '20px Arial'
    ctx.fillStyle = '#6B7280'
    ctx.fillText('正确答题数', width / 2, 310)

    ctx.font = 'bold 32px Arial'
    ctx.fillStyle = '#3B82F6'
    ctx.fillText(formatTime(scoreData!.timeUsed), width / 2, 380)

    ctx.font = '18px Arial'
    ctx.fillStyle = '#6B7280'
    ctx.fillText('用时', width / 2, 405)

    // 底部信息
    ctx.font = '16px Arial'
    ctx.fillText(new Date(scoreData!.date).toLocaleDateString(), width / 2, 500)
    
    ctx.font = '18px Arial'
    ctx.fillStyle = '#1F2937'
    ctx.fillText('继续学习，提升防诈骗能力', width / 2, 600)
  }

  const downloadPoster = () => {
    if (!canvasRef.current) return

    const link = document.createElement('a')
    link.download = `防诈挑战成绩-${userData.nickname}-${new Date().toLocaleDateString()}.png`
    link.href = canvasRef.current.toDataURL()
    link.click()
  }

  const sharePoster = async () => {
    if (!canvasRef.current) return

    try {
      const blob = await new Promise<Blob>((resolve) => {
        canvasRef.current!.toBlob((blob) => {
          resolve(blob!)
        })
      })

      if (navigator.share) {
        const file = new File([blob], 'score.png', { type: 'image/png' })
        await navigator.share({
          title: '我的防诈挑战成绩',
          text: `我在防诈挑战中答对了${scoreData!.score}题！一起来挑战吧！`,
          files: [file]
        })
      } else {
        // 降级到复制链接
        await navigator.clipboard.writeText(window.location.origin)
        alert('链接已复制到剪贴板')
      }
    } catch (error) {
      console.error('分享失败:', error)
      alert('分享失败，请尝试下载图片手动分享')
    }
  }

  useEffect(() => {
    if (scoreData && userData) {
      generatePoster()
    }
  }, [scoreData, userData, selectedTemplate])

  if (!scoreData || !userData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-3">
            <Link href="/dashboard" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </Link>
            <Share2 className="w-8 h-8 text-primary-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">分享成绩</h1>
              <p className="text-sm text-gray-600">生成精美的成绩海报</p>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 控制面板 */}
          <div className="space-y-6">
            {/* 模板选择 */}
            <div className="card">
              <div className="flex items-center gap-2 mb-4">
                <Palette className="w-5 h-5 text-primary-600" />
                <h3 className="text-lg font-semibold text-gray-900">选择模板</h3>
              </div>
              <div className="grid grid-cols-3 gap-3">
                {[
                  { id: 'classic', name: '经典', desc: '蓝色渐变' },
                  { id: 'modern', name: '现代', desc: '彩色卡片' },
                  { id: 'minimal', name: '简约', desc: '黑白风格' }
                ].map((template) => (
                  <button
                    key={template.id}
                    onClick={() => setSelectedTemplate(template.id as any)}
                    className={`p-3 border-2 rounded-lg text-center transition-all ${
                      selectedTemplate === template.id
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-gray-200 hover:border-primary-300'
                    }`}
                  >
                    <div className="font-medium text-gray-900">{template.name}</div>
                    <div className="text-xs text-gray-600">{template.desc}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* 成绩信息 */}
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">成绩详情</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">正确题数</span>
                  <span className="font-semibold text-primary-600">{scoreData.score}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">用时</span>
                  <span className="font-semibold text-blue-600">{formatTime(scoreData.timeUsed)}</span>
                </div>
                {scoreData.category && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">挑战类型</span>
                    <span className="font-semibold text-purple-600">
                      {CATEGORY_LABELS[scoreData.category as keyof typeof CATEGORY_LABELS]}
                    </span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-600">挑战日期</span>
                  <span className="font-semibold text-gray-900">
                    {new Date(scoreData.date).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="space-y-3">
              <button
                onClick={downloadPoster}
                disabled={isGenerating}
                className="w-full btn-primary flex items-center justify-center gap-2 disabled:opacity-50"
              >
                <Download className="w-5 h-5" />
                下载海报
              </button>
              <button
                onClick={sharePoster}
                disabled={isGenerating}
                className="w-full btn-secondary flex items-center justify-center gap-2 disabled:opacity-50"
              >
                <Share2 className="w-5 h-5" />
                分享海报
              </button>
            </div>
          </div>

          {/* 海报预览 */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">海报预览</h3>
            <div className="flex justify-center">
              <div className="relative">
                <canvas
                  ref={canvasRef}
                  className="max-w-full h-auto border border-gray-200 rounded-lg shadow-lg"
                  style={{ maxHeight: '600px' }}
                />
                {isGenerating && (
                  <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
