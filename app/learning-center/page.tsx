'use client'

import { useState } from 'react'
import { BookOpen, ArrowLeft, AlertTriangle, Shield, Users, CreditCard, Briefcase, Home, GraduationCap, Phone } from 'lucide-react'
import Link from 'next/link'
import { CATEGORY_LABELS, QuestionCategory } from '@/types'

interface LearningContent {
  category: QuestionCategory
  icon: React.ReactNode
  title: string
  description: string
  tips: string[]
  cases: string[]
  prevention: string[]
}

const LEARNING_CONTENTS: LearningContent[] = [
  {
    category: 'job-fraud',
    icon: <Briefcase className="w-6 h-6" />,
    title: '求职骗局',
    description: '利用求职者急于找工作的心理，通过虚假招聘信息进行诈骗。',
    tips: [
      '正规公司不会要求求职者预先交费',
      '警惕"高薪轻松"、"日赚千元"等诱人条件',
      '通过官方渠道核实公司信息',
      '面试地点应在正规办公场所'
    ],
    cases: [
      '虚假招聘要求交"培训费"、"保证金"',
      '冒充知名企业进行电话面试诈骗',
      '以"内推"名义收取"介绍费"',
      '传销组织伪装成正规公司招聘'
    ],
    prevention: [
      '通过企业官网或正规招聘平台投递简历',
      '面试前核实公司营业执照和办公地址',
      '不向任何招聘方预付费用',
      '保持警惕，相信常识判断'
    ]
  },
  {
    category: 'rental-scam',
    icon: <Home className="w-6 h-6" />,
    title: '租房陷阱',
    description: '利用租房需求，通过虚假房源信息或不合理条款进行诈骗。',
    tips: [
      '实地看房，不要仅凭照片决定',
      '必须签署正式租房合同',
      '核实房东身份和房屋产权',
      '警惕价格明显低于市场价的房源'
    ],
    cases: [
      '假房东收取定金后消失',
      '一房多租，同时收取多人定金',
      '虚假房源照片，实际房屋条件极差',
      '不签合同，口头承诺后反悔'
    ],
    prevention: [
      '通过正规中介或房屋租赁平台找房',
      '要求查看房产证和房东身份证',
      '签署正式合同并保留所有凭证',
      '不要一次性支付大额租金'
    ]
  },
  {
    category: 'loan-trap',
    icon: <CreditCard className="w-6 h-6" />,
    title: '网贷陷阱',
    description: '以"无抵押、快速放款"为诱饵，实际收取高额费用的贷款诈骗。',
    tips: [
      '正规贷款不需要预交任何费用',
      '警惕"无条件放款"的虚假承诺',
      '了解正常贷款流程和利率水平',
      '通过银行等正规金融机构贷款'
    ],
    cases: [
      '要求预交"手续费"、"保证金"',
      '承诺"无条件放款"后收费不放款',
      '利率远超法律规定的高利贷',
      '套取个人信息后进行其他诈骗'
    ],
    prevention: [
      '选择银行或持牌金融机构',
      '仔细阅读贷款合同条款',
      '不向任何贷款机构预付费用',
      '保护个人身份和银行信息'
    ]
  },
  {
    category: 'training-scam',
    icon: <GraduationCap className="w-6 h-6" />,
    title: '培训诈骗',
    description: '以技能培训、学历提升为名，收取高额费用但不提供承诺服务。',
    tips: [
      '核实培训机构的资质和口碑',
      '警惕"包过"、"包就业"等承诺',
      '了解正常的培训费用水平',
      '不要被"限时优惠"冲昏头脑'
    ],
    cases: [
      '承诺"包过包就业"收取高额培训费',
      '虚假宣传培训效果和就业率',
      '以"内部渠道"名义收取额外费用',
      '培训内容与宣传严重不符'
    ],
    prevention: [
      '选择有资质的正规培训机构',
      '实地考察培训环境和师资',
      '签署详细的培训协议',
      '保留所有付费和培训凭证'
    ]
  },
  {
    category: 'telecom-fraud',
    icon: <Phone className="w-6 h-6" />,
    title: '电信诈骗',
    description: '通过电话、短信、网络等方式实施的远程诈骗活动。',
    tips: [
      '不轻信陌生电话和短信',
      '不点击可疑链接',
      '不向陌生人透露个人信息',
      '遇到可疑情况及时报警'
    ],
    cases: [
      '冒充银行客服套取银行卡信息',
      '虚假中奖信息要求交税费',
      '冒充快递公司要求点击链接',
      '虚假购物退款要求提供验证码'
    ],
    prevention: [
      '通过官方渠道核实信息真伪',
      '不在陌生网站输入敏感信息',
      '设置复杂密码并定期更换',
      '开启银行卡短信提醒功能'
    ]
  },
  {
    category: 'fake-authority',
    icon: <Shield className="w-6 h-6" />,
    title: '冒充公检法',
    description: '冒充公安、检察院、法院等执法部门进行的诈骗活动。',
    tips: [
      '执法部门不会电话要求转账',
      '不存在所谓的"安全账户"',
      '正规执法程序不会保密进行',
      '遇到此类电话立即挂断'
    ],
    cases: [
      '声称涉嫌洗钱要求转账配合调查',
      '冒充法院要求交纳保证金',
      '声称快递涉毒要求证明清白',
      '冒充警察要求提供银行信息'
    ],
    prevention: [
      '立即挂断可疑电话',
      '到就近派出所核实情况',
      '不向任何人转账汇款',
      '保持冷静，理性判断'
    ]
  },
  {
    category: 'part-time-scam',
    icon: <Users className="w-6 h-6" />,
    title: '兼职诈骗',
    description: '以兼职工作为诱饵，要求预付费用或套取个人信息的诈骗。',
    tips: [
      '正规兼职不需要预付费用',
      '警惕"轻松赚钱"的虚假承诺',
      '通过正规平台寻找兼职',
      '保护个人身份信息安全'
    ],
    cases: [
      '刷单兼职要求预付保证金',
      '打字兼职要求购买软件',
      '代理加盟要求交纳代理费',
      '网络兼职套取银行卡信息'
    ],
    prevention: [
      '选择知名的兼职招聘平台',
      '不向任何兼职方预付费用',
      '核实兼职公司的真实性',
      '保留所有沟通和交易记录'
    ]
  }
]

export default function LearningCenterPage() {
  const [selectedCategory, setSelectedCategory] = useState<QuestionCategory>('job-fraud')
  
  const currentContent = LEARNING_CONTENTS.find(content => content.category === selectedCategory)!

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-3">
            <Link href="/dashboard" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </Link>
            <BookOpen className="w-8 h-8 text-primary-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">学习中心</h1>
              <p className="text-sm text-gray-600">防诈骗知识库</p>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 分类导航 */}
          <div className="lg:col-span-1">
            <div className="card sticky top-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">诈骗类型</h3>
              <nav className="space-y-2">
                {LEARNING_CONTENTS.map((content) => (
                  <button
                    key={content.category}
                    onClick={() => setSelectedCategory(content.category)}
                    className={`w-full flex items-center gap-3 p-3 rounded-lg text-left transition-all ${
                      selectedCategory === content.category
                        ? 'bg-primary-100 text-primary-700 border-primary-200'
                        : 'hover:bg-gray-50 text-gray-700'
                    }`}
                  >
                    {content.icon}
                    <span className="font-medium">{CATEGORY_LABELS[content.category]}</span>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* 内容区域 */}
          <div className="lg:col-span-3 space-y-8">
            {/* 概述 */}
            <div className="card">
              <div className="flex items-center gap-3 mb-4">
                {currentContent.icon}
                <h2 className="text-2xl font-bold text-gray-900">{currentContent.title}</h2>
              </div>
              <p className="text-gray-600 text-lg">{currentContent.description}</p>
            </div>

            {/* 识别要点 */}
            <div className="card">
              <div className="flex items-center gap-2 mb-4">
                <AlertTriangle className="w-6 h-6 text-yellow-500" />
                <h3 className="text-xl font-semibold text-gray-900">识别要点</h3>
              </div>
              <ul className="space-y-3">
                {currentContent.tips.map((tip, index) => (
                  <li key={index} className="flex items-start gap-3">
                    <div className="flex-shrink-0 w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center mt-0.5">
                      <span className="text-yellow-600 text-sm font-bold">{index + 1}</span>
                    </div>
                    <span className="text-gray-700">{tip}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* 常见案例 */}
            <div className="card">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">常见案例</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {currentContent.cases.map((case_, index) => (
                  <div key={index} className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                        <AlertTriangle className="w-4 h-4 text-red-600" />
                      </div>
                      <span className="text-red-800 text-sm">{case_}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 防范措施 */}
            <div className="card">
              <div className="flex items-center gap-2 mb-4">
                <Shield className="w-6 h-6 text-green-500" />
                <h3 className="text-xl font-semibold text-gray-900">防范措施</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {currentContent.prevention.map((measure, index) => (
                  <div key={index} className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                        <Shield className="w-4 h-4 text-green-600" />
                      </div>
                      <span className="text-green-800 text-sm">{measure}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 行动建议 */}
            <div className="card bg-gradient-to-r from-primary-500 to-purple-600 text-white">
              <h3 className="text-xl font-bold mb-4">立即行动</h3>
              <p className="text-primary-100 mb-6">
                学习了{currentContent.title}的防范知识，现在就去挑战相关题目，检验你的学习成果吧！
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link 
                  href="/challenge" 
                  className="btn-primary bg-white text-primary-600 hover:bg-gray-100"
                >
                  开始挑战
                </Link>
                <Link 
                  href="/starter-quiz" 
                  className="btn-secondary bg-white/10 text-white border-white/20 hover:bg-white/20"
                >
                  重做入门题
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
