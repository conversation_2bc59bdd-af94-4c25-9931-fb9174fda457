'use client'

import { useState, useEffect } from 'react'
import { Shield, Users, Trophy, BookOpen, ArrowRight, AlertTriangle, Plus } from 'lucide-react'
import Link from 'next/link'

export default function HomePage() {
  const [stats, setStats] = useState({
    totalUsers: 1234,
    questionsAnswered: 8765,
    successRate: 87
  })

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-8">
              <div className="p-4 bg-white/10 rounded-full backdrop-blur-sm">
                <Shield className="w-16 h-16 text-white" />
              </div>
            </div>
            <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
              大学生防诈挑战游戏
            </h1>
            <p className="text-lg md:text-xl text-blue-100 mb-6 max-w-3xl mx-auto">
              通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Link href="/demo-quiz" className="btn-primary text-lg px-6 py-3 inline-flex items-center gap-2">
                免费答题 (10题)
                <ArrowRight className="w-5 h-5" />
              </Link>
              <Link href="/submit-question" className="btn-secondary bg-white/10 text-white border-white/20 hover:bg-white/20 text-lg px-6 py-3 inline-flex items-center gap-2">
                体验出题
                <Plus className="w-5 h-5" />
              </Link>
              <Link href="/login" className="btn-secondary bg-white/10 text-white border-white/20 hover:bg-white/20 text-lg px-6 py-3 inline-flex items-center gap-2">
                完整体验
                <ArrowRight className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-8 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="text-4xl font-bold text-primary-600 mb-2">{stats.totalUsers.toLocaleString()}</div>
              <div className="text-gray-600">参与用户</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-primary-600 mb-2">{stats.questionsAnswered.toLocaleString()}</div>
              <div className="text-gray-600">题目完成数</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-primary-600 mb-2">{stats.successRate}%</div>
              <div className="text-gray-600">平均正确率</div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              游戏特色
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              通过科学的游戏机制，让防诈骗学习变得有趣且有效
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="card text-center">
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Shield className="w-6 h-6 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">匿名参与</h3>
              <p className="text-gray-600">
                无需实名注册，通过邀请码+昵称的方式匿名参与，保护隐私
              </p>
            </div>

            <div className="card text-center">
              <div className="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <BookOpen className="w-6 h-6 text-success-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">分类学习</h3>
              <p className="text-gray-600">
                涵盖求职、租房、网贷、培训等多种诈骗类型，针对性学习
              </p>
            </div>

            <div className="card text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Users className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">共建题库</h3>
              <p className="text-gray-600">
                用户可以提交真实案例，经审核后加入题库，共同建设
              </p>
            </div>

            <div className="card text-center">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Trophy className="w-6 h-6 text-orange-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">排行榜</h3>
              <p className="text-gray-600">
                每日挑战排行榜，激发学习动力，分享成绩海报
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <div className="flex justify-center mb-6">
            <AlertTriangle className="w-16 h-16 text-yellow-300" />
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            提高警惕，远离诈骗
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            每天只需几分钟，通过答题挑战提升防诈骗能力，保护自己和身边的人
          </p>
          <Link href="/login" className="btn-primary bg-white text-primary-600 hover:bg-gray-100 text-lg px-8 py-4 inline-flex items-center gap-2">
            立即开始学习
            <ArrowRight className="w-5 h-5" />
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <Shield className="w-8 h-8 text-primary-400" />
            </div>
            <h3 className="text-xl font-semibold mb-2">大学生防诈挑战游戏</h3>
            <p className="text-gray-400 mb-6">
              让防诈骗学习变得有趣且有效
            </p>
            <div className="text-sm text-gray-500">
              © 2024 防诈挑战游戏. 保护大学生远离诈骗陷阱.
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
