'use client'

import { useState, useEffect } from 'react'
import { 
  Shield, 
  Trophy, 
  BookOpen, 
  Users, 
  Clock, 
  Target,
  Plus,
  Calendar,
  ArrowRight,
  Star
} from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { CATEGORY_LABELS } from '@/types'

export default function DashboardPage() {
  const router = useRouter()
  const [userData, setUserData] = useState<any>(null)
  const [starterResult, setStarterResult] = useState<any>(null)
  const [lastChallengeDate, setLastChallengeDate] = useState<string | null>(null)
  const [canChallenge, setCanChallenge] = useState(true)

  useEffect(() => {
    // 检查用户登录状态
    const userDataStr = localStorage.getItem('userData')
    if (!userDataStr) {
      router.push('/login')
      return
    }

    const user = JSON.parse(userDataStr)
    setUserData(user)

    // 检查入门题完成状态
    const starterCompletedStr = localStorage.getItem('starterCompleted')
    if (!starterCompletedStr) {
      router.push('/starter-quiz')
      return
    }

    // 获取入门题结果
    const starterResultStr = localStorage.getItem('starterResult')
    if (starterResultStr) {
      setStarterResult(JSON.parse(starterResultStr))
    }

    // 检查今日是否已挑战
    const lastChallenge = localStorage.getItem('lastChallengeDate')
    const today = new Date().toDateString()
    
    if (lastChallenge === today) {
      setCanChallenge(false)
    }
    setLastChallengeDate(lastChallenge)
  }, [router])

  const getTimeUntilNextChallenge = () => {
    if (canChallenge) return null
    
    const now = new Date()
    const tomorrow = new Date(now)
    tomorrow.setDate(tomorrow.getDate() + 1)
    tomorrow.setHours(0, 0, 0, 0)
    
    const diff = tomorrow.getTime() - now.getTime()
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    
    return `${hours}小时${minutes}分钟`
  }

  if (!userData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Shield className="w-8 h-8 text-primary-600" />
              <div>
                <h1 className="text-xl font-bold text-gray-900">防诈挑战游戏</h1>
                <p className="text-sm text-gray-600">欢迎回来，{userData.nickname}</p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-right">
                <div className="text-sm text-gray-600">贡献值</div>
                <div className="font-semibold text-primary-600">{userData.contributionScore || 0}</div>
              </div>
              <Link href="/profile" className="btn-secondary">
                个人中心
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Card */}
        {starterResult && (
          <div className="card mb-8 bg-gradient-to-r from-primary-500 to-purple-600 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold mb-2">入门挑战已完成！</h2>
                <p className="text-primary-100 mb-4">
                  你在入门测试中答对了 {starterResult.score}/{starterResult.total} 题，
                  正确率 {Math.round((starterResult.score / starterResult.total) * 100)}%
                </p>
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    用时 {Math.floor(starterResult.timeUsed / 60)}:{(starterResult.timeUsed % 60).toString().padStart(2, '0')}
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    {new Date(starterResult.completedAt).toLocaleDateString()}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <Star className="w-16 h-16 text-yellow-300" />
              </div>
            </div>
          </div>
        )}

        {/* Main Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Daily Challenge */}
          <div className="card">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-primary-100 rounded-lg">
                <Target className="w-6 h-6 text-primary-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900">每日挑战</h3>
                <p className="text-gray-600">持续答题直到答错，挑战你的极限</p>
              </div>
            </div>

            {canChallenge ? (
              <div className="space-y-4">
                <div className="bg-success-50 border border-success-200 rounded-lg p-3">
                  <p className="text-success-800 text-sm">
                    ✨ 今日挑战已准备就绪！选择一个分类开始挑战吧
                  </p>
                </div>
                <Link href="/challenge" className="btn-primary w-full flex items-center justify-center gap-2">
                  开始今日挑战
                  <ArrowRight className="w-5 h-5" />
                </Link>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <p className="text-yellow-800 text-sm">
                    ⏰ 今日挑战已完成，请明天再来！
                  </p>
                  <p className="text-yellow-700 text-xs mt-1">
                    距离下次挑战还有 {getTimeUntilNextChallenge()}
                  </p>
                </div>
                <button disabled className="btn-primary w-full opacity-50 cursor-not-allowed">
                  今日挑战已完成
                </button>
              </div>
            )}
          </div>

          {/* Submit Question */}
          <div className="card">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-success-100 rounded-lg">
                <Plus className="w-6 h-6 text-success-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900">贡献题目</h3>
                <p className="text-gray-600">分享真实案例，帮助更多人防范诈骗</p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <p className="text-blue-800 text-sm">
                  💡 你的真实经历可能帮助其他人避免诈骗
                </p>
              </div>
              <Link href="/submit-question" className="btn-success w-full flex items-center justify-center gap-2">
                提交题目
                <Plus className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>

        {/* Quick Links */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Link href="/leaderboard" className="card hover:shadow-lg transition-shadow">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Trophy className="w-6 h-6 text-yellow-600" />
              </div>
              <div>
                <h4 className="font-semibold text-gray-900">排行榜</h4>
                <p className="text-sm text-gray-600">查看挑战排名</p>
              </div>
            </div>
          </Link>

          <Link href="/learning-center" className="card hover:shadow-lg transition-shadow">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <BookOpen className="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <h4 className="font-semibold text-gray-900">学习中心</h4>
                <p className="text-sm text-gray-600">防诈骗知识库</p>
              </div>
            </div>
          </Link>

          <Link href="/community" className="card hover:shadow-lg transition-shadow">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Users className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <h4 className="font-semibold text-gray-900">社区</h4>
                <p className="text-sm text-gray-600">用户交流分享</p>
              </div>
            </div>
          </Link>
        </div>

        {/* 未来功能预览 */}
        <div className="mt-12">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">🚀 即将推出</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Link href="/ai-features" className="card hover:shadow-lg transition-shadow bg-gradient-to-r from-purple-50 to-indigo-50 border-purple-200">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <span className="text-2xl">🤖</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">AI 智能功能</h4>
                  <p className="text-sm text-gray-600">智能出题、个性化推荐、数据分析</p>
                </div>
              </div>
              <div className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full inline-block">
                功能演示
              </div>
            </Link>

            <Link href="/multimedia-content" className="card hover:shadow-lg transition-shadow bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <span className="text-2xl">🎬</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">多媒体内容</h4>
                  <p className="text-sm text-gray-600">视频教学、音频课程、互动体验</p>
                </div>
              </div>
              <div className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full inline-block">
                内容预览
              </div>
            </Link>
          </div>
        </div>

        {/* Categories Preview */}
        <div className="mt-12">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">诈骗类型分类</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
            {Object.entries(CATEGORY_LABELS).map(([key, label]) => (
              <div key={key} className="card text-center p-4 hover:shadow-lg transition-shadow">
                <div className="text-2xl mb-2">🛡️</div>
                <div className="text-sm font-medium text-gray-900">{label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
