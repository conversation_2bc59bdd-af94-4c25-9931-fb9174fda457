'use client'

import { useState } from 'react'
import { Trophy, ArrowLeft, Medal, Crown, Star, TrendingUp, Calendar, Filter } from 'lucide-react'
import Link from 'next/link'

export default function LeaderboardPage() {
  const [timeFilter, setTimeFilter] = useState<'daily' | 'weekly' | 'monthly' | 'all'>('weekly')
  const [categoryFilter, setCategoryFilter] = useState<'all' | 'job' | 'rental' | 'finance'>('all')

  const leaderboardStats = {
    totalPlayers: 15420,
    activeToday: 892,
    averageScore: 78.5,
    topScore: 98
  }

  const topPlayers = [
    {
      rank: 1,
      nickname: '防诈大师',
      score: 98,
      streak: 15,
      category: '综合',
      avatar: '👑',
      level: '专家级',
      achievements: ['连胜王', '满分达人', '知识渊博'],
      joinDate: '2024-01-01',
      totalQuestions: 450
    },
    {
      rank: 2,
      nickname: '安全卫士',
      score: 96,
      streak: 12,
      category: '求职防骗',
      avatar: '🛡️',
      level: '高级',
      achievements: ['求职专家', '稳定发挥'],
      joinDate: '2024-01-03',
      totalQuestions: 380
    },
    {
      rank: 3,
      nickname: '谨慎学者',
      score: 95,
      streak: 10,
      category: '金融理财',
      avatar: '📚',
      level: '高级',
      achievements: ['理财达人', '学习之星'],
      joinDate: '2024-01-05',
      totalQuestions: 320
    },
    {
      rank: 4,
      nickname: '防骗达人',
      score: 94,
      streak: 8,
      category: '租房安全',
      avatar: '🏠',
      level: '中级',
      achievements: ['租房专家'],
      joinDate: '2024-01-08',
      totalQuestions: 280
    },
    {
      rank: 5,
      nickname: '智慧守护',
      score: 93,
      streak: 7,
      category: '综合',
      avatar: '🧠',
      level: '中级',
      achievements: ['全能选手'],
      joinDate: '2024-01-10',
      totalQuestions: 250
    }
  ]

  const categoryLeaders = {
    'job': { name: '防诈小能手', score: 97, avatar: '💼' },
    'rental': { name: '房产专家', score: 95, avatar: '🏠' },
    'finance': { name: '理财高手', score: 96, avatar: '💰' },
    'education': { name: '教育达人', score: 94, avatar: '🎓' }
  }

  const achievements = [
    { name: '连胜王', description: '连续答对15题', icon: '🔥', rarity: 'legendary' },
    { name: '满分达人', description: '获得满分10次', icon: '💯', rarity: 'epic' },
    { name: '知识渊博', description: '完成所有分类', icon: '📖', rarity: 'rare' },
    { name: '求职专家', description: '求职类题目90%正确率', icon: '💼', rarity: 'uncommon' },
    { name: '理财达人', description: '金融类题目90%正确率', icon: '💰', rarity: 'uncommon' }
  ]

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return <Crown className="w-6 h-6 text-yellow-500" />
      case 2: return <Medal className="w-6 h-6 text-gray-400" />
      case 3: return <Medal className="w-6 h-6 text-amber-600" />
      default: return <span className="w-6 h-6 flex items-center justify-center text-gray-600 font-bold">{rank}</span>
    }
  }

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return 'text-purple-600 bg-purple-100'
      case 'epic': return 'text-orange-600 bg-orange-100'
      case 'rare': return 'text-blue-600 bg-blue-100'
      case 'uncommon': return 'text-green-600 bg-green-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 to-orange-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-3">
            <Link href="/" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </Link>
            <Trophy className="w-8 h-8 text-yellow-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">排行榜</h1>
              <p className="text-sm text-gray-600">竞技学习，激发动力</p>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 统计概览 */}
        <div className="card mb-8 bg-gradient-to-r from-yellow-500 to-orange-600 text-white">
          <h2 className="text-2xl font-bold mb-6">竞技统计</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{leaderboardStats.totalPlayers.toLocaleString()}</div>
              <div className="text-sm text-yellow-200">总参与人数</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{leaderboardStats.activeToday}</div>
              <div className="text-sm text-yellow-200">今日活跃</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{leaderboardStats.averageScore}%</div>
              <div className="text-sm text-yellow-200">平均分数</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{leaderboardStats.topScore}%</div>
              <div className="text-sm text-yellow-200">最高分数</div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 主排行榜 */}
          <div className="lg:col-span-2">
            <div className="card">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900">总排行榜</h3>
                <div className="flex gap-2">
                  <select
                    value={timeFilter}
                    onChange={(e) => setTimeFilter(e.target.value as any)}
                    className="px-3 py-1 border border-gray-300 rounded-lg text-sm"
                  >
                    <option value="daily">今日</option>
                    <option value="weekly">本周</option>
                    <option value="monthly">本月</option>
                    <option value="all">全部</option>
                  </select>
                  <select
                    value={categoryFilter}
                    onChange={(e) => setCategoryFilter(e.target.value as any)}
                    className="px-3 py-1 border border-gray-300 rounded-lg text-sm"
                  >
                    <option value="all">全部分类</option>
                    <option value="job">求职防骗</option>
                    <option value="rental">租房安全</option>
                    <option value="finance">金融理财</option>
                  </select>
                </div>
              </div>

              <div className="space-y-4">
                {topPlayers.map((player) => (
                  <div key={player.rank} className={`p-4 rounded-lg border-2 transition-all ${
                    player.rank <= 3 
                      ? 'bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200' 
                      : 'bg-gray-50 border-gray-200'
                  }`}>
                    <div className="flex items-center gap-4">
                      <div className="flex-shrink-0">
                        {getRankIcon(player.rank)}
                      </div>
                      <div className="text-3xl">{player.avatar}</div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-bold text-gray-900">{player.nickname}</span>
                          <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                            {player.level}
                          </span>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>分数: {player.score}%</span>
                          <span>连胜: {player.streak}</span>
                          <span>专长: {player.category}</span>
                          <span>题目: {player.totalQuestions}</span>
                        </div>
                        <div className="flex flex-wrap gap-1 mt-2">
                          {player.achievements.map((achievement, index) => (
                            <span key={index} className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">
                              {achievement}
                            </span>
                          ))}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-primary-600">{player.score}%</div>
                        <div className="text-xs text-gray-500">#{player.rank}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 侧边栏 */}
          <div className="space-y-6">
            {/* 分类冠军 */}
            <div className="card">
              <h3 className="text-lg font-bold text-gray-900 mb-4">分类冠军</h3>
              <div className="space-y-3">
                {Object.entries(categoryLeaders).map(([key, leader]) => (
                  <div key={key} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <span className="text-2xl">{leader.avatar}</span>
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{leader.name}</div>
                      <div className="text-sm text-gray-600">{leader.score}% 正确率</div>
                    </div>
                    <Crown className="w-4 h-4 text-yellow-500" />
                  </div>
                ))}
              </div>
            </div>

            {/* 成就系统 */}
            <div className="card">
              <h3 className="text-lg font-bold text-gray-900 mb-4">稀有成就</h3>
              <div className="space-y-3">
                {achievements.map((achievement, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg">
                    <span className="text-xl">{achievement.icon}</span>
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{achievement.name}</div>
                      <div className="text-sm text-gray-600">{achievement.description}</div>
                    </div>
                    <span className={`px-2 py-1 text-xs rounded-full ${getRarityColor(achievement.rarity)}`}>
                      {achievement.rarity}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* 我的排名 */}
            <div className="card bg-gradient-to-r from-blue-500 to-purple-600 text-white">
              <h3 className="text-lg font-bold mb-4">我的排名</h3>
              <div className="text-center">
                <div className="text-3xl mb-2">🎯</div>
                <div className="text-2xl font-bold mb-1">#42</div>
                <div className="text-sm text-blue-200 mb-3">当前排名</div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="font-bold">85%</div>
                    <div className="text-blue-200">我的分数</div>
                  </div>
                  <div>
                    <div className="font-bold">5</div>
                    <div className="text-blue-200">连胜次数</div>
                  </div>
                </div>
                <Link href="/demo-quiz" className="btn-secondary mt-4 w-full">
                  继续挑战
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* 竞技规则 */}
        <div className="card mt-8">
          <h3 className="text-xl font-bold text-gray-900 mb-6">竞技规则</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Star className="w-6 h-6 text-blue-600" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">分数计算</h4>
              <p className="text-sm text-gray-600">基于正确率和答题速度综合计算</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <TrendingUp className="w-6 h-6 text-green-600" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">连胜奖励</h4>
              <p className="text-sm text-gray-600">连续答对可获得额外分数加成</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Calendar className="w-6 h-6 text-purple-600" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">周期更新</h4>
              <p className="text-sm text-gray-600">排行榜每周重置，保持竞争活力</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Trophy className="w-6 h-6 text-yellow-600" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">奖励机制</h4>
              <p className="text-sm text-gray-600">前三名可获得特殊称号和徽章</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
