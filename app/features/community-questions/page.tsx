'use client'

import { useState } from 'react'
import { Users, ArrowLeft, Plus, CheckCircle, Clock, Star, TrendingUp, Award } from 'lucide-react'
import Link from 'next/link'

export default function CommunityQuestionsPage() {
  const [activeTab, setActiveTab] = useState<'overview' | 'submit' | 'review' | 'contributors'>('overview')

  const communityStats = {
    totalSubmissions: 1247,
    approvedQuestions: 892,
    activeContributors: 156,
    averageQuality: 8.7
  }

  const recentSubmissions = [
    {
      id: 'Q001',
      title: '虚假招聘要求交"体检费"的识别方法',
      author: '防诈小能手',
      category: '求职骗局',
      status: 'approved',
      submittedAt: '2024-01-15',
      votes: 23,
      quality: 9.2
    },
    {
      id: 'Q002',
      title: '租房遇到假房东的真实案例分析',
      author: '谨慎租客',
      category: '租房陷阱',
      status: 'reviewing',
      submittedAt: '2024-01-14',
      votes: 18,
      quality: 8.8
    },
    {
      id: 'Q003',
      title: '网贷"砍头息"的计算和识别技巧',
      author: '理财达人',
      category: '网贷陷阱',
      status: 'approved',
      submittedAt: '2024-01-13',
      votes: 31,
      quality: 9.5
    },
    {
      id: 'Q004',
      title: '培训机构"包就业"承诺的陷阱分析',
      author: '教育观察者',
      category: '培训诈骗',
      status: 'pending',
      submittedAt: '2024-01-12',
      votes: 12,
      quality: 8.3
    }
  ]

  const topContributors = [
    {
      nickname: '防诈专家',
      contributions: 45,
      approvalRate: 96,
      totalVotes: 1250,
      badge: '🏆',
      level: '专家级'
    },
    {
      nickname: '安全卫士',
      contributions: 38,
      approvalRate: 92,
      totalVotes: 980,
      badge: '🥇',
      level: '高级'
    },
    {
      nickname: '谨慎学者',
      contributions: 32,
      approvalRate: 89,
      totalVotes: 756,
      badge: '🥈',
      level: '高级'
    },
    {
      nickname: '防骗达人',
      contributions: 28,
      approvalRate: 94,
      totalVotes: 623,
      badge: '🥉',
      level: '中级'
    }
  ]

  const submissionGuidelines = [
    {
      title: '基于真实案例',
      description: '题目应该基于真实的诈骗案例或常见诈骗手段',
      icon: '📋',
      examples: ['亲身经历的诈骗尝试', '新闻报道的真实案例', '朋友遭遇的诈骗事件']
    },
    {
      title: '教育意义明确',
      description: '题目应该具有明确的教育价值，帮助用户识别和防范诈骗',
      icon: '🎯',
      examples: ['识别诈骗特征', '防范措施说明', '正确应对方法']
    },
    {
      title: '选项设计合理',
      description: '提供4个选项，包含1个正确答案和3个干扰项',
      icon: '✅',
      examples: ['正确的防范措施', '常见的错误做法', '似是而非的选项']
    },
    {
      title: '解析详细准确',
      description: '为每个选项提供详细解析，说明为什么正确或错误',
      icon: '📝',
      examples: ['正确答案的原理', '错误选项的风险', '相关法律法规']
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-600 bg-green-100'
      case 'reviewing': return 'text-yellow-600 bg-yellow-100'
      case 'pending': return 'text-blue-600 bg-blue-100'
      case 'rejected': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'approved': return '已通过'
      case 'reviewing': return '审核中'
      case 'pending': return '待审核'
      case 'rejected': return '已拒绝'
      default: return '未知'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-3">
            <Link href="/" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </Link>
            <Users className="w-8 h-8 text-purple-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">共建题库</h1>
              <p className="text-sm text-gray-600">用户共同建设，知识共享</p>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 统计概览 */}
        <div className="card mb-8 bg-gradient-to-r from-purple-500 to-indigo-600 text-white">
          <h2 className="text-2xl font-bold mb-6">社区贡献统计</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{communityStats.totalSubmissions}</div>
              <div className="text-sm text-purple-200">总提交数</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{communityStats.approvedQuestions}</div>
              <div className="text-sm text-purple-200">已通过题目</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{communityStats.activeContributors}</div>
              <div className="text-sm text-purple-200">活跃贡献者</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{communityStats.averageQuality}/10</div>
              <div className="text-sm text-purple-200">平均质量分</div>
            </div>
          </div>
        </div>

        {/* 标签导航 */}
        <div className="card mb-8">
          <div className="flex flex-wrap gap-2">
            {[
              { key: 'overview', label: '概览', icon: TrendingUp },
              { key: 'submit', label: '提交指南', icon: Plus },
              { key: 'review', label: '审核流程', icon: CheckCircle },
              { key: 'contributors', label: '贡献者', icon: Award }
            ].map(({ key, label, icon: Icon }) => (
              <button
                key={key}
                onClick={() => setActiveTab(key as any)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all ${
                  activeTab === key
                    ? 'bg-purple-100 text-purple-700 border-purple-300 border-2'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Icon className="w-4 h-4" />
                {label}
              </button>
            ))}
          </div>
        </div>

        {/* 内容区域 */}
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* 最近提交 */}
            <div className="card">
              <h3 className="text-xl font-bold text-gray-900 mb-6">最近提交的题目</h3>
              <div className="space-y-4">
                {recentSubmissions.map((submission) => (
                  <div key={submission.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-900 mb-1">{submission.title}</h4>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>作者: {submission.author}</span>
                          <span>分类: {submission.category}</span>
                          <span>提交: {submission.submittedAt}</span>
                        </div>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(submission.status)}`}>
                        {getStatusLabel(submission.status)}
                      </span>
                    </div>
                    <div className="flex items-center gap-4 text-sm">
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 text-yellow-500" />
                        <span>{submission.quality}/10</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <TrendingUp className="w-4 h-4 text-blue-500" />
                        <span>{submission.votes} 票</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'submit' && (
          <div className="space-y-8">
            {/* 提交指南 */}
            <div className="card">
              <h3 className="text-xl font-bold text-gray-900 mb-6">题目提交指南</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {submissionGuidelines.map((guideline, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center gap-3 mb-3">
                      <span className="text-2xl">{guideline.icon}</span>
                      <h4 className="font-semibold text-gray-900">{guideline.title}</h4>
                    </div>
                    <p className="text-gray-600 mb-3">{guideline.description}</p>
                    <div className="space-y-1">
                      <div className="text-sm font-medium text-gray-700">示例：</div>
                      {guideline.examples.map((example, idx) => (
                        <div key={idx} className="text-sm text-gray-600">• {example}</div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 提交按钮 */}
            <div className="text-center">
              <Link href="/submit-question" className="btn-primary text-lg px-8 py-4 inline-flex items-center gap-2">
                <Plus className="w-5 h-5" />
                立即提交题目
              </Link>
            </div>
          </div>
        )}

        {activeTab === 'review' && (
          <div className="card">
            <h3 className="text-xl font-bold text-gray-900 mb-6">审核流程</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-blue-600 font-bold">1</span>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">提交题目</h4>
                <p className="text-sm text-gray-600">用户提交题目和解析</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-yellow-600 font-bold">2</span>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">初步审核</h4>
                <p className="text-sm text-gray-600">检查格式和基本要求</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-purple-600 font-bold">3</span>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">专家评审</h4>
                <p className="text-sm text-gray-600">专家评估教育价值</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-green-600 font-bold">4</span>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">发布上线</h4>
                <p className="text-sm text-gray-600">通过审核加入题库</p>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'contributors' && (
          <div className="card">
            <h3 className="text-xl font-bold text-gray-900 mb-6">优秀贡献者</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {topContributors.map((contributor, index) => (
                <div key={index} className="text-center p-4 border border-gray-200 rounded-lg">
                  <div className="text-3xl mb-2">{contributor.badge}</div>
                  <div className="font-semibold text-gray-900 mb-1">{contributor.nickname}</div>
                  <div className="text-sm text-gray-600 mb-3">{contributor.level}</div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>贡献题目:</span>
                      <span className="font-medium">{contributor.contributions}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>通过率:</span>
                      <span className="font-medium text-green-600">{contributor.approvalRate}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>获得点赞:</span>
                      <span className="font-medium text-blue-600">{contributor.totalVotes}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 激励机制 */}
        <div className="card mt-8 bg-gradient-to-r from-green-500 to-blue-600 text-white">
          <h3 className="text-xl font-bold mb-4">🎁 贡献激励机制</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="font-semibold mb-2">贡献值奖励</h4>
              <ul className="text-green-100 text-sm space-y-1">
                <li>• 提交题目: +1 贡献值</li>
                <li>• 题目通过: +5 贡献值</li>
                <li>• 获得点赞: +0.1 贡献值</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">等级系统</h4>
              <ul className="text-green-100 text-sm space-y-1">
                <li>• 新手: 0-10 贡献值</li>
                <li>• 中级: 11-50 贡献值</li>
                <li>• 高级: 51-100 贡献值</li>
                <li>• 专家: 100+ 贡献值</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">特殊权益</h4>
              <ul className="text-green-100 text-sm space-y-1">
                <li>• 优先审核通道</li>
                <li>• 专属贡献者标识</li>
                <li>• 参与题库管理</li>
                <li>• 获得纪念证书</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
