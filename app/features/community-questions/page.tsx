'use client'

import { useState } from 'react'
import { Users, ArrowLeft, Plus, Star, TrendingUp, Award } from 'lucide-react'
import Link from 'next/link'

export default function CommunityQuestionsPage() {
  const [activeTab, setActiveTab] = useState<'overview' | 'submit' | 'contributors'>('overview')

  const communityStats = {
    totalSubmissions: 1247,
    approvedQuestions: 892,
    activeContributors: 156,
    averageQuality: 8.7
  }

  const recentSubmissions = [
    {
      id: 'Q001',
      title: '虚假招聘要求交"体检费"的识别方法',
      author: '防诈小能手',
      category: '求职骗局',
      status: 'approved',
      submittedAt: '2024-01-15',
      votes: 23,
      quality: 9.2
    },
    {
      id: 'Q002',
      title: '租房遇到假房东的真实案例分析',
      author: '谨慎租客',
      category: '租房陷阱',
      status: 'reviewing',
      submittedAt: '2024-01-14',
      votes: 18,
      quality: 8.8
    }
  ]

  const topContributors = [
    {
      nickname: '防诈专家',
      contributions: 45,
      approvalRate: 96,
      totalVotes: 1250,
      badge: '🏆',
      level: '专家级'
    },
    {
      nickname: '安全卫士',
      contributions: 38,
      approvalRate: 92,
      totalVotes: 980,
      badge: '🥇',
      level: '高级'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-600 bg-green-100'
      case 'reviewing': return 'text-yellow-600 bg-yellow-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'approved': return '已通过'
      case 'reviewing': return '审核中'
      default: return '未知'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-3">
            <Link href="/" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </Link>
            <Users className="w-8 h-8 text-purple-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">共建题库</h1>
              <p className="text-sm text-gray-600">用户共同建设，知识共享</p>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 统计概览 */}
        <div className="card mb-8 bg-gradient-to-r from-purple-500 to-indigo-600 text-white">
          <h2 className="text-2xl font-bold mb-6">社区贡献统计</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{communityStats.totalSubmissions}</div>
              <div className="text-sm text-purple-200">总提交数</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{communityStats.approvedQuestions}</div>
              <div className="text-sm text-purple-200">已通过题目</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{communityStats.activeContributors}</div>
              <div className="text-sm text-purple-200">活跃贡献者</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{communityStats.averageQuality}/10</div>
              <div className="text-sm text-purple-200">平均质量分</div>
            </div>
          </div>
        </div>

        {/* 标签导航 */}
        <div className="card mb-8">
          <div className="flex flex-wrap gap-2">
            {[
              { key: 'overview', label: '概览', icon: TrendingUp },
              { key: 'submit', label: '提交指南', icon: Plus },
              { key: 'contributors', label: '贡献者', icon: Award }
            ].map(({ key, label, icon: Icon }) => (
              <button
                key={key}
                onClick={() => setActiveTab(key as any)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all ${
                  activeTab === key
                    ? 'bg-purple-100 text-purple-700 border-purple-300 border-2'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Icon className="w-4 h-4" />
                {label}
              </button>
            ))}
          </div>
        </div>

        {/* 内容区域 */}
        {activeTab === 'overview' && (
          <div className="card">
            <h3 className="text-xl font-bold text-gray-900 mb-6">最近提交的题目</h3>
            <div className="space-y-4">
              {recentSubmissions.map((submission) => (
                <div key={submission.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-1">{submission.title}</h4>
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span>作者: {submission.author}</span>
                        <span>分类: {submission.category}</span>
                        <span>提交: {submission.submittedAt}</span>
                      </div>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(submission.status)}`}>
                      {getStatusLabel(submission.status)}
                    </span>
                  </div>
                  <div className="flex items-center gap-4 text-sm">
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 text-yellow-500" />
                      <span>{submission.quality}/10</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <TrendingUp className="w-4 h-4 text-blue-500" />
                      <span>{submission.votes} 票</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'submit' && (
          <div className="card">
            <h3 className="text-xl font-bold text-gray-900 mb-6">题目提交指南</h3>
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-3">📋 基于真实案例</h4>
                  <p className="text-gray-600 mb-3">题目应该基于真实的诈骗案例</p>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• 亲身经历的诈骗尝试</li>
                    <li>• 新闻报道的真实案例</li>
                    <li>• 朋友遭遇的诈骗事件</li>
                  </ul>
                </div>
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-3">🎯 教育意义明确</h4>
                  <p className="text-gray-600 mb-3">题目应该具有明确的教育价值</p>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• 识别诈骗特征</li>
                    <li>• 防范措施说明</li>
                    <li>• 正确应对方法</li>
                  </ul>
                </div>
              </div>
              <div className="text-center">
                <Link href="/submit-question" className="btn-primary text-lg px-8 py-4 inline-flex items-center gap-2">
                  <Plus className="w-5 h-5" />
                  立即提交题目
                </Link>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'contributors' && (
          <div className="card">
            <h3 className="text-xl font-bold text-gray-900 mb-6">优秀贡献者</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {topContributors.map((contributor, index) => (
                <div key={index} className="text-center p-6 border border-gray-200 rounded-lg">
                  <div className="text-4xl mb-3">{contributor.badge}</div>
                  <div className="font-bold text-lg text-gray-900 mb-1">{contributor.nickname}</div>
                  <div className="text-sm text-gray-600 mb-4">{contributor.level}</div>
                  <div className="space-y-3 text-sm">
                    <div className="bg-blue-50 rounded-lg p-3">
                      <div className="text-lg font-bold text-blue-600">{contributor.contributions}</div>
                      <div className="text-blue-700">贡献题目</div>
                    </div>
                    <div className="bg-green-50 rounded-lg p-3">
                      <div className="text-lg font-bold text-green-600">{contributor.approvalRate}%</div>
                      <div className="text-green-700">通过率</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 激励机制 */}
        <div className="card mt-8 bg-gradient-to-r from-green-500 to-blue-600 text-white">
          <h3 className="text-xl font-bold mb-4">🎁 贡献激励机制</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="font-semibold mb-2">贡献值奖励</h4>
              <ul className="text-green-100 text-sm space-y-1">
                <li>• 提交题目: +1 贡献值</li>
                <li>• 题目通过: +5 贡献值</li>
                <li>• 获得点赞: +0.1 贡献值</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">等级系统</h4>
              <ul className="text-green-100 text-sm space-y-1">
                <li>• 新手: 0-10 贡献值</li>
                <li>• 中级: 11-50 贡献值</li>
                <li>• 高级: 51-100 贡献值</li>
                <li>• 专家: 100+ 贡献值</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">特殊权益</h4>
              <ul className="text-green-100 text-sm space-y-1">
                <li>• 优先审核通道</li>
                <li>• 专属贡献者标识</li>
                <li>• 参与题库管理</li>
                <li>• 获得纪念证书</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}