'use client'

import { useState } from 'react'
import { AlertTriangle, ArrowLeft, Eye, Heart, MessageCircle, Share2, Filter, Search } from 'lucide-react'
import Link from 'next/link'

export default function RealCasesPage() {
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'job' | 'rental' | 'finance' | 'education'>('all')
  const [selectedRisk, setSelectedRisk] = useState<'all' | 'high' | 'medium' | 'low'>('all')

  const caseStats = {
    totalCases: 1247,
    verifiedCases: 892,
    totalViews: 156420,
    helpfulVotes: 8934
  }

  const realCases = [
    {
      id: 'case001',
      title: '求职遇到"培训费"陷阱，差点被骗5000元',
      author: '小心求职者',
      category: 'job',
      riskLevel: 'high',
      amount: 5000,
      publishedAt: '2024-01-15',
      views: 1250,
      likes: 89,
      comments: 23,
      verified: true,
      tags: ['培训费', '虚假招聘', '求职陷阱'],
      summary: '应聘时被要求先交培训费，幸好及时识破骗局',
      content: `我在某招聘网站上看到一个"高薪客服"的职位，月薪8000-12000，工作轻松。投递简历后很快收到面试通知。

面试官说我很符合要求，但需要先参加公司的"专业培训"，培训费5000元，培训结束后会退还并正式入职。

当时我觉得有些奇怪，但面试官说这是为了筛选真正有意向的员工。我差点就交钱了，幸好朋友提醒我这是典型的培训费诈骗。

后来我查了这家公司，发现根本不存在，网站也是假的。现在想想真是后怕，差点就被骗了5000元。`,
      lessons: [
        '正规公司不会要求员工交培训费',
        '入职前要核实公司真实性',
        '遇到要求先交钱的工作要警惕',
        '可以通过企查查等平台验证公司信息'
      ],
      prevention: [
        '通过官方渠道投递简历',
        '面试时注意观察公司环境',
        '不要轻易交纳任何费用',
        '与朋友家人商量再做决定'
      ]
    },
    {
      id: 'case002',
      title: '租房遇到假房东，押金3000元打水漂',
      author: '租房小白',
      category: 'rental',
      riskLevel: 'medium',
      amount: 3000,
      publishedAt: '2024-01-14',
      views: 980,
      likes: 67,
      comments: 18,
      verified: true,
      tags: ['假房东', '押金诈骗', '租房陷阱'],
      summary: '通过网络找房遇到假房东，交了押金后人就消失了',
      content: `在某租房APP上看到一套很便宜的房子，位置好价格低，我立刻联系了"房东"。

"房东"说人在外地，不能现场看房，但可以先交押金锁定房源。他发了很多房子的照片，看起来很真实。

我担心房子被别人租走，就通过微信转账交了3000元押金。约定第二天去看房签合同。

结果第二天到了地址，发现根本联系不上"房东"，微信也被拉黑了。后来才知道那些照片都是从网上盗用的，我遇到了假房东。`,
      lessons: [
        '租房一定要实地看房',
        '不要轻信网上的房源照片',
        '交钱前要核实房东身份',
        '通过正规中介或平台租房更安全'
      ],
      prevention: [
        '要求查看房产证和身份证',
        '实地看房后再谈价格',
        '通过银行转账留下记录',
        '签订正式租房合同'
      ]
    },
    {
      id: 'case003',
      title: '网贷"砍头息"陷阱，实际年利率超过100%',
      author: '理财新手',
      category: 'finance',
      riskLevel: 'high',
      amount: 10000,
      publishedAt: '2024-01-13',
      views: 1580,
      likes: 124,
      comments: 35,
      verified: true,
      tags: ['砍头息', '高利贷', '网贷陷阱'],
      summary: '急需用钱申请网贷，遇到砍头息陷阱，实际利率惊人',
      content: `因为急需1万元周转，我在网上找到一个贷款平台，声称"低息快贷，当天放款"。

申请时平台说月利率只有1.5%，看起来很合理。但放款时发现实际到账只有8000元，平台说扣除了"服务费"、"手续费"等2000元。

还款时我才发现，虽然借了1万元，但实际只拿到8000元，却要按1万元还款。算下来实际年利率超过了100%，这就是典型的"砍头息"。

现在我深陷其中，每月还款压力巨大，真后悔当初没有仔细了解。`,
      lessons: [
        '砍头息是违法的高利贷行为',
        '借款前要仔细计算实际利率',
        '正规金融机构不会预扣费用',
        '遇到资金困难要通过正当渠道解决'
      ],
      prevention: [
        '选择银行等正规金融机构',
        '仔细阅读借款合同条款',
        '计算真实的借款成本',
        '不要被"低息"广告迷惑'
      ]
    }
  ]

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high': return 'text-red-600 bg-red-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'low': return 'text-green-600 bg-green-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getRiskLabel = (risk: string) => {
    switch (risk) {
      case 'high': return '高危'
      case 'medium': return '中危'
      case 'low': return '低危'
      default: return '未知'
    }
  }

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'job': return '求职骗局'
      case 'rental': return '租房陷阱'
      case 'finance': return '金融诈骗'
      case 'education': return '教育骗局'
      default: return '其他'
    }
  }

  const filteredCases = realCases.filter(caseItem => {
    const categoryMatch = selectedCategory === 'all' || caseItem.category === selectedCategory
    const riskMatch = selectedRisk === 'all' || caseItem.riskLevel === selectedRisk
    return categoryMatch && riskMatch
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-3">
            <Link href="/" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </Link>
            <AlertTriangle className="w-8 h-8 text-red-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">真实案例</h1>
              <p className="text-sm text-gray-600">真实经历，警示教育</p>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 统计概览 */}
        <div className="card mb-8 bg-gradient-to-r from-red-500 to-orange-600 text-white">
          <h2 className="text-2xl font-bold mb-6">案例统计</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{caseStats.totalCases}</div>
              <div className="text-sm text-red-200">总案例数</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{caseStats.verifiedCases}</div>
              <div className="text-sm text-red-200">已验证</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{caseStats.totalViews.toLocaleString()}</div>
              <div className="text-sm text-red-200">总浏览量</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{caseStats.helpfulVotes}</div>
              <div className="text-sm text-red-200">有用投票</div>
            </div>
          </div>
        </div>

        {/* 筛选器 */}
        <div className="card mb-8">
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">筛选：</span>
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
            >
              <option value="all">全部分类</option>
              <option value="job">求职骗局</option>
              <option value="rental">租房陷阱</option>
              <option value="finance">金融诈骗</option>
              <option value="education">教育骗局</option>
            </select>
            <select
              value={selectedRisk}
              onChange={(e) => setSelectedRisk(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
            >
              <option value="all">全部风险等级</option>
              <option value="high">高危</option>
              <option value="medium">中危</option>
              <option value="low">低危</option>
            </select>
          </div>
        </div>

        {/* 案例列表 */}
        <div className="space-y-6">
          {filteredCases.map((caseItem) => (
            <div key={caseItem.id} className="card hover:shadow-lg transition-shadow">
              <div className="flex items-start gap-4 mb-4">
                <div className="flex-shrink-0">
                  <AlertTriangle className="w-6 h-6 text-red-500" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-lg font-bold text-gray-900">{caseItem.title}</h3>
                    {caseItem.verified && (
                      <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                        已验证
                      </span>
                    )}
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskColor(caseItem.riskLevel)}`}>
                      {getRiskLabel(caseItem.riskLevel)}
                    </span>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                    <span>作者: {caseItem.author}</span>
                    <span>分类: {getCategoryLabel(caseItem.category)}</span>
                    <span>涉及金额: ¥{caseItem.amount.toLocaleString()}</span>
                    <span>发布: {caseItem.publishedAt}</span>
                  </div>
                  <p className="text-gray-700 mb-4">{caseItem.summary}</p>
                  
                  <div className="flex flex-wrap gap-2 mb-4">
                    {caseItem.tags.map((tag, index) => (
                      <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                        #{tag}
                      </span>
                    ))}
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4 mb-4">
                    <h4 className="font-semibold text-gray-900 mb-2">案例详情</h4>
                    <div className="text-gray-700 text-sm whitespace-pre-line">
                      {caseItem.content}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div className="bg-red-50 rounded-lg p-3">
                      <h5 className="font-semibold text-red-900 mb-2">⚠️ 经验教训</h5>
                      <ul className="text-red-800 text-sm space-y-1">
                        {caseItem.lessons.map((lesson, index) => (
                          <li key={index}>• {lesson}</li>
                        ))}
                      </ul>
                    </div>
                    <div className="bg-green-50 rounded-lg p-3">
                      <h5 className="font-semibold text-green-900 mb-2">🛡️ 防范措施</h5>
                      <ul className="text-green-800 text-sm space-y-1">
                        {caseItem.prevention.map((measure, index) => (
                          <li key={index}>• {measure}</li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <Eye className="w-4 h-4" />
                        <span>{caseItem.views}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Heart className="w-4 h-4" />
                        <span>{caseItem.likes}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <MessageCircle className="w-4 h-4" />
                        <span>{caseItem.comments}</span>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <button className="flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-red-600 transition-colors">
                        <Heart className="w-4 h-4" />
                        有用
                      </button>
                      <button className="flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-blue-600 transition-colors">
                        <Share2 className="w-4 h-4" />
                        分享
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 提交案例 */}
        <div className="card mt-8 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-center">
          <h3 className="text-xl font-bold mb-4">分享你的经历，帮助更多人</h3>
          <p className="text-blue-100 mb-6">
            如果你也有防诈骗的真实经历，欢迎分享给大家，让更多人从中受益。
          </p>
          <Link href="/demo-share" className="btn-secondary inline-flex items-center gap-2">
            <Share2 className="w-5 h-5" />
            分享我的经历
          </Link>
        </div>
      </div>
    </div>
  )
}
