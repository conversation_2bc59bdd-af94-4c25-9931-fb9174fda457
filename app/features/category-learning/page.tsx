'use client'

import { useState } from 'react'
import { BookOpen, ArrowLeft, Target, TrendingUp, Award, Users, BarChart3 } from 'lucide-react'
import Link from 'next/link'

export default function CategoryLearningPage() {
  const [selectedCategory, setSelectedCategory] = useState('job-fraud')

  const categories = {
    'job-fraud': {
      title: '求职防骗',
      icon: '💼',
      color: 'blue',
      description: '识别虚假招聘、培训费诈骗等求职陷阱',
      questionCount: 25,
      completionRate: 78,
      averageScore: 85,
      difficulty: '入门',
      learningPath: [
        '基础概念：什么是求职诈骗',
        '常见套路：培训费、保证金陷阱',
        '识别技巧：如何辨别虚假招聘',
        '防范措施：正确的求职方式',
        '实战演练：真实案例分析'
      ],
      recentUpdates: [
        '新增"AI面试诈骗"专题',
        '更新最新求职诈骗案例',
        '优化题目难度梯度'
      ]
    },
    'rental-scam': {
      title: '租房安全',
      icon: '🏠',
      color: 'green',
      description: '防范假房东、押金诈骗等租房风险',
      questionCount: 18,
      completionRate: 65,
      averageScore: 79,
      difficulty: '入门',
      learningPath: [
        '租房基础：正规租房流程',
        '风险识别：假房东特征',
        '合同要点：租房合同注意事项',
        '资金安全：押金支付技巧',
        '维权方法：遇到问题如何处理'
      ],
      recentUpdates: [
        '新增"长租公寓陷阱"内容',
        '更新租房合同模板',
        '增加地区性案例分析'
      ]
    },
    'loan-trap': {
      title: '金融理财',
      icon: '💰',
      color: 'yellow',
      description: '识别网贷陷阱、投资诈骗等金融风险',
      questionCount: 32,
      completionRate: 52,
      averageScore: 72,
      difficulty: '进阶',
      learningPath: [
        '金融基础：正规金融机构识别',
        '网贷风险：套路贷识别技巧',
        '投资陷阱：虚假理财产品',
        '利率计算：真实成本分析',
        '维权途径：金融纠纷处理'
      ],
      recentUpdates: [
        '新增"虚拟货币诈骗"专题',
        '更新最新金融监管政策',
        '增加计算器工具'
      ]
    },
    'training-scam': {
      title: '教育培训',
      icon: '🎓',
      color: 'purple',
      description: '防范培训诈骗、学历造假等教育陷阱',
      questionCount: 15,
      completionRate: 43,
      averageScore: 68,
      difficulty: '进阶',
      learningPath: [
        '培训机构：正规资质识别',
        '承诺陷阱："包过包就业"风险',
        '费用合理性：培训成本分析',
        '合同条款：培训协议要点',
        '维权指南：培训纠纷处理'
      ],
      recentUpdates: [
        '新增"在线教育诈骗"内容',
        '更新培训机构查询方法',
        '增加退费维权指南'
      ]
    }
  }

  const currentCategory = categories[selectedCategory as keyof typeof categories]

  const learningStats = {
    totalLearners: 12580,
    completedToday: 456,
    averageImprovement: 23.5,
    satisfactionRate: 94.2
  }

  const topLearners = [
    { nickname: '学习达人', score: 98, category: '求职防骗', badge: '🏆' },
    { nickname: '防诈专家', score: 96, category: '金融理财', badge: '🥇' },
    { nickname: '安全卫士', score: 95, category: '租房安全', badge: '🥈' },
    { nickname: '谨慎学者', score: 94, category: '教育培训', badge: '🥉' }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-3">
            <Link href="/" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </Link>
            <BookOpen className="w-8 h-8 text-success-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">分类学习</h1>
              <p className="text-sm text-gray-600">针对性学习，高效提升</p>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 学习统计 */}
        <div className="card mb-8 bg-gradient-to-r from-success-500 to-green-600 text-white">
          <h2 className="text-2xl font-bold mb-6">分类学习数据概览</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{learningStats.totalLearners.toLocaleString()}</div>
              <div className="text-sm text-green-200">总学习人数</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{learningStats.completedToday}</div>
              <div className="text-sm text-green-200">今日完成</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{learningStats.averageImprovement}%</div>
              <div className="text-sm text-green-200">平均提升</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{learningStats.satisfactionRate}%</div>
              <div className="text-sm text-green-200">满意度</div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 分类选择 */}
          <div className="lg:col-span-1">
            <div className="card sticky top-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">学习分类</h3>
              <div className="space-y-3">
                {Object.entries(categories).map(([key, category]) => (
                  <button
                    key={key}
                    onClick={() => setSelectedCategory(key)}
                    className={`w-full p-3 rounded-lg text-left transition-all ${
                      selectedCategory === key
                        ? 'bg-primary-100 border-primary-300 border-2'
                        : 'bg-gray-50 hover:bg-gray-100 border-2 border-transparent'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{category.icon}</span>
                      <div>
                        <div className="font-medium text-gray-900">{category.title}</div>
                        <div className="text-sm text-gray-600">{category.questionCount} 道题目</div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* 分类详情 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 分类概览 */}
            <div className="card">
              <div className="flex items-center gap-4 mb-6">
                <div className="text-4xl">{currentCategory.icon}</div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900">{currentCategory.title}</h3>
                  <p className="text-gray-600">{currentCategory.description}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-lg font-bold text-blue-600">{currentCategory.questionCount}</div>
                  <div className="text-sm text-gray-600">题目数量</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-lg font-bold text-green-600">{currentCategory.completionRate}%</div>
                  <div className="text-sm text-gray-600">完成率</div>
                </div>
                <div className="text-center p-3 bg-yellow-50 rounded-lg">
                  <div className="text-lg font-bold text-yellow-600">{currentCategory.averageScore}%</div>
                  <div className="text-sm text-gray-600">平均分</div>
                </div>
                <div className="text-center p-3 bg-purple-50 rounded-lg">
                  <div className="text-lg font-bold text-purple-600">{currentCategory.difficulty}</div>
                  <div className="text-sm text-gray-600">难度等级</div>
                </div>
              </div>

              <Link 
                href={`/category-practice?category=${selectedCategory}`}
                className="btn-primary w-full flex items-center justify-center gap-2"
              >
                <Target className="w-5 h-5" />
                开始学习 {currentCategory.title}
              </Link>
            </div>

            {/* 学习路径 */}
            <div className="card">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">学习路径</h4>
              <div className="space-y-3">
                {currentCategory.learningPath.map((step, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <div className="flex-shrink-0 w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center text-primary-600 text-sm font-bold">
                      {index + 1}
                    </div>
                    <div className="text-gray-700">{step}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* 最新更新 */}
            <div className="card">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">最新更新</h4>
              <div className="space-y-3">
                {currentCategory.recentUpdates.map((update, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                    <TrendingUp className="w-5 h-5 text-blue-600" />
                    <span className="text-blue-800">{update}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 学习排行榜 */}
        <div className="card mt-8">
          <div className="flex items-center gap-2 mb-6">
            <Award className="w-6 h-6 text-yellow-500" />
            <h3 className="text-xl font-bold text-gray-900">分类学习排行榜</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {topLearners.map((learner, index) => (
              <div key={index} className="p-4 border border-gray-200 rounded-lg text-center">
                <div className="text-2xl mb-2">{learner.badge}</div>
                <div className="font-semibold text-gray-900">{learner.nickname}</div>
                <div className="text-sm text-gray-600 mb-2">{learner.category}</div>
                <div className="text-lg font-bold text-primary-600">{learner.score}分</div>
              </div>
            ))}
          </div>
        </div>

        {/* 学习建议 */}
        <div className="card mt-8 bg-gradient-to-r from-purple-500 to-indigo-600 text-white">
          <h3 className="text-xl font-bold mb-4">💡 个性化学习建议</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">新手推荐</h4>
              <ul className="text-purple-100 text-sm space-y-1">
                <li>• 从"求职防骗"开始，建立基础认知</li>
                <li>• 每天学习1-2个分类，循序渐进</li>
                <li>• 重点关注与自己相关的场景</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">进阶技巧</h4>
              <ul className="text-purple-100 text-sm space-y-1">
                <li>• 交叉学习多个分类，形成知识网络</li>
                <li>• 定期复习错题，巩固薄弱环节</li>
                <li>• 结合实际案例，提升实战能力</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
