'use client'

import { useState } from 'react'
import { Shield, ArrowLeft, Eye, EyeOff, Users, Lock, CheckCircle, AlertTriangle } from 'lucide-react'
import Link from 'next/link'

export default function AnonymousFeaturePage() {
  const [showDetails, setShowDetails] = useState(false)

  const privacyFeatures = [
    {
      title: '邀请码机制',
      description: '通过邀请码参与，无需提供真实身份信息',
      icon: '🔑',
      details: '每个邀请码由系统随机生成，不与任何个人信息关联，确保参与者身份完全匿名。'
    },
    {
      title: '昵称系统',
      description: '自定义昵称显示，保护真实姓名隐私',
      icon: '👤',
      details: '用户可以使用任意昵称参与游戏，系统不会要求验证真实姓名，充分保护个人隐私。'
    },
    {
      title: '本地存储',
      description: '数据仅保存在本地浏览器，不上传服务器',
      icon: '💾',
      details: '所有游戏数据、学习记录都保存在用户本地，随时可以清除，完全掌控自己的数据。'
    },
    {
      title: '无追踪设计',
      description: '不收集设备信息，不进行用户行为追踪',
      icon: '🚫',
      details: '系统设计遵循隐私优先原则，不收集IP地址、设备指纹等可识别信息。'
    }
  ]

  const anonymousStats = {
    totalUsers: 15420,
    activeToday: 892,
    averageScore: 78.5,
    privacyRating: 9.8
  }

  const testimonials = [
    {
      nickname: '防诈小卫士',
      content: '很喜欢这种匿名参与的方式，可以放心学习防诈骗知识，不用担心个人信息泄露。',
      score: 95,
      timeAgo: '2小时前'
    },
    {
      nickname: '谨慎学习者',
      content: '邀请码+昵称的设计很贴心，既能参与学习又保护隐私，这样的设计很人性化。',
      score: 88,
      timeAgo: '5小时前'
    },
    {
      nickname: '安全意识强',
      content: '数据本地存储让我很放心，随时可以清除，完全掌控自己的学习数据。',
      score: 92,
      timeAgo: '1天前'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-3">
            <Link href="/" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </Link>
            <Shield className="w-8 h-8 text-primary-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">匿名参与</h1>
              <p className="text-sm text-gray-600">保护隐私，安心学习</p>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 概述 */}
        <div className="card mb-8 bg-gradient-to-r from-primary-500 to-blue-600 text-white">
          <div className="flex items-center gap-4 mb-6">
            <div className="p-3 bg-white/20 rounded-lg">
              <Shield className="w-8 h-8" />
            </div>
            <div>
              <h2 className="text-2xl font-bold mb-2">隐私优先的学习体验</h2>
              <p className="text-primary-100">
                无需实名注册，通过邀请码+昵称的方式匿名参与，让你在保护隐私的同时安心学习防诈骗知识。
              </p>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{anonymousStats.totalUsers.toLocaleString()}</div>
              <div className="text-sm text-primary-200">匿名用户</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{anonymousStats.activeToday}</div>
              <div className="text-sm text-primary-200">今日活跃</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{anonymousStats.averageScore}%</div>
              <div className="text-sm text-primary-200">平均成绩</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{anonymousStats.privacyRating}/10</div>
              <div className="text-sm text-primary-200">隐私评分</div>
            </div>
          </div>
        </div>

        {/* 隐私保护特性 */}
        <div className="mb-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">隐私保护特性</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {privacyFeatures.map((feature, index) => (
              <div key={index} className="card hover:shadow-lg transition-shadow">
                <div className="flex items-start gap-4">
                  <div className="text-3xl">{feature.icon}</div>
                  <div className="flex-1">
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">{feature.title}</h4>
                    <p className="text-gray-600 mb-3">{feature.description}</p>
                    <p className="text-sm text-gray-500">{feature.details}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 匿名参与流程 */}
        <div className="card mb-8">
          <h3 className="text-xl font-bold text-gray-900 mb-6">匿名参与流程</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-primary-600 font-bold">1</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">获取邀请码</h4>
              <p className="text-sm text-gray-600">通过官方渠道获取邀请码</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-primary-600 font-bold">2</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">设置昵称</h4>
              <p className="text-sm text-gray-600">选择一个喜欢的昵称</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-primary-600 font-bold">3</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">开始学习</h4>
              <p className="text-sm text-gray-600">立即开始防诈骗学习</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-primary-600 font-bold">4</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">数据掌控</h4>
              <p className="text-sm text-gray-600">随时导出或清除数据</p>
            </div>
          </div>
        </div>

        {/* 用户反馈 */}
        <div className="card mb-8">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-gray-900">用户反馈</h3>
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="flex items-center gap-2 text-primary-600 hover:text-primary-700"
            >
              {showDetails ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              {showDetails ? '隐藏详情' : '显示详情'}
            </button>
          </div>

          <div className="space-y-4">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                      <Users className="w-4 h-4 text-primary-600" />
                    </div>
                    <span className="font-medium text-gray-900">{testimonial.nickname}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">{testimonial.timeAgo}</span>
                    <span className="px-2 py-1 bg-success-100 text-success-700 text-xs rounded-full">
                      {testimonial.score}分
                    </span>
                  </div>
                </div>
                <p className="text-gray-700">{testimonial.content}</p>
                {showDetails && (
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <span>✓ 已验证匿名用户</span>
                      <span>✓ 数据本地存储</span>
                      <span>✓ 隐私完全保护</span>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* 隐私承诺 */}
        <div className="card bg-green-50 border-green-200">
          <div className="flex items-start gap-3">
            <CheckCircle className="w-6 h-6 text-green-600 mt-1" />
            <div>
              <h3 className="text-lg font-bold text-green-900 mb-2">我们的隐私承诺</h3>
              <ul className="text-green-800 space-y-2">
                <li>• 绝不收集用户真实身份信息</li>
                <li>• 所有数据仅保存在用户本地设备</li>
                <li>• 不进行任何形式的用户行为追踪</li>
                <li>• 用户可随时完全删除所有数据</li>
                <li>• 开源透明，接受社区监督</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 行动按钮 */}
        <div className="text-center mt-8">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/demo-quiz" className="btn-primary">
              立即体验匿名学习
            </Link>
            <Link href="/login" className="btn-secondary">
              注册匿名账户
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
