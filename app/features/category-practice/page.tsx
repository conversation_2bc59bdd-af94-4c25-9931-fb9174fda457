'use client'

import { useState } from 'react'
import { Target, ArrowLeft, Play, BookOpen, Award, BarChart3, Clock, CheckCircle } from 'lucide-react'
import Link from 'next/link'

export default function CategoryPracticePage() {
  const [selectedDifficulty, setSelectedDifficulty] = useState<'beginner' | 'intermediate' | 'advanced'>('beginner')

  const practiceStats = {
    totalCategories: 7,
    completedCategories: 3,
    totalQuestions: 180,
    answeredQuestions: 85,
    averageAccuracy: 82.5,
    studyTime: 45
  }

  const categories = [
    {
      id: 'job-fraud',
      title: '求职防骗',
      icon: '💼',
      color: 'blue',
      description: '识别虚假招聘、培训费诈骗等求职陷阱',
      difficulty: {
        beginner: { questions: 15, completed: 15, accuracy: 90 },
        intermediate: { questions: 12, completed: 8, accuracy: 85 },
        advanced: { questions: 8, completed: 0, accuracy: 0 }
      },
      topics: ['虚假招聘识别', '培训费陷阱', '保证金诈骗', '网络兼职风险'],
      recentProgress: 85,
      estimatedTime: '25分钟',
      lastStudied: '2024-01-15'
    },
    {
      id: 'rental-scam',
      title: '租房安全',
      icon: '🏠',
      color: 'green',
      description: '防范假房东、押金诈骗等租房风险',
      difficulty: {
        beginner: { questions: 12, completed: 12, accuracy: 88 },
        intermediate: { questions: 10, completed: 5, accuracy: 80 },
        advanced: { questions: 6, completed: 0, accuracy: 0 }
      },
      topics: ['假房东识别', '押金安全', '租房合同', '中介陷阱'],
      recentProgress: 70,
      estimatedTime: '20分钟',
      lastStudied: '2024-01-14'
    },
    {
      id: 'finance-trap',
      title: '金融理财',
      icon: '💰',
      color: 'yellow',
      description: '识别网贷陷阱、投资诈骗等金融风险',
      difficulty: {
        beginner: { questions: 18, completed: 10, accuracy: 75 },
        intermediate: { questions: 15, completed: 0, accuracy: 0 },
        advanced: { questions: 12, completed: 0, accuracy: 0 }
      },
      topics: ['网贷陷阱', '投资诈骗', '理财产品', '利率计算'],
      recentProgress: 45,
      estimatedTime: '35分钟',
      lastStudied: '2024-01-13'
    },
    {
      id: 'education-scam',
      title: '教育培训',
      icon: '🎓',
      color: 'purple',
      description: '防范培训诈骗、学历造假等教育陷阱',
      difficulty: {
        beginner: { questions: 10, completed: 0, accuracy: 0 },
        intermediate: { questions: 8, completed: 0, accuracy: 0 },
        advanced: { questions: 5, completed: 0, accuracy: 0 }
      },
      topics: ['培训机构', '学历认证', '技能证书', '就业承诺'],
      recentProgress: 0,
      estimatedTime: '30分钟',
      lastStudied: null
    }
  ]

  const studyPlan = [
    {
      week: 1,
      title: '基础防诈骗认知',
      categories: ['求职防骗', '租房安全'],
      status: 'completed',
      progress: 100
    },
    {
      week: 2,
      title: '金融安全意识',
      categories: ['金融理财', '网络通信'],
      status: 'current',
      progress: 60
    },
    {
      week: 3,
      title: '高级防范技巧',
      categories: ['教育培训', '冒充权威'],
      status: 'upcoming',
      progress: 0
    },
    {
      week: 4,
      title: '综合实战演练',
      categories: ['兼职副业', '综合测试'],
      status: 'upcoming',
      progress: 0
    }
  ]

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'text-green-600 bg-green-100'
      case 'intermediate': return 'text-yellow-600 bg-yellow-100'
      case 'advanced': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getDifficultyLabel = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return '入门'
      case 'intermediate': return '进阶'
      case 'advanced': return '高级'
      default: return '未知'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-5 h-5 text-green-600" />
      case 'current': return <Play className="w-5 h-5 text-blue-600" />
      case 'upcoming': return <Clock className="w-5 h-5 text-gray-400" />
      default: return null
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 to-purple-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-3">
            <Link href="/" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </Link>
            <Target className="w-8 h-8 text-indigo-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">分类刷题</h1>
              <p className="text-sm text-gray-600">针对性强化，系统提升</p>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 学习统计 */}
        <div className="card mb-8 bg-gradient-to-r from-indigo-500 to-purple-600 text-white">
          <h2 className="text-2xl font-bold mb-6">学习进度统计</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{practiceStats.completedCategories}/{practiceStats.totalCategories}</div>
              <div className="text-sm text-indigo-200">完成分类</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{practiceStats.answeredQuestions}/{practiceStats.totalQuestions}</div>
              <div className="text-sm text-indigo-200">答题进度</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{practiceStats.averageAccuracy}%</div>
              <div className="text-sm text-indigo-200">平均正确率</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{practiceStats.studyTime}h</div>
              <div className="text-sm text-indigo-200">学习时长</div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 分类列表 */}
          <div className="lg:col-span-2">
            <div className="card mb-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900">练习分类</h3>
                <div className="flex gap-2">
                  {['beginner', 'intermediate', 'advanced'].map((difficulty) => (
                    <button
                      key={difficulty}
                      onClick={() => setSelectedDifficulty(difficulty as any)}
                      className={`px-3 py-1 rounded-lg text-sm transition-all ${
                        selectedDifficulty === difficulty
                          ? getDifficultyColor(difficulty)
                          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }`}
                    >
                      {getDifficultyLabel(difficulty)}
                    </button>
                  ))}
                </div>
              </div>

              <div className="space-y-4">
                {categories.map((category) => {
                  const difficultyData = category.difficulty[selectedDifficulty]
                  const completionRate = difficultyData.questions > 0 
                    ? (difficultyData.completed / difficultyData.questions) * 100 
                    : 0

                  return (
                    <div key={category.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex items-start gap-4">
                        <div className="text-3xl">{category.icon}</div>
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h4 className="text-lg font-semibold text-gray-900">{category.title}</h4>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(selectedDifficulty)}`}>
                              {getDifficultyLabel(selectedDifficulty)}
                            </span>
                          </div>
                          <p className="text-gray-600 mb-3">{category.description}</p>
                          
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                            <div className="text-center p-2 bg-blue-50 rounded">
                              <div className="font-bold text-blue-600">{difficultyData.questions}</div>
                              <div className="text-xs text-gray-600">题目数</div>
                            </div>
                            <div className="text-center p-2 bg-green-50 rounded">
                              <div className="font-bold text-green-600">{difficultyData.completed}</div>
                              <div className="text-xs text-gray-600">已完成</div>
                            </div>
                            <div className="text-center p-2 bg-yellow-50 rounded">
                              <div className="font-bold text-yellow-600">{difficultyData.accuracy}%</div>
                              <div className="text-xs text-gray-600">正确率</div>
                            </div>
                            <div className="text-center p-2 bg-purple-50 rounded">
                              <div className="font-bold text-purple-600">{category.estimatedTime}</div>
                              <div className="text-xs text-gray-600">预计时长</div>
                            </div>
                          </div>

                          <div className="flex flex-wrap gap-2 mb-3">
                            {category.topics.map((topic, index) => (
                              <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                                {topic}
                              </span>
                            ))}
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex-1 mr-4">
                              <div className="flex items-center justify-between text-sm mb-1">
                                <span>完成进度</span>
                                <span>{completionRate.toFixed(0)}%</span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <div 
                                  className="bg-primary-600 h-2 rounded-full transition-all"
                                  style={{ width: `${completionRate}%` }}
                                ></div>
                              </div>
                            </div>
                            <Link 
                              href={`/category-quiz?category=${category.id}&difficulty=${selectedDifficulty}`}
                              className="btn-primary flex items-center gap-2"
                            >
                              <Play className="w-4 h-4" />
                              {difficultyData.completed === 0 ? '开始练习' : '继续练习'}
                            </Link>
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>

          {/* 侧边栏 */}
          <div className="space-y-6">
            {/* 学习计划 */}
            <div className="card">
              <h3 className="text-lg font-bold text-gray-900 mb-4">学习计划</h3>
              <div className="space-y-3">
                {studyPlan.map((plan) => (
                  <div key={plan.week} className="border border-gray-200 rounded-lg p-3">
                    <div className="flex items-center gap-2 mb-2">
                      {getStatusIcon(plan.status)}
                      <span className="font-medium text-gray-900">第{plan.week}周</span>
                    </div>
                    <div className="text-sm text-gray-900 mb-2">{plan.title}</div>
                    <div className="text-xs text-gray-600 mb-2">
                      {plan.categories.join(' • ')}
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <div 
                        className="bg-primary-600 h-1.5 rounded-full transition-all"
                        style={{ width: `${plan.progress}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 学习建议 */}
            <div className="card bg-gradient-to-r from-green-500 to-blue-600 text-white">
              <h3 className="text-lg font-bold mb-4">💡 学习建议</h3>
              <div className="space-y-3 text-sm">
                <div className="flex items-start gap-2">
                  <span>📚</span>
                  <span>建议从入门难度开始，循序渐进</span>
                </div>
                <div className="flex items-start gap-2">
                  <span>🎯</span>
                  <span>专注完成一个分类再进入下一个</span>
                </div>
                <div className="flex items-start gap-2">
                  <span>⏰</span>
                  <span>每天练习20-30分钟效果最佳</span>
                </div>
                <div className="flex items-start gap-2">
                  <span>🔄</span>
                  <span>定期复习错题，巩固知识点</span>
                </div>
              </div>
            </div>

            {/* 成就展示 */}
            <div className="card">
              <h3 className="text-lg font-bold text-gray-900 mb-4">学习成就</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-2 bg-yellow-50 rounded-lg">
                  <span className="text-xl">🏆</span>
                  <div>
                    <div className="font-medium text-gray-900">求职防骗专家</div>
                    <div className="text-xs text-gray-600">完成求职防骗所有难度</div>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-2 bg-green-50 rounded-lg">
                  <span className="text-xl">🎯</span>
                  <div>
                    <div className="font-medium text-gray-900">精准射手</div>
                    <div className="text-xs text-gray-600">单次练习正确率90%+</div>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-2 bg-blue-50 rounded-lg">
                  <span className="text-xl">📚</span>
                  <div>
                    <div className="font-medium text-gray-900">勤奋学者</div>
                    <div className="text-xs text-gray-600">连续学习7天</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
