'use client'

import { useState, useEffect } from 'react'
import { User, ArrowLeft, LogOut, Trash2, Download, Calendar, Trophy, Target } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

export default function ProfilePage() {
  const router = useRouter()
  const [userData, setUserData] = useState<any>(null)
  const [starterResult, setStarterResult] = useState<any>(null)
  const [challengeHistory, setChallengeHistory] = useState<any[]>([])

  useEffect(() => {
    // 检查用户登录状态
    const userDataStr = localStorage.getItem('userData')
    if (!userDataStr) {
      router.push('/login')
      return
    }
    setUserData(JSON.parse(userDataStr))

    // 获取入门题结果
    const starterResultStr = localStorage.getItem('starterResult')
    if (starterResultStr) {
      setStarterResult(JSON.parse(starterResultStr))
    }

    // 获取挑战历史（模拟数据）
    const lastResult = localStorage.getItem('lastChallengeResult')
    if (lastResult) {
      setChallengeHistory([JSON.parse(lastResult)])
    }
  }, [router])

  const handleLogout = () => {
    if (confirm('确定要退出登录吗？')) {
      localStorage.clear()
      router.push('/')
    }
  }

  const handleClearData = () => {
    if (confirm('确定要清除所有数据吗？此操作不可恢复！')) {
      localStorage.clear()
      router.push('/')
    }
  }

  const exportData = () => {
    const data = {
      userData,
      starterResult,
      challengeHistory,
      exportDate: new Date().toISOString()
    }
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `防诈挑战数据-${userData?.nickname}-${new Date().toLocaleDateString()}.json`
    link.click()
    URL.revokeObjectURL(url)
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (!userData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Link href="/dashboard" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </Link>
              <User className="w-8 h-8 text-primary-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">个人中心</h1>
                <p className="text-sm text-gray-600">管理你的账户和数据</p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              className="btn-danger flex items-center gap-2"
            >
              <LogOut className="w-4 h-4" />
              退出登录
            </button>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 用户信息卡片 */}
        <div className="card mb-8">
          <div className="flex items-center gap-4 mb-6">
            <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center">
              <User className="w-8 h-8 text-primary-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">{userData.nickname}</h2>
              <p className="text-gray-600">邀请码: {userData.inviteCode}</p>
              <p className="text-sm text-gray-500">
                注册时间: {new Date(userData.createdAt).toLocaleDateString()}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-primary-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-primary-600">{userData.contributionScore || 0}</div>
              <div className="text-sm text-gray-600">贡献值</div>
            </div>
            <div className="bg-success-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-success-600">
                {starterResult ? `${starterResult.score}/${starterResult.total}` : '未完成'}
              </div>
              <div className="text-sm text-gray-600">入门题成绩</div>
            </div>
            <div className="bg-blue-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">{challengeHistory.length}</div>
              <div className="text-sm text-gray-600">挑战次数</div>
            </div>
          </div>
        </div>

        {/* 入门题结果 */}
        {starterResult && (
          <div className="card mb-8">
            <div className="flex items-center gap-2 mb-4">
              <Target className="w-6 h-6 text-success-600" />
              <h3 className="text-xl font-semibold text-gray-900">入门挑战成绩</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-lg font-bold text-success-600">{starterResult.score}</div>
                <div className="text-sm text-gray-600">正确题数</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-blue-600">
                  {Math.round((starterResult.score / starterResult.total) * 100)}%
                </div>
                <div className="text-sm text-gray-600">正确率</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-purple-600">{formatTime(starterResult.timeUsed)}</div>
                <div className="text-sm text-gray-600">用时</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-gray-600">
                  {new Date(starterResult.completedAt).toLocaleDateString()}
                </div>
                <div className="text-sm text-gray-600">完成日期</div>
              </div>
            </div>
          </div>
        )}

        {/* 挑战历史 */}
        <div className="card mb-8">
          <div className="flex items-center gap-2 mb-4">
            <Trophy className="w-6 h-6 text-yellow-500" />
            <h3 className="text-xl font-semibold text-gray-900">挑战历史</h3>
          </div>
          
          {challengeHistory.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">还没有挑战记录</p>
              <Link href="/challenge" className="btn-primary mt-4">
                开始挑战
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {challengeHistory.map((challenge, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-semibold text-gray-900">
                        每日挑战 #{index + 1}
                      </div>
                      <div className="text-sm text-gray-600">
                        {new Date(challenge.date).toLocaleDateString()}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-primary-600">
                        {challenge.score} 题
                      </div>
                      <div className="text-sm text-gray-600">
                        {formatTime(challenge.timeUsed)}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 数据管理 */}
        <div className="card">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">数据管理</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div>
                <div className="font-medium text-blue-900">导出数据</div>
                <div className="text-sm text-blue-700">下载你的所有游戏数据</div>
              </div>
              <button
                onClick={exportData}
                className="btn-primary flex items-center gap-2"
              >
                <Download className="w-4 h-4" />
                导出
              </button>
            </div>

            <div className="flex items-center justify-between p-4 bg-red-50 border border-red-200 rounded-lg">
              <div>
                <div className="font-medium text-red-900">清除所有数据</div>
                <div className="text-sm text-red-700">删除所有本地存储的数据，此操作不可恢复</div>
              </div>
              <button
                onClick={handleClearData}
                className="btn-danger flex items-center gap-2"
              >
                <Trash2 className="w-4 h-4" />
                清除
              </button>
            </div>
          </div>
        </div>

        {/* 隐私说明 */}
        <div className="mt-8 p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <h4 className="font-semibold text-gray-900 mb-2">隐私保护说明</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• 所有数据仅保存在你的浏览器本地存储中</li>
            <li>• 我们不会收集或上传你的个人信息</li>
            <li>• 你可以随时导出或删除自己的数据</li>
            <li>• 清除浏览器数据会同时删除游戏记录</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
