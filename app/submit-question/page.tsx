'use client'

import { useState, useEffect } from 'react'
import { Plus, ArrowLeft, AlertTriangle, CheckCircle, Clock } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { CATEGORY_LABELS, QuestionCategory } from '@/types'

interface QuestionSubmission {
  title: string
  options: string[]
  correctAnswer: number
  category: QuestionCategory
  explanation: string
  source: string
  optionExplanations: string[]
}

export default function SubmitQuestionPage() {
  const router = useRouter()
  const [userData, setUserData] = useState<any>(null)
  const [formData, setFormData] = useState<QuestionSubmission>({
    title: '',
    options: ['', '', '', ''],
    correctAnswer: 0,
    category: 'job-fraud',
    explanation: '',
    source: '',
    optionExplanations: ['', '', '', '']
  })
  const [errors, setErrors] = useState<{[key: string]: string}>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  useEffect(() => {
    // 检查用户登录状态（Demo 模式允许未登录用户体验）
    const userDataStr = localStorage.getItem('userData')
    if (userDataStr) {
      setUserData(JSON.parse(userDataStr))
    } else {
      // 为未登录用户创建临时身份
      setUserData({
        id: 'demo_user',
        nickname: '体验用户',
        contributionScore: 0
      })
    }
  }, [router])

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const handleOptionChange = (index: number, value: string) => {
    const newOptions = [...formData.options]
    newOptions[index] = value
    setFormData(prev => ({
      ...prev,
      options: newOptions
    }))
    if (errors[`option${index}`]) {
      setErrors(prev => ({
        ...prev,
        [`option${index}`]: ''
      }))
    }
  }

  const handleOptionExplanationChange = (index: number, value: string) => {
    const newExplanations = [...formData.optionExplanations]
    newExplanations[index] = value
    setFormData(prev => ({
      ...prev,
      optionExplanations: newExplanations
    }))
  }

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {}

    if (!formData.title.trim()) {
      newErrors.title = '请输入题目内容'
    } else if (formData.title.length < 10) {
      newErrors.title = '题目内容至少需要10个字符'
    }

    formData.options.forEach((option, index) => {
      if (!option.trim()) {
        newErrors[`option${index}`] = `请输入选项${String.fromCharCode(65 + index)}的内容`
      }
    })

    if (!formData.explanation.trim()) {
      newErrors.explanation = '请输入详细解析'
    } else if (formData.explanation.length < 20) {
      newErrors.explanation = '详细解析至少需要20个字符'
    }

    if (!formData.source.trim()) {
      newErrors.source = '请输入案例来源'
    }

    formData.optionExplanations.forEach((explanation, index) => {
      if (!explanation.trim()) {
        newErrors[`optionExplanation${index}`] = `请输入选项${String.fromCharCode(65 + index)}的解析`
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 保存提交记录
      const submission = {
        ...formData,
        id: `submission_${Date.now()}`,
        authorId: userData.id,
        authorNickname: userData.nickname,
        status: 'pending',
        submittedAt: new Date().toISOString()
      }
      
      const existingSubmissions = JSON.parse(localStorage.getItem('questionSubmissions') || '[]')
      existingSubmissions.push(submission)
      localStorage.setItem('questionSubmissions', JSON.stringify(existingSubmissions))
      
      // 增加用户贡献值
      const updatedUserData = {
        ...userData,
        contributionScore: (userData.contributionScore || 0) + 1
      }
      localStorage.setItem('userData', JSON.stringify(updatedUserData))
      setUserData(updatedUserData)
      
      setIsSubmitted(true)
    } catch (error) {
      setErrors({ general: '提交失败，请重试' })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!userData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (isSubmitted) {
    return (
      <div className="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full">
          <div className="card text-center">
            <CheckCircle className="w-16 h-16 text-success-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">提交成功！</h1>
            <p className="text-gray-600 mb-6">
              感谢你的贡献！你的题目已提交审核，通过后将加入题库供其他用户挑战。
            </p>
            <div className="bg-success-50 border border-success-200 rounded-lg p-4 mb-6">
              <p className="text-success-800 text-sm">
                🎉 你获得了 1 贡献值！当前贡献值：{userData.contributionScore}
              </p>
            </div>
            <div className="flex flex-col gap-3">
              <Link href="/dashboard" className="btn-primary">
                返回主页
              </Link>
              <button
                onClick={() => {
                  setIsSubmitted(false)
                  setFormData({
                    title: '',
                    options: ['', '', '', ''],
                    correctAnswer: 0,
                    category: 'job-fraud',
                    explanation: '',
                    source: '',
                    optionExplanations: ['', '', '', '']
                  })
                }}
                className="btn-secondary"
              >
                继续提交题目
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-3">
            <Link href="/dashboard" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </Link>
            <Plus className="w-8 h-8 text-success-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">提交题目</h1>
              <p className="text-sm text-gray-600">分享真实案例，帮助更多人防范诈骗</p>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 提示信息 */}
        <div className="card mb-8 bg-blue-50 border border-blue-200">
          <div className="flex items-start gap-3">
            <AlertTriangle className="w-6 h-6 text-blue-600 mt-1" />
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">提交须知</h3>
              <ul className="text-blue-800 text-sm space-y-1">
                <li>• 请基于真实案例或常见诈骗手段出题</li>
                <li>• 题目内容应具有教育意义，帮助他人识别诈骗</li>
                <li>• 所有选项都需要提供详细解析说明</li>
                <li>• 提交后将进入审核流程，通过后加入正式题库</li>
                <li>• 成功贡献题目可获得贡献值奖励</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 提交表单 */}
        <form onSubmit={handleSubmit} className="space-y-8">
          {errors.general && (
            <div className="bg-danger-50 border border-danger-200 rounded-lg p-4">
              <div className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5 text-danger-600" />
                <span className="text-danger-800">{errors.general}</span>
              </div>
            </div>
          )}

          {/* 基本信息 */}
          <div className="card">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">基本信息</h3>
            
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  诈骗类型 *
                </label>
                <select
                  value={formData.category}
                  onChange={(e) => handleInputChange('category', e.target.value)}
                  className="input-field"
                >
                  {Object.entries(CATEGORY_LABELS).map(([key, label]) => (
                    <option key={key} value={key}>{label}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  题目内容 *
                </label>
                <textarea
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  className={`input-field h-24 ${errors.title ? 'border-danger-300' : ''}`}
                  placeholder="请描述一个具体的诈骗场景，让用户判断应该如何应对..."
                />
                {errors.title && (
                  <p className="mt-1 text-sm text-danger-600">{errors.title}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  案例来源 *
                </label>
                <input
                  type="text"
                  value={formData.source}
                  onChange={(e) => handleInputChange('source', e.target.value)}
                  className={`input-field ${errors.source ? 'border-danger-300' : ''}`}
                  placeholder="例如：亲身经历、新闻报道、朋友遭遇等"
                />
                {errors.source && (
                  <p className="mt-1 text-sm text-danger-600">{errors.source}</p>
                )}
              </div>
            </div>
          </div>

          {/* 选项设置 */}
          <div className="card">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">答案选项</h3>
            
            <div className="space-y-6">
              {formData.options.map((option, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                      <span className="text-primary-600 font-semibold">
                        {String.fromCharCode(65 + index)}
                      </span>
                    </div>
                    <input
                      type="radio"
                      name="correctAnswer"
                      checked={formData.correctAnswer === index}
                      onChange={() => handleInputChange('correctAnswer', index)}
                      className="text-primary-600"
                    />
                    <label className="text-sm font-medium text-gray-700">
                      正确答案
                    </label>
                  </div>
                  
                  <div className="space-y-3">
                    <div>
                      <input
                        type="text"
                        value={option}
                        onChange={(e) => handleOptionChange(index, e.target.value)}
                        className={`input-field ${errors[`option${index}`] ? 'border-danger-300' : ''}`}
                        placeholder={`选项${String.fromCharCode(65 + index)}内容`}
                      />
                      {errors[`option${index}`] && (
                        <p className="mt-1 text-sm text-danger-600">{errors[`option${index}`]}</p>
                      )}
                    </div>
                    
                    <div>
                      <textarea
                        value={formData.optionExplanations[index]}
                        onChange={(e) => handleOptionExplanationChange(index, e.target.value)}
                        className={`input-field h-20 ${errors[`optionExplanation${index}`] ? 'border-danger-300' : ''}`}
                        placeholder={`选项${String.fromCharCode(65 + index)}的解析说明`}
                      />
                      {errors[`optionExplanation${index}`] && (
                        <p className="mt-1 text-sm text-danger-600">{errors[`optionExplanation${index}`]}</p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 详细解析 */}
          <div className="card">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">详细解析</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                题目解析 *
              </label>
              <textarea
                value={formData.explanation}
                onChange={(e) => handleInputChange('explanation', e.target.value)}
                className={`input-field h-32 ${errors.explanation ? 'border-danger-300' : ''}`}
                placeholder="请详细解释为什么这样选择是正确的，以及这类诈骗的特点和防范方法..."
              />
              {errors.explanation && (
                <p className="mt-1 text-sm text-danger-600">{errors.explanation}</p>
              )}
            </div>
          </div>

          {/* 提交按钮 */}
          <div className="flex justify-between">
            <Link href="/dashboard" className="btn-secondary">
              取消
            </Link>
            <button
              type="submit"
              disabled={isSubmitting}
              className="btn-success flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <Clock className="w-5 h-5 animate-spin" />
                  提交中...
                </>
              ) : (
                <>
                  <Plus className="w-5 h-5" />
                  提交题目
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
