'use client'

import { useState, useEffect } from 'react'
import { Target, Clock, ArrowLeft, Zap, AlertTriangle, CheckCircle, XCircle } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { CATEGORY_LABELS, QuestionCategory } from '@/types'
import { STARTER_QUESTIONS } from '@/data/questions'

export default function ChallengePage() {
  const router = useRouter()
  const [selectedCategory, setSelectedCategory] = useState<QuestionCategory | null>(null)
  const [isStarted, setIsStarted] = useState(false)
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [score, setScore] = useState(0)
  const [timeUsed, setTimeUsed] = useState(0)
  const [startTime, setStartTime] = useState<number | null>(null)
  const [isFinished, setIsFinished] = useState(false)
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null)
  const [showResult, setShowResult] = useState(false)

  // 使用入门题作为挑战题库（实际项目中会从API获取）
  const challengeQuestions = STARTER_QUESTIONS.filter(q => 
    !selectedCategory || q.category === selectedCategory
  )

  const currentQuestion = challengeQuestions[currentQuestionIndex]

  useEffect(() => {
    // 检查用户登录状态
    const userData = localStorage.getItem('userData')
    if (!userData) {
      router.push('/login')
      return
    }

    // 检查今日是否已挑战
    const lastChallengeDate = localStorage.getItem('lastChallengeDate')
    const today = new Date().toDateString()
    
    if (lastChallengeDate === today) {
      alert('今日挑战已完成，请明天再来！')
      router.push('/dashboard')
      return
    }

    let timer: NodeJS.Timeout
    if (isStarted && !isFinished) {
      timer = setInterval(() => {
        if (startTime) {
          setTimeUsed(Math.floor((Date.now() - startTime) / 1000))
        }
      }, 1000)
    }

    return () => {
      if (timer) clearInterval(timer)
    }
  }, [isStarted, isFinished, startTime, router])

  const handleStartChallenge = () => {
    if (!selectedCategory) {
      alert('请选择一个挑战类型')
      return
    }
    setIsStarted(true)
    setStartTime(Date.now())
  }

  const handleAnswerSelect = (answerIndex: number) => {
    setSelectedAnswer(answerIndex)
  }

  const handleSubmitAnswer = () => {
    if (selectedAnswer === null) {
      alert('请选择一个答案')
      return
    }

    const isCorrect = selectedAnswer === currentQuestion.correctAnswer
    setShowResult(true)

    if (isCorrect) {
      setScore(score + 1)
      // 继续下一题
      setTimeout(() => {
        if (currentQuestionIndex < challengeQuestions.length - 1) {
          setCurrentQuestionIndex(currentQuestionIndex + 1)
          setSelectedAnswer(null)
          setShowResult(false)
        } else {
          // 所有题目完成
          finishChallenge()
        }
      }, 2000)
    } else {
      // 答错了，挑战结束
      setTimeout(() => {
        finishChallenge()
      }, 3000)
    }
  }

  const finishChallenge = () => {
    setIsFinished(true)
    
    // 保存挑战结果
    const result = {
      score,
      timeUsed,
      category: selectedCategory,
      date: new Date().toISOString(),
      questionsAnswered: currentQuestionIndex + 1
    }
    
    localStorage.setItem('lastChallengeDate', new Date().toDateString())
    localStorage.setItem('lastChallengeResult', JSON.stringify(result))
    
    // 更新用户贡献值
    const userData = JSON.parse(localStorage.getItem('userData') || '{}')
    userData.contributionScore = (userData.contributionScore || 0) + score
    localStorage.setItem('userData', JSON.stringify(userData))
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (isFinished) {
    return (
      <div className="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div className="max-w-2xl w-full">
          <div className="card text-center">
            <div className="mb-6">
              {score > 0 ? (
                <CheckCircle className="w-16 h-16 text-success-500 mx-auto mb-4" />
              ) : (
                <XCircle className="w-16 h-16 text-danger-500 mx-auto mb-4" />
              )}
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                挑战完成！
              </h1>
              <p className="text-gray-600">
                {score > 0 ? `恭喜你答对了 ${score} 道题！` : '很遗憾，第一题就答错了，继续加油！'}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-primary-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-primary-600">{score}</div>
                <div className="text-sm text-gray-600">正确题数</div>
              </div>
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-blue-600">{formatTime(timeUsed)}</div>
                <div className="text-sm text-gray-600">用时</div>
              </div>
              <div className="bg-purple-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-purple-600">
                  {selectedCategory ? CATEGORY_LABELS[selectedCategory] : '全部'}
                </div>
                <div className="text-sm text-gray-600">挑战类型</div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/dashboard" className="btn-primary">
                返回主页
              </Link>
              <Link href="/leaderboard" className="btn-secondary">
                查看排行榜
              </Link>
              <Link href="/share-score" className="btn-success">
                分享成绩
              </Link>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!isStarted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center gap-3">
              <Link href="/dashboard" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </Link>
              <Target className="w-8 h-8 text-primary-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">每日挑战</h1>
                <p className="text-sm text-gray-600">选择类型开始挑战</p>
              </div>
            </div>
          </div>
        </header>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="card mb-8">
            <div className="text-center mb-8">
              <Zap className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 mb-2">准备开始挑战</h2>
              <p className="text-gray-600">
                选择一个诈骗类型，持续答题直到答错。每天只能挑战一次，加油！
              </p>
            </div>

            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">选择挑战类型：</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <button
                  onClick={() => setSelectedCategory(null)}
                  className={`p-4 border-2 rounded-lg text-left transition-all ${
                    selectedCategory === null
                      ? 'border-primary-500 bg-primary-50'
                      : 'border-gray-200 hover:border-primary-300'
                  }`}
                >
                  <div className="font-semibold text-gray-900">全部类型</div>
                  <div className="text-sm text-gray-600">混合挑战所有诈骗类型</div>
                </button>
                
                {Object.entries(CATEGORY_LABELS).map(([key, label]) => (
                  <button
                    key={key}
                    onClick={() => setSelectedCategory(key as QuestionCategory)}
                    className={`p-4 border-2 rounded-lg text-left transition-all ${
                      selectedCategory === key
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-gray-200 hover:border-primary-300'
                    }`}
                  >
                    <div className="font-semibold text-gray-900">{label}</div>
                    <div className="text-sm text-gray-600">专项挑战</div>
                  </button>
                ))}
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <div className="flex items-start gap-3">
                <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                <div className="text-sm text-yellow-800">
                  <p className="font-medium mb-1">挑战规则：</p>
                  <ul className="space-y-1 text-yellow-700">
                    <li>• 持续答题直到答错或完成所有题目</li>
                    <li>• 每天只能参与一次挑战</li>
                    <li>• 答题数量和用时将计入排行榜</li>
                    <li>• 完成挑战可获得贡献值奖励</li>
                  </ul>
                </div>
              </div>
            </div>

            <button
              onClick={handleStartChallenge}
              disabled={selectedCategory === undefined}
              className="w-full btn-primary text-lg py-4 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              开始挑战
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="card mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Target className="w-8 h-8 text-primary-600" />
              <div>
                <h1 className="text-xl font-bold text-gray-900">每日挑战进行中</h1>
                <p className="text-sm text-gray-600">
                  {selectedCategory ? CATEGORY_LABELS[selectedCategory] : '全部类型'}
                </p>
              </div>
            </div>
            <div className="text-right">
              <div className="flex items-center gap-4">
                <div className="text-center">
                  <div className="text-lg font-bold text-primary-600">{score}</div>
                  <div className="text-xs text-gray-500">正确</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">{formatTime(timeUsed)}</div>
                  <div className="text-xs text-gray-500">用时</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Question */}
        <div className="card mb-8">
          <div className="mb-4">
            <div className="text-sm text-gray-600 mb-2">
              第 {currentQuestionIndex + 1} 题 • {CATEGORY_LABELS[currentQuestion.category]}
            </div>
            <h2 className="text-xl font-semibold text-gray-900">
              {currentQuestion.title}
            </h2>
          </div>

          <div className="space-y-3">
            {currentQuestion.options.map((option, index) => (
              <button
                key={index}
                onClick={() => handleAnswerSelect(index)}
                disabled={showResult}
                className={`quiz-option ${
                  selectedAnswer === index ? 'selected' : ''
                } ${
                  showResult
                    ? index === currentQuestion.correctAnswer
                      ? 'correct'
                      : selectedAnswer === index
                      ? 'incorrect'
                      : ''
                    : ''
                }`}
              >
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full border-2 border-current flex items-center justify-center text-sm font-medium">
                    {String.fromCharCode(65 + index)}
                  </div>
                  <div className="text-left">
                    <div className="font-medium">{option.text}</div>
                    {showResult && (
                      <div className="mt-2 text-sm opacity-80">
                        {option.explanation}
                      </div>
                    )}
                  </div>
                </div>
              </button>
            ))}
          </div>

          {showResult && (
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">详细解析：</h4>
              <p className="text-blue-800 text-sm">{currentQuestion.explanation}</p>
            </div>
          )}
        </div>

        {/* Actions */}
        {!showResult && (
          <div className="flex justify-between">
            <button
              onClick={() => {
                if (confirm('确定要退出挑战吗？今日将无法再次参与。')) {
                  router.push('/dashboard')
                }
              }}
              className="btn-secondary"
            >
              退出挑战
            </button>
            
            <button
              onClick={handleSubmitAnswer}
              disabled={selectedAnswer === null}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              提交答案
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
