import Link from 'next/link'
import { AlertTriangle, Home, ArrowLeft } from 'lucide-react'

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full text-center">
        <div className="card">
          <div className="mb-6">
            <AlertTriangle className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
            <h1 className="text-6xl font-bold text-gray-900 mb-2">404</h1>
            <h2 className="text-2xl font-semibold text-gray-700 mb-4">页面未找到</h2>
            <p className="text-gray-600">
              抱歉，你访问的页面不存在。可能是链接错误或页面已被移除。
            </p>
          </div>

          <div className="space-y-4">
            <Link href="/" className="btn-primary w-full flex items-center justify-center gap-2">
              <Home className="w-5 h-5" />
              返回首页
            </Link>
            <button 
              onClick={() => window.history.back()} 
              className="btn-secondary w-full flex items-center justify-center gap-2"
            >
              <ArrowLeft className="w-5 h-5" />
              返回上一页
            </button>
          </div>

          <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-blue-800 text-sm">
              💡 <strong>提示：</strong>如果你是通过链接访问的，请检查链接是否正确。
              如果问题持续存在，请联系我们。
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
