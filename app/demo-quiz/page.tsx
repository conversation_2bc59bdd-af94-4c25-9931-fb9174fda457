'use client'

import { useState, useEffect } from 'react'
import { Clock, CheckCircle, XCircle, ArrowRight, BookOpen, AlertTriangle, Share2 } from 'lucide-react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { STARTER_QUESTIONS } from '@/data/questions'

export default function DemoQuizPage() {
  const router = useRouter()
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [selectedAnswers, setSelectedAnswers] = useState<number[]>([])
  const [showResult, setShowResult] = useState(false)
  const [timeUsed, setTimeUsed] = useState(0)
  const [startTime] = useState(Date.now())
  const [showExplanation, setShowExplanation] = useState(false)

  const currentQuestion = STARTER_QUESTIONS[currentQuestionIndex]
  const isLastQuestion = currentQuestionIndex === STARTER_QUESTIONS.length - 1
  const correctAnswers = selectedAnswers.filter((answer, index) => 
    answer === STARTER_QUESTIONS[index].correctAnswer
  ).length

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeUsed(Math.floor((Date.now() - startTime) / 1000))
    }, 1000)

    return () => clearInterval(timer)
  }, [startTime])

  const handleAnswerSelect = (answerIndex: number) => {
    const newAnswers = [...selectedAnswers]
    newAnswers[currentQuestionIndex] = answerIndex
    setSelectedAnswers(newAnswers)
  }

  const handleNext = () => {
    if (selectedAnswers[currentQuestionIndex] === undefined) {
      alert('请选择一个答案')
      return
    }

    if (isLastQuestion) {
      setShowResult(true)
      // 保存体验结果
      const result = {
        score: correctAnswers + (selectedAnswers[currentQuestionIndex] === currentQuestion.correctAnswer ? 1 : 0),
        total: STARTER_QUESTIONS.length,
        timeUsed,
        completedAt: new Date().toISOString(),
        isDemo: true
      }
      localStorage.setItem('demoResult', JSON.stringify(result))
    } else {
      setCurrentQuestionIndex(currentQuestionIndex + 1)
      setShowExplanation(false)
    }
  }

  const handleShowExplanation = () => {
    setShowExplanation(true)
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (showResult) {
    const finalScore = correctAnswers + (selectedAnswers[currentQuestionIndex] === currentQuestion.correctAnswer ? 1 : 0)
    const percentage = Math.round((finalScore / STARTER_QUESTIONS.length) * 100)
    
    return (
      <div className="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="max-w-2xl w-full">
          <div className="card text-center">
            <div className="mb-6">
              {percentage >= 80 ? (
                <CheckCircle className="w-16 h-16 text-success-500 mx-auto mb-4" />
              ) : percentage >= 60 ? (
                <AlertTriangle className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
              ) : (
                <XCircle className="w-16 h-16 text-danger-500 mx-auto mb-4" />
              )}
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                体验完成！
              </h1>
              <p className="text-gray-600">
                感谢体验防诈骗知识测试
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-primary-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-primary-600">{finalScore}/{STARTER_QUESTIONS.length}</div>
                <div className="text-sm text-gray-600">正确题数</div>
              </div>
              <div className="bg-success-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-success-600">{percentage}%</div>
                <div className="text-sm text-gray-600">正确率</div>
              </div>
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-blue-600">{formatTime(timeUsed)}</div>
                <div className="text-sm text-gray-600">用时</div>
              </div>
            </div>

            <div className="mb-8">
              {percentage >= 80 ? (
                <div className="bg-success-50 border border-success-200 rounded-lg p-4">
                  <p className="text-success-800 font-medium">
                    🎉 优秀！你已经具备了良好的防诈骗意识！
                  </p>
                </div>
              ) : percentage >= 60 ? (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <p className="text-yellow-800 font-medium">
                    ⚠️ 不错！你的防诈骗知识还需要加强。
                  </p>
                </div>
              ) : (
                <div className="bg-danger-50 border border-danger-200 rounded-lg p-4">
                  <p className="text-danger-800 font-medium">
                    ⚠️ 需要提高！建议认真学习防诈骗知识。
                  </p>
                </div>
              )}
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <h3 className="font-semibold text-blue-900 mb-2">想要更多功能？</h3>
              <p className="text-blue-800 text-sm mb-3">
                注册完整账户，解锁每日挑战、排行榜、社区交流等更多功能！
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/login" className="btn-primary flex items-center gap-2">
                注册完整体验
                <ArrowRight className="w-5 h-5" />
              </Link>
              <Link href="/demo-share" className="btn-secondary flex items-center gap-2">
                分享成绩
                <Share2 className="w-5 h-5" />
              </Link>
              <Link href="/" className="btn-secondary">
                返回首页
              </Link>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-4">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-6">
          <div className="flex items-center justify-center gap-2 mb-4">
            <BookOpen className="w-8 h-8 text-primary-600" />
            <h1 className="text-2xl font-bold text-gray-900">免费体验测试</h1>
          </div>
          <p className="text-gray-600">
            10道防诈骗基础题目，测试你的防范意识
          </p>
        </div>

        {/* Progress */}
        <div className="card mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-gray-500" />
              <span className="text-gray-600">用时: {formatTime(timeUsed)}</span>
            </div>
            <div className="text-sm text-gray-600">
              {currentQuestionIndex + 1} / {STARTER_QUESTIONS.length}
            </div>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-primary-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentQuestionIndex + 1) / STARTER_QUESTIONS.length) * 100}%` }}
            ></div>
          </div>
        </div>

        {/* Question */}
        <div className="card mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-6">
            {currentQuestion.title}
          </h2>

          <div className="space-y-3">
            {currentQuestion.options.map((option, index) => (
              <button
                key={index}
                onClick={() => handleAnswerSelect(index)}
                className={`quiz-option ${
                  selectedAnswers[currentQuestionIndex] === index ? 'selected' : ''
                } ${
                  showExplanation
                    ? index === currentQuestion.correctAnswer
                      ? 'correct'
                      : selectedAnswers[currentQuestionIndex] === index
                      ? 'incorrect'
                      : ''
                    : ''
                }`}
                disabled={showExplanation}
              >
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full border-2 border-current flex items-center justify-center text-sm font-medium">
                    {String.fromCharCode(65 + index)}
                  </div>
                  <div className="text-left">
                    <div className="font-medium">{option.text}</div>
                    {showExplanation && (
                      <div className="mt-2 text-sm opacity-80">
                        {option.explanation}
                      </div>
                    )}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-between">
          <Link href="/" className="btn-secondary">
            返回首页
          </Link>
          
          <div className="flex gap-3">
            {selectedAnswers[currentQuestionIndex] !== undefined && !showExplanation && (
              <button
                onClick={handleShowExplanation}
                className="btn-secondary"
              >
                查看解析
              </button>
            )}
            <button
              onClick={handleNext}
              disabled={selectedAnswers[currentQuestionIndex] === undefined}
              className="btn-primary flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLastQuestion ? '完成测试' : '下一题'}
              <ArrowRight className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
