'use client'

import { useState } from 'react'
import { Play, ArrowLeft, Video, Headphones, FileText, Camera, Mic, BookOpen } from 'lucide-react'
import Link from 'next/link'

export default function MultimediaContentPage() {
  const [activeCategory, setActiveCategory] = useState<'video' | 'audio' | 'interactive' | 'live'>('video')

  const categories = {
    video: {
      title: '视频教学',
      icon: <Video className="w-6 h-6" />,
      description: '生动的视频案例和专家讲解',
      content: [
        {
          title: '真实案例重现：大学生求职诈骗',
          duration: '8:32',
          views: '12.4K',
          description: '通过情景再现的方式，展示求职诈骗的完整过程和识别要点',
          thumbnail: '🎭',
          tags: ['求职骗局', '案例重现', '专家解析']
        },
        {
          title: '防诈骗专家访谈：如何识别网贷陷阱',
          duration: '15:20',
          views: '8.7K',
          description: '邀请反诈专家深度解析网贷诈骗的常见套路和防范方法',
          thumbnail: '👨‍💼',
          tags: ['专家访谈', '网贷陷阱', '深度解析']
        },
        {
          title: '动画科普：电信诈骗的演变历程',
          duration: '6:45',
          views: '15.2K',
          description: '用动画形式展示电信诈骗从传统到现代的发展变化',
          thumbnail: '🎨',
          tags: ['动画科普', '电信诈骗', '历史演变']
        }
      ]
    },
    audio: {
      title: '音频内容',
      icon: <Headphones className="w-6 h-6" />,
      description: '便于随时收听的音频课程和播客',
      content: [
        {
          title: '防诈骗每日一听：租房安全指南',
          duration: '12:15',
          views: '6.8K',
          description: '每日更新的防诈骗小贴士，今日主题：租房安全',
          thumbnail: '🏠',
          tags: ['每日更新', '租房安全', '实用指南']
        },
        {
          title: '真实受害者访谈：我是如何被培训诈骗的',
          duration: '25:30',
          views: '9.3K',
          description: '受害者亲述被骗经历，分享血泪教训',
          thumbnail: '🎙️',
          tags: ['真实案例', '受害者访谈', '培训诈骗']
        },
        {
          title: '法律专家解读：诈骗案件的法律后果',
          duration: '18:45',
          views: '4.2K',
          description: '法律专家详解诈骗相关法律条文和判决案例',
          thumbnail: '⚖️',
          tags: ['法律解读', '专家观点', '案例分析']
        }
      ]
    },
    interactive: {
      title: '互动体验',
      icon: <Camera className="w-6 h-6" />,
      description: '沉浸式的互动学习体验',
      content: [
        {
          title: 'VR 虚拟现实：身临其境体验诈骗现场',
          duration: '体验时长 10-15分钟',
          views: '3.1K',
          description: '通过 VR 技术让用户身临其境地体验各种诈骗场景',
          thumbnail: '🥽',
          tags: ['VR体验', '沉浸式', '场景模拟']
        },
        {
          title: '角色扮演：我是反诈警察',
          duration: '互动游戏',
          views: '7.5K',
          description: '扮演反诈警察，处理各种诈骗案件，学习专业识别技巧',
          thumbnail: '👮‍♂️',
          tags: ['角色扮演', '游戏化', '专业技能']
        },
        {
          title: '模拟对话：与诈骗分子的智斗',
          duration: '对话练习',
          views: '5.9K',
          description: '模拟与诈骗分子的对话，练习应对技巧和话术',
          thumbnail: '💬',
          tags: ['对话模拟', '应对技巧', '实战练习']
        }
      ]
    },
    live: {
      title: '直播讲座',
      icon: <Mic className="w-6 h-6" />,
      description: '实时互动的专家讲座和答疑',
      content: [
        {
          title: '本周直播：大学生防诈骗专题讲座',
          duration: '每周三 19:00',
          views: '预约 2.3K',
          description: '邀请反诈专家和高校老师，针对大学生群体的防诈骗教育',
          thumbnail: '📺',
          tags: ['定期直播', '专家讲座', '互动答疑']
        },
        {
          title: '案例分析直播：最新诈骗手段解析',
          duration: '每月第二个周五',
          views: '预约 1.8K',
          description: '分析当月最新出现的诈骗手段和防范方法',
          thumbnail: '🔍',
          tags: ['案例分析', '最新动态', '实时更新']
        },
        {
          title: '互动问答：你问我答防诈骗',
          duration: '不定期',
          views: '关注 4.7K',
          description: '用户提问，专家实时解答各种防诈骗相关问题',
          thumbnail: '❓',
          tags: ['互动问答', '实时解答', '用户参与']
        }
      ]
    }
  }

  const currentCategory = categories[activeCategory]

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-3">
            <Link href="/dashboard" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </Link>
            <BookOpen className="w-8 h-8 text-green-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">多媒体内容中心</h1>
              <p className="text-sm text-gray-600">丰富的视听学习资源</p>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 分类导航 */}
        <div className="card mb-8">
          <h2 className="text-xl font-bold text-gray-900 mb-6">内容分类</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Object.entries(categories).map(([key, category]) => (
              <button
                key={key}
                onClick={() => setActiveCategory(key as any)}
                className={`p-4 rounded-lg border-2 text-left transition-all ${
                  activeCategory === key
                    ? 'border-green-500 bg-green-50'
                    : 'border-gray-200 hover:border-green-300'
                }`}
              >
                <div className="flex items-center gap-3 mb-2">
                  <div className={`p-2 rounded-lg ${
                    activeCategory === key ? 'bg-green-100' : 'bg-gray-100'
                  }`}>
                    {category.icon}
                  </div>
                  <h3 className="font-semibold text-gray-900">{category.title}</h3>
                </div>
                <p className="text-sm text-gray-600">{category.description}</p>
              </button>
            ))}
          </div>
        </div>

        {/* 内容展示 */}
        <div className="space-y-6">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-3 bg-green-100 rounded-lg">
              {currentCategory.icon}
            </div>
            <div>
              <h3 className="text-2xl font-bold text-gray-900">{currentCategory.title}</h3>
              <p className="text-gray-600">{currentCategory.description}</p>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {currentCategory.content.map((item, index) => (
              <div key={index} className="card hover:shadow-lg transition-shadow">
                {/* 缩略图 */}
                <div className="relative bg-gray-100 rounded-lg mb-4 h-48 flex items-center justify-center">
                  <div className="text-6xl">{item.thumbnail}</div>
                  <div className="absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                    <button className="bg-white/90 rounded-full p-3 hover:bg-white transition-colors">
                      <Play className="w-6 h-6 text-gray-800" />
                    </button>
                  </div>
                  <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                    {item.duration}
                  </div>
                </div>

                {/* 内容信息 */}
                <div className="space-y-3">
                  <h4 className="font-semibold text-gray-900 line-clamp-2">{item.title}</h4>
                  <p className="text-sm text-gray-600 line-clamp-3">{item.description}</p>
                  
                  {/* 标签 */}
                  <div className="flex flex-wrap gap-2">
                    {item.tags.map((tag, tagIndex) => (
                      <span
                        key={tagIndex}
                        className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* 统计信息 */}
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>{item.views} 观看</span>
                    <button className="text-green-600 hover:text-green-700 font-medium">
                      立即观看
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 功能说明 */}
        <div className="card mt-8 bg-gradient-to-r from-green-500 to-blue-600 text-white">
          <h3 className="text-xl font-bold mb-4">🎬 多媒体学习的优势</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">视听结合，印象深刻</h4>
              <p className="text-green-100 text-sm">
                通过视频、音频等多种形式，让防诈骗知识更加生动有趣，提高学习效果。
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-2">真实案例，警示教育</h4>
              <p className="text-green-100 text-sm">
                基于真实案例制作内容，让用户深刻理解诈骗的危害和防范的重要性。
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-2">专家指导，权威可信</h4>
              <p className="text-green-100 text-sm">
                邀请反诈专家、法律专家等权威人士，确保内容的专业性和准确性。
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-2">互动体验，身临其境</h4>
              <p className="text-green-100 text-sm">
                通过 VR、角色扮演等互动方式，让用户在实践中掌握防诈骗技能。
              </p>
            </div>
          </div>
        </div>

        {/* 开发状态 */}
        <div className="card mt-6 text-center">
          <h3 className="text-xl font-bold text-gray-900 mb-4">📋 开发计划</h3>
          <p className="text-gray-600 mb-6">
            多媒体内容正在制作中，我们正在与专家团队合作，打造高质量的教育内容。
          </p>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="text-2xl font-bold text-green-600">15+</div>
              <div className="text-sm text-gray-600">视频制作中</div>
            </div>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="text-2xl font-bold text-blue-600">8+</div>
              <div className="text-sm text-gray-600">音频录制中</div>
            </div>
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <div className="text-2xl font-bold text-purple-600">5+</div>
              <div className="text-sm text-gray-600">互动体验开发中</div>
            </div>
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <div className="text-2xl font-bold text-orange-600">2+</div>
              <div className="text-sm text-gray-600">直播栏目筹备中</div>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="btn-primary" disabled>
              订阅更新通知 (开发中)
            </button>
            <Link href="/dashboard" className="btn-secondary">
              返回主页
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
