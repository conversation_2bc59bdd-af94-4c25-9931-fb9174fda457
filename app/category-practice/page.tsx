'use client'

import { useState, useEffect } from 'react'
import { Book<PERSON><PERSON>, ArrowLeft, Target, Clock, Trophy, Play, CheckCircle } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { CATEGORY_LABELS, QuestionCategory } from '@/types'

interface CategoryInfo {
  category: QuestionCategory
  title: string
  description: string
  icon: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  questionCount: number
  completedCount: number
  bestScore: number
  color: string
}

const CATEGORY_INFO: CategoryInfo[] = [
  {
    category: 'job-fraud',
    title: '求职防骗',
    description: '识别虚假招聘、培训费诈骗等求职陷阱',
    icon: '💼',
    difficulty: 'beginner',
    questionCount: 15,
    completedCount: 8,
    bestScore: 87,
    color: 'blue'
  },
  {
    category: 'rental-scam',
    title: '租房安全',
    description: '防范假房东、押金诈骗等租房风险',
    icon: '🏠',
    difficulty: 'beginner',
    questionCount: 12,
    completedCount: 5,
    bestScore: 75,
    color: 'green'
  },
  {
    category: 'loan-trap',
    title: '金融理财',
    description: '识别网贷陷阱、投资诈骗等金融风险',
    icon: '💰',
    difficulty: 'intermediate',
    questionCount: 18,
    completedCount: 3,
    bestScore: 65,
    color: 'yellow'
  },
  {
    category: 'training-scam',
    title: '教育培训',
    description: '防范培训诈骗、学历造假等教育陷阱',
    icon: '🎓',
    difficulty: 'intermediate',
    questionCount: 10,
    completedCount: 0,
    bestScore: 0,
    color: 'purple'
  },
  {
    category: 'telecom-fraud',
    title: '网络通信',
    description: '识别电信诈骗、钓鱼网站等网络风险',
    icon: '📱',
    difficulty: 'advanced',
    questionCount: 20,
    completedCount: 2,
    bestScore: 45,
    color: 'indigo'
  },
  {
    category: 'fake-authority',
    title: '冒充权威',
    description: '识别冒充公检法、政府机构等权威诈骗',
    icon: '⚖️',
    difficulty: 'advanced',
    questionCount: 8,
    completedCount: 0,
    bestScore: 0,
    color: 'red'
  },
  {
    category: 'part-time-scam',
    title: '兼职副业',
    description: '防范刷单诈骗、兼职陷阱等副业风险',
    icon: '💻',
    difficulty: 'beginner',
    questionCount: 14,
    completedCount: 6,
    bestScore: 92,
    color: 'pink'
  }
]

export default function CategoryPracticePage() {
  const router = useRouter()
  const [selectedDifficulty, setSelectedDifficulty] = useState<'all' | 'beginner' | 'intermediate' | 'advanced'>('all')
  const [userData, setUserData] = useState<any>(null)

  useEffect(() => {
    // 检查用户登录状态（Demo 模式允许未登录）
    const userDataStr = localStorage.getItem('userData')
    if (userDataStr) {
      setUserData(JSON.parse(userDataStr))
    }
  }, [])

  const filteredCategories = selectedDifficulty === 'all' 
    ? CATEGORY_INFO 
    : CATEGORY_INFO.filter(cat => cat.difficulty === selectedDifficulty)

  const getDifficultyLabel = (difficulty: string) => {
    const labels = {
      'beginner': '入门',
      'intermediate': '进阶', 
      'advanced': '高级'
    }
    return labels[difficulty as keyof typeof labels] || difficulty
  }

  const getColorClasses = (color: string) => {
    const colorMap = {
      'blue': 'from-blue-500 to-blue-600 border-blue-200 bg-blue-50',
      'green': 'from-green-500 to-green-600 border-green-200 bg-green-50',
      'yellow': 'from-yellow-500 to-yellow-600 border-yellow-200 bg-yellow-50',
      'purple': 'from-purple-500 to-purple-600 border-purple-200 bg-purple-50',
      'indigo': 'from-indigo-500 to-indigo-600 border-indigo-200 bg-indigo-50',
      'red': 'from-red-500 to-red-600 border-red-200 bg-red-50',
      'pink': 'from-pink-500 to-pink-600 border-pink-200 bg-pink-50'
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.blue
  }

  const handleStartPractice = (category: QuestionCategory) => {
    // 保存选择的分类到 localStorage
    localStorage.setItem('selectedCategory', category)
    
    if (userData) {
      // 已登录用户跳转到专项挑战
      router.push(`/category-challenge?category=${category}`)
    } else {
      // 未登录用户跳转到体验模式
      router.push(`/demo-quiz?category=${category}`)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-3">
            <Link href={userData ? "/dashboard" : "/"} className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </Link>
            <BookOpen className="w-8 h-8 text-primary-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">分类刷题</h1>
              <p className="text-sm text-gray-600">选择感兴趣的领域，针对性学习防诈骗知识</p>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 难度筛选 */}
        <div className="card mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">难度筛选</h3>
          <div className="flex flex-wrap gap-3">
            {['all', 'beginner', 'intermediate', 'advanced'].map((difficulty) => (
              <button
                key={difficulty}
                onClick={() => setSelectedDifficulty(difficulty as any)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                  selectedDifficulty === difficulty
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {difficulty === 'all' ? '全部' : getDifficultyLabel(difficulty)}
              </button>
            ))}
          </div>
        </div>

        {/* 分类网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCategories.map((categoryInfo) => (
            <div
              key={categoryInfo.category}
              className={`card hover:shadow-lg transition-all duration-300 border-2 ${getColorClasses(categoryInfo.color)}`}
            >
              {/* 分类头部 */}
              <div className="flex items-center gap-4 mb-4">
                <div className="text-4xl">{categoryInfo.icon}</div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-gray-900">{categoryInfo.title}</h3>
                  <p className="text-sm text-gray-600">{categoryInfo.description}</p>
                </div>
              </div>

              {/* 难度标签 */}
              <div className="flex items-center gap-2 mb-4">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  categoryInfo.difficulty === 'beginner' ? 'bg-green-100 text-green-700' :
                  categoryInfo.difficulty === 'intermediate' ? 'bg-yellow-100 text-yellow-700' :
                  'bg-red-100 text-red-700'
                }`}>
                  {getDifficultyLabel(categoryInfo.difficulty)}
                </span>
                <span className="text-sm text-gray-500">
                  {categoryInfo.questionCount} 道题目
                </span>
              </div>

              {/* 进度信息 */}
              <div className="space-y-3 mb-6">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">完成进度</span>
                  <span className="font-medium">
                    {categoryInfo.completedCount}/{categoryInfo.questionCount}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full bg-gradient-to-r ${getColorClasses(categoryInfo.color).split(' ')[0]} ${getColorClasses(categoryInfo.color).split(' ')[1]}`}
                    style={{ width: `${(categoryInfo.completedCount / categoryInfo.questionCount) * 100}%` }}
                  ></div>
                </div>
                
                {categoryInfo.bestScore > 0 && (
                  <div className="flex items-center gap-2 text-sm">
                    <Trophy className="w-4 h-4 text-yellow-500" />
                    <span className="text-gray-600">最佳成绩: {categoryInfo.bestScore}%</span>
                  </div>
                )}
              </div>

              {/* 操作按钮 */}
              <div className="flex gap-3">
                <button
                  onClick={() => handleStartPractice(categoryInfo.category)}
                  className="flex-1 btn-primary flex items-center justify-center gap-2"
                >
                  <Play className="w-4 h-4" />
                  开始练习
                </button>
                {categoryInfo.completedCount > 0 && (
                  <button className="btn-secondary flex items-center gap-2">
                    <Clock className="w-4 h-4" />
                    复习
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* 学习建议 */}
        <div className="card mt-8 bg-gradient-to-r from-primary-500 to-purple-600 text-white">
          <h3 className="text-xl font-bold mb-4">💡 学习建议</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">新手推荐路径</h4>
              <ol className="text-primary-100 text-sm space-y-1">
                <li>1. 求职防骗 → 基础必学</li>
                <li>2. 租房安全 → 生活必备</li>
                <li>3. 兼职副业 → 收入相关</li>
                <li>4. 金融理财 → 进阶学习</li>
              </ol>
            </div>
            <div>
              <h4 className="font-semibold mb-2">学习小贴士</h4>
              <ul className="text-primary-100 text-sm space-y-1">
                <li>• 建议每天练习1-2个分类</li>
                <li>• 错题要及时复习巩固</li>
                <li>• 结合实际生活场景思考</li>
                <li>• 分享经验帮助他人学习</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-8">
          <div className="card text-center">
            <div className="text-2xl font-bold text-primary-600">
              {CATEGORY_INFO.reduce((sum, cat) => sum + cat.questionCount, 0)}
            </div>
            <div className="text-sm text-gray-600">总题目数</div>
          </div>
          <div className="card text-center">
            <div className="text-2xl font-bold text-success-600">
              {CATEGORY_INFO.reduce((sum, cat) => sum + cat.completedCount, 0)}
            </div>
            <div className="text-sm text-gray-600">已完成</div>
          </div>
          <div className="card text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {CATEGORY_INFO.filter(cat => cat.bestScore > 0).length}
            </div>
            <div className="text-sm text-gray-600">已掌握分类</div>
          </div>
          <div className="card text-center">
            <div className="text-2xl font-bold text-purple-600">
              {Math.round(CATEGORY_INFO.reduce((sum, cat) => sum + cat.bestScore, 0) / CATEGORY_INFO.length)}%
            </div>
            <div className="text-sm text-gray-600">平均成绩</div>
          </div>
        </div>
      </div>
    </div>
  )
}
