'use client'

import { useState, useEffect } from 'react'
import { Trophy, Medal, Crown, Clock, Target, Filter, ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { CATEGORY_LABELS, QuestionCategory } from '@/types'

interface LeaderboardEntry {
  rank: number
  nickname: string
  score: number
  timeUsed: number
  category?: QuestionCategory
  date: string
}

// 模拟排行榜数据
const MOCK_LEADERBOARD: LeaderboardEntry[] = [
  { rank: 1, nickname: '防诈达人', score: 25, timeUsed: 180, category: 'job-fraud', date: '2024-01-15' },
  { rank: 2, nickname: '安全小卫士', score: 23, timeUsed: 165, category: 'telecom-fraud', date: '2024-01-15' },
  { rank: 3, nickname: '智慧学子', score: 22, timeUsed: 190, category: 'loan-trap', date: '2024-01-14' },
  { rank: 4, nickname: '警觉青年', score: 20, timeUsed: 145, category: 'rental-scam', date: '2024-01-14' },
  { rank: 5, nickname: '反诈先锋', score: 19, timeUsed: 200, category: 'training-scam', date: '2024-01-13' },
  { rank: 6, nickname: '谨慎同学', score: 18, timeUsed: 175, category: 'part-time-scam', date: '2024-01-13' },
  { rank: 7, nickname: '明智选择', score: 17, timeUsed: 160, category: 'fake-authority', date: '2024-01-12' },
  { rank: 8, nickname: '理性思考', score: 16, timeUsed: 185, category: 'job-fraud', date: '2024-01-12' },
  { rank: 9, nickname: '细心观察', score: 15, timeUsed: 170, category: 'telecom-fraud', date: '2024-01-11' },
  { rank: 10, nickname: '冷静分析', score: 14, timeUsed: 195, category: 'loan-trap', date: '2024-01-11' },
]

export default function LeaderboardPage() {
  const [selectedCategory, setSelectedCategory] = useState<QuestionCategory | 'all'>('all')
  const [timeFilter, setTimeFilter] = useState<'today' | 'week' | 'month' | 'all'>('all')
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>(MOCK_LEADERBOARD)

  const filteredLeaderboard = leaderboard.filter(entry => {
    if (selectedCategory !== 'all' && entry.category !== selectedCategory) {
      return false
    }
    
    const entryDate = new Date(entry.date)
    const now = new Date()
    
    switch (timeFilter) {
      case 'today':
        return entryDate.toDateString() === now.toDateString()
      case 'week':
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        return entryDate >= weekAgo
      case 'month':
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        return entryDate >= monthAgo
      default:
        return true
    }
  })

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-6 h-6 text-yellow-500" />
      case 2:
        return <Medal className="w-6 h-6 text-gray-400" />
      case 3:
        return <Medal className="w-6 h-6 text-amber-600" />
      default:
        return <div className="w-6 h-6 flex items-center justify-center text-gray-500 font-bold">{rank}</div>
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Link href="/dashboard" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </Link>
              <Trophy className="w-8 h-8 text-yellow-500" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">排行榜</h1>
                <p className="text-sm text-gray-600">查看挑战高手排名</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters */}
        <div className="card mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Category Filter */}
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Filter className="w-4 h-4 inline mr-1" />
                诈骗类型
              </label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value as QuestionCategory | 'all')}
                className="input-field"
              >
                <option value="all">全部类型</option>
                {Object.entries(CATEGORY_LABELS).map(([key, label]) => (
                  <option key={key} value={key}>{label}</option>
                ))}
              </select>
            </div>

            {/* Time Filter */}
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Clock className="w-4 h-4 inline mr-1" />
                时间范围
              </label>
              <select
                value={timeFilter}
                onChange={(e) => setTimeFilter(e.target.value as 'today' | 'week' | 'month' | 'all')}
                className="input-field"
              >
                <option value="all">全部时间</option>
                <option value="today">今日</option>
                <option value="week">本周</option>
                <option value="month">本月</option>
              </select>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="card text-center">
            <div className="text-3xl font-bold text-primary-600 mb-2">{filteredLeaderboard.length}</div>
            <div className="text-gray-600">参与用户</div>
          </div>
          <div className="card text-center">
            <div className="text-3xl font-bold text-success-600 mb-2">
              {filteredLeaderboard.length > 0 ? Math.max(...filteredLeaderboard.map(e => e.score)) : 0}
            </div>
            <div className="text-gray-600">最高分数</div>
          </div>
          <div className="card text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">
              {filteredLeaderboard.length > 0 ? 
                formatTime(Math.min(...filteredLeaderboard.map(e => e.timeUsed))) : '0:00'
              }
            </div>
            <div className="text-gray-600">最快用时</div>
          </div>
        </div>

        {/* Leaderboard */}
        <div className="card">
          <div className="flex items-center gap-2 mb-6">
            <Trophy className="w-6 h-6 text-yellow-500" />
            <h2 className="text-xl font-bold text-gray-900">
              {selectedCategory === 'all' ? '全部' : CATEGORY_LABELS[selectedCategory as QuestionCategory]} 排行榜
            </h2>
          </div>

          {filteredLeaderboard.length === 0 ? (
            <div className="text-center py-12">
              <Target className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">暂无符合条件的排行数据</p>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredLeaderboard.map((entry, index) => (
                <div
                  key={index}
                  className={`flex items-center justify-between p-4 rounded-lg border-2 transition-all ${
                    entry.rank <= 3
                      ? 'border-yellow-200 bg-yellow-50'
                      : 'border-gray-200 bg-gray-50'
                  }`}
                >
                  <div className="flex items-center gap-4">
                    <div className="flex-shrink-0">
                      {getRankIcon(entry.rank)}
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{entry.nickname}</div>
                      <div className="text-sm text-gray-600">
                        {entry.category && CATEGORY_LABELS[entry.category]} • {entry.date}
                      </div>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="flex items-center gap-4">
                      <div className="text-center">
                        <div className="text-lg font-bold text-primary-600">{entry.score}</div>
                        <div className="text-xs text-gray-500">题数</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-blue-600">{formatTime(entry.timeUsed)}</div>
                        <div className="text-xs text-gray-500">用时</div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-8">
          <div className="card bg-gradient-to-r from-primary-500 to-purple-600 text-white">
            <h3 className="text-xl font-bold mb-2">想要上榜吗？</h3>
            <p className="text-primary-100 mb-4">
              参与每日挑战，提升你的防诈骗能力，争取更好的排名！
            </p>
            <Link href="/challenge" className="btn-primary bg-white text-primary-600 hover:bg-gray-100">
              开始挑战
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
