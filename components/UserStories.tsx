'use client'

import { useState, useEffect } from 'react'
import { AlertTriangle, Clock, ThumbsUp, MessageCircle, Eye } from 'lucide-react'

interface UserStory {
  id: string
  title: string
  content: string
  category: string
  author: string
  timeAgo: string
  likes: number
  comments: number
  views: number
  isVerified: boolean
  severity: 'low' | 'medium' | 'high'
}

const USER_STORIES: UserStory[] = [
  {
    id: '1',
    title: '差点被"高薪兼职"骗了5000元',
    content: '在QQ群里看到日赚300的兼职广告，要求先交2000元保证金。幸好室友提醒我这是诈骗，差点就转账了...',
    category: '兼职诈骗',
    author: '小李同学',
    timeAgo: '2小时前',
    likes: 23,
    comments: 8,
    views: 156,
    isVerified: true,
    severity: 'high'
  },
  {
    id: '2',
    title: '租房遇到假房东，损失1500元',
    content: '在网上看到便宜房源，房东说要先交定金才能看房。结果交了钱就联系不上了，房子根本不存在...',
    category: '租房陷阱',
    author: '张小明',
    timeAgo: '5小时前',
    likes: 31,
    comments: 12,
    views: 203,
    isVerified: false,
    severity: 'medium'
  },
  {
    id: '3',
    title: '网贷"砍头息"套路深，借3000还6000',
    content: '急需用钱申请了网贷，说是无息但要交各种手续费。最后发现实际利率超高，还不起就威胁恐吓...',
    category: '网贷陷阱',
    author: '王小华',
    timeAgo: '1天前',
    likes: 45,
    comments: 18,
    views: 312,
    isVerified: true,
    severity: 'high'
  },
  {
    id: '4',
    title: '培训机构承诺"包就业"，结果是骗局',
    content: '交了8000元培训费，说包分配工作。培训结束后推荐的都是销售岗位，和承诺的技术岗完全不符...',
    category: '培训诈骗',
    author: '李小红',
    timeAgo: '2天前',
    likes: 28,
    comments: 15,
    views: 189,
    isVerified: false,
    severity: 'medium'
  },
  {
    id: '5',
    title: '冒充银行客服，差点泄露银行卡信息',
    content: '接到"银行客服"电话说卡被盗刷，要求提供验证码。还好想起防诈骗知识，直接挂断并报警...',
    category: '电信诈骗',
    author: '陈小军',
    timeAgo: '3天前',
    likes: 52,
    comments: 22,
    views: 278,
    isVerified: true,
    severity: 'high'
  },
  {
    id: '6',
    title: '求职被要求交"体检费"，及时识破',
    content: '面试通过后HR说要交500元体检费才能入职。感觉不对劲上网查了查，发现是常见的求职诈骗套路...',
    category: '求职骗局',
    author: '刘小芳',
    timeAgo: '4天前',
    likes: 19,
    comments: 7,
    views: 134,
    isVerified: false,
    severity: 'low'
  },
  {
    id: '7',
    title: '投资理财群里的"专家"都是托',
    content: '被拉进投资群，看到很多人晒收益截图。跟着投了2万元，结果平台跑路了，钱全没了...',
    category: '投资诈骗',
    author: '赵小强',
    timeAgo: '5天前',
    likes: 67,
    comments: 25,
    views: 445,
    isVerified: true,
    severity: 'high'
  },
  {
    id: '8',
    title: '网购退款诈骗，差点被套取验证码',
    content: '收到"客服"电话说商品有质量问题要退款，要求提供支付宝验证码。幸好多了个心眼没有提供...',
    category: '网购诈骗',
    author: '孙小美',
    timeAgo: '1周前',
    likes: 34,
    comments: 11,
    views: 198,
    isVerified: false,
    severity: 'medium'
  }
]

export default function UserStories() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isPlaying, setIsPlaying] = useState(true)

  useEffect(() => {
    if (!isPlaying) return

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % USER_STORIES.length)
    }, 5000) // 每5秒切换一次

    return () => clearInterval(interval)
  }, [isPlaying])

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'text-red-600 bg-red-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'low': return 'text-green-600 bg-green-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getSeverityLabel = (severity: string) => {
    switch (severity) {
      case 'high': return '高危'
      case 'medium': return '中危'
      case 'low': return '低危'
      default: return '未知'
    }
  }

  const currentStory = USER_STORIES[currentIndex]

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6 h-full">
      {/* 标题栏 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <AlertTriangle className="w-5 h-5 text-orange-500" />
          <h3 className="text-lg font-semibold text-gray-900">用户防骗故事</h3>
        </div>
        <button
          onClick={() => setIsPlaying(!isPlaying)}
          className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
            isPlaying 
              ? 'bg-green-100 text-green-700 hover:bg-green-200' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          {isPlaying ? '暂停' : '播放'}
        </button>
      </div>

      {/* 故事内容 */}
      <div className="space-y-4">
        {/* 故事头部 */}
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h4 className="font-semibold text-gray-900 mb-2 line-clamp-2">
              {currentStory.title}
            </h4>
            <div className="flex items-center gap-2 mb-3">
              <span className="text-sm text-gray-600">{currentStory.author}</span>
              {currentStory.isVerified && (
                <span className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">✓</span>
                </span>
              )}
              <span className="text-xs text-gray-500">•</span>
              <span className="text-xs text-gray-500">{currentStory.timeAgo}</span>
            </div>
          </div>
          <div className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(currentStory.severity)}`}>
            {getSeverityLabel(currentStory.severity)}
          </div>
        </div>

        {/* 分类标签 */}
        <div className="flex items-center gap-2">
          <span className="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full">
            {currentStory.category}
          </span>
        </div>

        {/* 故事内容 */}
        <p className="text-gray-700 text-sm leading-relaxed line-clamp-4">
          {currentStory.content}
        </p>

        {/* 互动数据 */}
        <div className="flex items-center gap-4 text-xs text-gray-500 pt-3 border-t border-gray-100">
          <div className="flex items-center gap-1">
            <ThumbsUp className="w-3 h-3" />
            <span>{currentStory.likes}</span>
          </div>
          <div className="flex items-center gap-1">
            <MessageCircle className="w-3 h-3" />
            <span>{currentStory.comments}</span>
          </div>
          <div className="flex items-center gap-1">
            <Eye className="w-3 h-3" />
            <span>{currentStory.views}</span>
          </div>
        </div>
      </div>

      {/* 进度指示器 */}
      <div className="flex items-center gap-1 mt-6">
        {USER_STORIES.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentIndex(index)}
            className={`h-1 rounded-full transition-all duration-300 ${
              index === currentIndex 
                ? 'bg-primary-600 w-8' 
                : 'bg-gray-300 w-2 hover:bg-gray-400'
            }`}
          />
        ))}
      </div>

      {/* 底部提示 */}
      <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <p className="text-blue-800 text-xs">
          💡 <strong>提醒：</strong>这些都是真实用户分享的被骗经历，希望能帮助大家提高警惕，避免类似损失。
        </p>
      </div>
    </div>
  )
}
