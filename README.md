# 大学生防诈挑战游戏 Demo

这是一个基于 Next.js 开发的防诈骗教育应用 Demo，通过游戏化的答题挑战帮助大学生提升防诈骗意识。

## 🎯 项目特色

- **匿名参与机制**：通过邀请码+昵称的方式匿名登录，保护用户隐私
- **分类学习系统**：涵盖求职、租房、网贷、培训等7大诈骗类型
- **游戏化体验**：入门题+每日挑战的递进式学习模式
- **社区分享**：用户可以分享防诈骗经验和真实案例
- **成绩分享**：生成精美海报，支持多种模板风格
- **题库共建**：用户可提交题目，经审核后加入题库

## 🚀 技术栈

- **前端框架**：Next.js 14 + React 18 + TypeScript
- **样式方案**：TailwindCSS + 自定义组件
- **状态管理**：React Hooks + localStorage
- **图标库**：Lucide React
- **部署方案**：Cloudflare Pages (计划)

## 📱 功能模块

### 核心功能
- [x] 匿名登录系统
- [x] 入门题挑战（10道基础题）
- [x] 每日挑战模式
- [x] 分类题库系统
- [x] 排行榜展示
- [x] 个人中心管理

### 扩展功能
- [x] 学习中心（防诈骗知识库）
- [x] 社区交流平台
- [x] 题目提交系统
- [x] 成绩海报生成
- [x] 数据导出功能

## 🎮 游戏流程

1. **匿名登录**：输入邀请码和昵称
2. **入门挑战**：完成10道基础防诈骗题目
3. **每日挑战**：选择类型，持续答题直到答错
4. **查看排名**：在排行榜中查看自己的成绩
5. **分享成果**：生成成绩海报分享到社交平台

## 🛡️ 防诈骗类型

- **求职骗局**：虚假招聘、培训费诈骗
- **租房陷阱**：假房东、一房多租
- **网贷陷阱**：高利贷、套路贷
- **培训诈骗**：虚假培训、包过承诺
- **电信诈骗**：钓鱼短信、虚假客服
- **冒充公检法**：虚假执法、安全账户
- **兼职诈骗**：刷单诈骗、垫付骗局

## 🚀 快速开始

### 环境要求
- Node.js 18+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本
```bash
npm run build
```

## 📊 Demo 数据

当前 Demo 包含：
- 10道精心设计的入门题目
- 模拟的排行榜数据
- 示例社区帖子
- 完整的用户流程体验

所有数据存储在浏览器 localStorage 中，刷新页面不会丢失。

## 🔮 后续开发计划

### Phase 1: 基础功能完善
- [ ] 后端 API 开发（Cloudflare Workers）
- [ ] 数据库集成（Cloudflare D1）
- [ ] 用户认证系统
- [ ] 题目审核后台

### Phase 2: 功能增强
- [ ] AI 辅助出题
- [ ] 个性化推荐
- [ ] 学习报告生成
- [ ] 多媒体题目支持

### Phase 3: 社交功能
- [ ] 用户互动系统
- [ ] 团队挑战模式
- [ ] 成就系统
- [ ] 分享激励机制

## 🎨 设计理念

- **教育性**：每道题目都基于真实案例，具有实际教育意义
- **趣味性**：游戏化的挑战模式，提高用户参与度
- **实用性**：涵盖大学生常遇到的诈骗类型
- **安全性**：匿名参与，保护用户隐私

## 📝 贡献指南

欢迎提交 Issue 和 Pull Request！

### 题目贡献
如果你有真实的防诈骗案例或题目想法，可以：
1. 通过应用内的"提交题目"功能
2. 在 GitHub 上提交 Issue
3. 直接提交 Pull Request

### 代码贡献
1. Fork 本仓库
2. 创建功能分支
3. 提交代码并测试
4. 创建 Pull Request

## 📄 许可证

MIT License

## 🤝 联系我们

如有问题或建议，请通过以下方式联系：
- GitHub Issues
- 项目讨论区

---

**让我们一起建设一个更安全的网络环境！** 🛡️
