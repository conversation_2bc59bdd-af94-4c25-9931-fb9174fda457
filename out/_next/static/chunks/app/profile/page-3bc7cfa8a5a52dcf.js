(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[178],{2898:function(e,t,s){"use strict";s.d(t,{Z:function(){return createLucideIcon}});var r=s(2265),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let toKebabCase=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),createLucideIcon=(e,t)=>{let s=(0,r.forwardRef)(({color:s="currentColor",size:l=24,strokeWidth:c=2,absoluteStrokeWidth:n,className:i="",children:d,...x},o)=>(0,r.createElement)("svg",{ref:o,...a,width:l,height:l,stroke:s,strokeWidth:n?24*Number(c)/Number(l):c,className:["lucide",`lucide-${toKebabCase(e)}`,i].join(" "),...x},[...t.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(d)?d:[d]]));return s.displayName=`${e}`,s}},3067:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8203:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},5817:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},6654:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},8957:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},7972:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},4622:function(e,t,s){Promise.resolve().then(s.bind(s,5054))},5054:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return ProfilePage}});var r=s(7437),a=s(2265),l=s(3067),c=s(7972),n=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,n.Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);var d=s(6654),x=s(8957),o=s(8203),m=s(5817);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let h=(0,n.Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var u=s(1396),y=s.n(u),g=s(4033);function ProfilePage(){let e=(0,g.useRouter)(),[t,s]=(0,a.useState)(null),[n,u]=(0,a.useState)(null),[j,f]=(0,a.useState)([]);(0,a.useEffect)(()=>{let t=localStorage.getItem("userData");if(!t){e.push("/login");return}s(JSON.parse(t));let r=localStorage.getItem("starterResult");r&&u(JSON.parse(r));let a=localStorage.getItem("lastChallengeResult");a&&f([JSON.parse(a)])},[e]);let formatTime=e=>"".concat(Math.floor(e/60),":").concat((e%60).toString().padStart(2,"0"));return t?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(y(),{href:"/dashboard",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(l.Z,{className:"w-5 h-5 text-gray-600"})}),(0,r.jsx)(c.Z,{className:"w-8 h-8 text-primary-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"个人中心"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"管理你的账户和数据"})]})]}),(0,r.jsxs)("button",{onClick:()=>{confirm("确定要退出登录吗？")&&(localStorage.clear(),e.push("/"))},className:"btn-danger flex items-center gap-2",children:[(0,r.jsx)(i,{className:"w-4 h-4"}),"退出登录"]})]})})}),(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"card mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(c.Z,{className:"w-8 h-8 text-primary-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:t.nickname}),(0,r.jsxs)("p",{className:"text-gray-600",children:["邀请码: ",t.inviteCode]}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["注册时间: ",new Date(t.createdAt).toLocaleDateString()]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"bg-primary-50 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-primary-600",children:t.contributionScore||0}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"贡献值"})]}),(0,r.jsxs)("div",{className:"bg-success-50 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-success-600",children:n?"".concat(n.score,"/").concat(n.total):"未完成"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"入门题成绩"})]}),(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:j.length}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"挑战次数"})]})]})]}),n&&(0,r.jsxs)("div",{className:"card mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,r.jsx)(d.Z,{className:"w-6 h-6 text-success-600"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"入门挑战成绩"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-lg font-bold text-success-600",children:n.score}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"正确题数"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-lg font-bold text-blue-600",children:[Math.round(n.score/n.total*100),"%"]}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"正确率"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-lg font-bold text-purple-600",children:formatTime(n.timeUsed)}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"用时"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-lg font-bold text-gray-600",children:new Date(n.completedAt).toLocaleDateString()}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"完成日期"})]})]})]}),(0,r.jsxs)("div",{className:"card mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,r.jsx)(x.Z,{className:"w-6 h-6 text-yellow-500"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"挑战历史"})]}),0===j.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(o.Z,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-500",children:"还没有挑战记录"}),(0,r.jsx)(y(),{href:"/challenge",className:"btn-primary mt-4",children:"开始挑战"})]}):(0,r.jsx)("div",{className:"space-y-4",children:j.map((e,t)=>(0,r.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"font-semibold text-gray-900",children:["每日挑战 #",t+1]}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:new Date(e.date).toLocaleDateString()})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("div",{className:"text-lg font-bold text-primary-600",children:[e.score," 题"]}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:formatTime(e.timeUsed)})]})]})},t))})]}),(0,r.jsxs)("div",{className:"card",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"数据管理"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-blue-900",children:"导出数据"}),(0,r.jsx)("div",{className:"text-sm text-blue-700",children:"下载你的所有游戏数据"})]}),(0,r.jsxs)("button",{onClick:()=>{let e={userData:t,starterResult:n,challengeHistory:j,exportDate:new Date().toISOString()},s=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),r=URL.createObjectURL(s),a=document.createElement("a");a.href=r,a.download="防诈挑战数据-".concat(null==t?void 0:t.nickname,"-").concat(new Date().toLocaleDateString(),".json"),a.click(),URL.revokeObjectURL(r)},className:"btn-primary flex items-center gap-2",children:[(0,r.jsx)(m.Z,{className:"w-4 h-4"}),"导出"]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-red-900",children:"清除所有数据"}),(0,r.jsx)("div",{className:"text-sm text-red-700",children:"删除所有本地存储的数据，此操作不可恢复"})]}),(0,r.jsxs)("button",{onClick:()=>{confirm("确定要清除所有数据吗？此操作不可恢复！")&&(localStorage.clear(),e.push("/"))},className:"btn-danger flex items-center gap-2",children:[(0,r.jsx)(h,{className:"w-4 h-4"}),"清除"]})]})]})]}),(0,r.jsxs)("div",{className:"mt-8 p-4 bg-gray-50 border border-gray-200 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"隐私保护说明"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,r.jsx)("li",{children:"• 所有数据仅保存在你的浏览器本地存储中"}),(0,r.jsx)("li",{children:"• 我们不会收集或上传你的个人信息"}),(0,r.jsx)("li",{children:"• 你可以随时导出或删除自己的数据"}),(0,r.jsx)("li",{children:"• 清除浏览器数据会同时删除游戏记录"})]})]})]})]}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"})})}},622:function(e,t,s){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=s(2265),a=Symbol.for("react.element"),l=Symbol.for("react.fragment"),c=Object.prototype.hasOwnProperty,n=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,i={key:!0,ref:!0,__self:!0,__source:!0};function q(e,t,s){var r,l={},d=null,x=null;for(r in void 0!==s&&(d=""+s),void 0!==t.key&&(d=""+t.key),void 0!==t.ref&&(x=t.ref),t)c.call(t,r)&&!i.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===l[r]&&(l[r]=t[r]);return{$$typeof:a,type:e,key:d,ref:x,props:l,_owner:n.current}}t.Fragment=l,t.jsx=q,t.jsxs=q},7437:function(e,t,s){"use strict";e.exports=s(622)},1396:function(e,t,s){e.exports=s(8326)},4033:function(e,t,s){e.exports=s(94)}},function(e){e.O(0,[326,971,472,744],function(){return e(e.s=4622)}),_N_E=e.O()}]);