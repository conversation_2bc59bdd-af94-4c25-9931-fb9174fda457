(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[61],{2898:function(e,t,l){"use strict";l.d(t,{Z:function(){return createLucideIcon}});var r=l(2265),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let toKebabCase=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),createLucideIcon=(e,t)=>{let l=(0,r.forwardRef)(({color:l="currentColor",size:i=24,strokeWidth:s=2,absoluteStrokeWidth:n,className:c="",children:o,...d},x)=>(0,r.createElement)("svg",{ref:x,...a,width:i,height:i,stroke:l,strokeWidth:n?24*Number(s)/Number(i):s,className:["lucide",`lucide-${toKebabCase(e)}`,c].join(" "),...d},[...t.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(o)?o:[o]]));return l.displayName=`${e}`,l}},3067:function(e,t,l){"use strict";l.d(t,{Z:function(){return a}});var r=l(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5817:function(e,t,l){"use strict";l.d(t,{Z:function(){return a}});var r=l(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},597:function(e,t,l){"use strict";l.d(t,{Z:function(){return a}});var r=l(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",key:"1xcu5"}],["circle",{cx:"17.5",cy:"10.5",r:".5",key:"736e4u"}],["circle",{cx:"8.5",cy:"7.5",r:".5",key:"clrty"}],["circle",{cx:"6.5",cy:"12.5",r:".5",key:"1s4xz9"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]])},9764:function(e,t,l){"use strict";l.d(t,{Z:function(){return a}});var r=l(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},5505:function(e,t,l){Promise.resolve().then(l.bind(l,9615))},9615:function(e,t,l){"use strict";l.r(t),l.d(t,{default:function(){return DemoSharePage}});var r=l(7437),a=l(2265),i=l(3067),s=l(9764),n=l(597),c=l(5817),o=l(1396),d=l.n(o),x=l(4033);function DemoSharePage(){let e=(0,x.useRouter)(),t=(0,a.useRef)(null),[l,o]=(0,a.useState)(null),[f,m]=(0,a.useState)("classic"),[h,u]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let t=localStorage.getItem("demoResult");if(!t){e.push("/demo-quiz");return}o(JSON.parse(t))},[e]);let formatTime=e=>"".concat(Math.floor(e/60),":").concat((e%60).toString().padStart(2,"0")),generatePoster=async()=>{if(!t.current||!l)return;u(!0);let e=t.current,r=e.getContext("2d");if(r){switch(e.width=600,e.height=800,f){case"classic":drawClassicTemplate(r,e.width,e.height);break;case"modern":drawModernTemplate(r,e.width,e.height);break;case"minimal":drawMinimalTemplate(r,e.width,e.height)}u(!1)}},drawClassicTemplate=(e,t,r)=>{let a=e.createLinearGradient(0,0,0,r);a.addColorStop(0,"#3B82F6"),a.addColorStop(1,"#1E40AF"),e.fillStyle=a,e.fillRect(0,0,t,r),e.fillStyle="#FFFFFF",e.font="bold 36px Arial",e.textAlign="center",e.fillText("防诈挑战游戏",t/2,80),e.font="bold 24px Arial",e.fillText("免费体验版",t/2,120),e.fillStyle="rgba(255, 255, 255, 0.95)",e.roundRect(50,180,t-100,350,20),e.fill(),e.fillStyle="#1F2937",e.font="bold 48px Arial",e.fillText("".concat(l.score,"/").concat(l.total),t/2,280),e.font="24px Arial",e.fillText("正确题数",t/2,310),e.font="bold 32px Arial",e.fillText("".concat(Math.round(l.score/l.total*100),"%"),t/2,370),e.font="20px Arial",e.fillText("正确率",t/2,400),e.font="bold 24px Arial",e.fillText(formatTime(l.timeUsed),t/2,450),e.font="18px Arial",e.fillText("用时",t/2,475),e.font="18px Arial",e.fillStyle="#6B7280",e.fillText(new Date(l.completedAt).toLocaleDateString(),t/2,510),e.fillStyle="#FFFFFF",e.font="20px Arial",e.fillText("提升防诈骗意识，保护自己远离陷阱",t/2,650),e.font="16px Arial",e.fillText("扫码体验完整版功能",t/2,700)},drawModernTemplate=(e,t,r)=>{e.fillStyle="#F8FAFC",e.fillRect(0,0,t,r);let a=e.createLinearGradient(0,0,t,0);a.addColorStop(0,"#8B5CF6"),a.addColorStop(1,"#EC4899"),e.fillStyle=a,e.fillRect(0,0,t,150),e.fillStyle="#FFFFFF",e.font="bold 32px Arial",e.textAlign="center",e.fillText("\uD83D\uDEE1️ 防诈挑战成绩",t/2,60),e.fillText("(体验版)",t/2,110),e.fillStyle="#FFFFFF",e.shadowColor="rgba(0, 0, 0, 0.1)",e.shadowBlur=20,e.roundRect(40,200,t-80,300,15),e.fill(),e.shadowBlur=0,e.fillStyle="#1F2937",e.font="bold 56px Arial",e.fillText("".concat(l.score,"/").concat(l.total),t/2,300),e.font="24px Arial",e.fillStyle="#6B7280",e.fillText("正确题数",t/2,330),e.fillStyle="#3B82F6",e.font="bold 28px Arial",e.fillText("".concat(Math.round(l.score/l.total*100),"%"),t/2,390),e.fillStyle="#6B7280",e.font="20px Arial",e.fillText("正确率",t/2,415),e.fillStyle="#8B5CF6",e.font="bold 22px Arial",e.fillText(formatTime(l.timeUsed),t/2,460),e.fillStyle="#6B7280",e.font="18px Arial",e.fillText("用时",t/2,485)},drawMinimalTemplate=(e,t,r)=>{e.fillStyle="#FFFFFF",e.fillRect(0,0,t,r),e.strokeStyle="#E5E7EB",e.lineWidth=2,e.strokeRect(20,20,t-40,r-40),e.fillStyle="#1F2937",e.font="bold 28px Arial",e.textAlign="center",e.fillText("防诈挑战成绩单",t/2,100),e.font="20px Arial",e.fillStyle="#6B7280",e.fillText("(免费体验版)",t/2,130),e.strokeStyle="#D1D5DB",e.lineWidth=1,e.beginPath(),e.moveTo(80,160),e.lineTo(t-80,160),e.stroke(),e.font="bold 48px Arial",e.fillStyle="#059669",e.fillText("".concat(l.score,"/").concat(l.total),t/2,250),e.font="20px Arial",e.fillStyle="#6B7280",e.fillText("正确题数",t/2,280),e.font="bold 32px Arial",e.fillStyle="#3B82F6",e.fillText("".concat(Math.round(l.score/l.total*100),"%"),t/2,340),e.font="18px Arial",e.fillStyle="#6B7280",e.fillText("正确率",t/2,365),e.font="16px Arial",e.fillText(new Date(l.completedAt).toLocaleDateString(),t/2,450),e.font="18px Arial",e.fillStyle="#1F2937",e.fillText("继续学习，提升防诈骗能力",t/2,550)},sharePoster=async()=>{if(t.current)try{let e=await new Promise(e=>{t.current.toBlob(t=>{e(t)})});if(navigator.share){let t=new File([e],"score.png",{type:"image/png"});await navigator.share({title:"我的防诈挑战体验成绩",text:"我在防诈挑战中答对了".concat(l.score,"/").concat(l.total,"题！一起来体验吧！"),files:[t]})}else await navigator.clipboard.writeText(window.location.origin),alert("链接已复制到剪贴板")}catch(e){console.error("分享失败:",e),alert("分享失败，请尝试下载图片手动分享")}};return((0,a.useEffect)(()=>{l&&generatePoster()},[l,f]),l)?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-4",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b mb-6",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(d(),{href:"/demo-quiz",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(i.Z,{className:"w-5 h-5 text-gray-600"})}),(0,r.jsx)(s.Z,{className:"w-8 h-8 text-primary-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"分享体验成绩"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"生成精美的成绩海报"})]})]})})}),(0,r.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"card",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,r.jsx)(n.Z,{className:"w-5 h-5 text-primary-600"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"选择模板"})]}),(0,r.jsx)("div",{className:"grid grid-cols-3 gap-3",children:[{id:"classic",name:"经典",desc:"蓝色渐变"},{id:"modern",name:"现代",desc:"彩色卡片"},{id:"minimal",name:"简约",desc:"黑白风格"}].map(e=>(0,r.jsxs)("button",{onClick:()=>m(e.id),className:"p-3 border-2 rounded-lg text-center transition-all ".concat(f===e.id?"border-primary-500 bg-primary-50":"border-gray-200 hover:border-primary-300"),children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsx)("div",{className:"text-xs text-gray-600",children:e.desc})]},e.id))})]}),(0,r.jsxs)("div",{className:"card",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"体验成绩"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"正确题数"}),(0,r.jsxs)("span",{className:"font-semibold text-primary-600",children:[l.score,"/",l.total]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"正确率"}),(0,r.jsxs)("span",{className:"font-semibold text-success-600",children:[Math.round(l.score/l.total*100),"%"]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"用时"}),(0,r.jsx)("span",{className:"font-semibold text-blue-600",children:formatTime(l.timeUsed)})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"完成时间"}),(0,r.jsx)("span",{className:"font-semibold text-gray-900",children:new Date(l.completedAt).toLocaleDateString()})]})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("button",{onClick:()=>{if(!t.current)return;let e=document.createElement("a");e.download="防诈挑战体验成绩-".concat(new Date().toLocaleDateString(),".png"),e.href=t.current.toDataURL(),e.click()},disabled:h,className:"w-full btn-primary flex items-center justify-center gap-2 disabled:opacity-50",children:[(0,r.jsx)(c.Z,{className:"w-5 h-5"}),"下载海报"]}),(0,r.jsxs)("button",{onClick:sharePoster,disabled:h,className:"w-full btn-secondary flex items-center justify-center gap-2 disabled:opacity-50",children:[(0,r.jsx)(s.Z,{className:"w-5 h-5"}),"分享海报"]})]}),(0,r.jsxs)("div",{className:"card bg-gradient-to-r from-primary-500 to-purple-600 text-white",children:[(0,r.jsx)("h3",{className:"text-lg font-bold mb-2",children:"想要更多功能？"}),(0,r.jsx)("p",{className:"text-primary-100 mb-4 text-sm",children:"注册完整账户，解锁每日挑战、排行榜、社区交流等功能！"}),(0,r.jsx)(d(),{href:"/login",className:"btn-primary bg-white text-primary-600 hover:bg-gray-100 text-sm",children:"立即注册"})]})]}),(0,r.jsxs)("div",{className:"card",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"海报预览"}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("canvas",{ref:t,className:"max-w-full h-auto border border-gray-200 rounded-lg shadow-lg",style:{maxHeight:"500px"}}),h&&(0,r.jsx)("div",{className:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"})})]})})]})]})})]}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"})})}},622:function(e,t,l){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=l(2265),a=Symbol.for("react.element"),i=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,n=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function q(e,t,l){var r,i={},o=null,d=null;for(r in void 0!==l&&(o=""+l),void 0!==t.key&&(o=""+t.key),void 0!==t.ref&&(d=t.ref),t)s.call(t,r)&&!c.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===i[r]&&(i[r]=t[r]);return{$$typeof:a,type:e,key:o,ref:d,props:i,_owner:n.current}}t.Fragment=i,t.jsx=q,t.jsxs=q},7437:function(e,t,l){"use strict";e.exports=l(622)},1396:function(e,t,l){e.exports=l(8326)},4033:function(e,t,l){e.exports=l(94)}},function(e){e.O(0,[326,971,472,744],function(){return e(e.s=5505)}),_N_E=e.O()}]);