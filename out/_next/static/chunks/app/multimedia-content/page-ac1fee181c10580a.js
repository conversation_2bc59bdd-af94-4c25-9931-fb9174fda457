(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[463],{2898:function(e,t,s){"use strict";s.d(t,{Z:function(){return createLucideIcon}});var r=s(2265),i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let toKebabCase=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),createLucideIcon=(e,t)=>{let s=(0,r.forwardRef)(({color:s="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:d="",children:c,...o},x)=>(0,r.createElement)("svg",{ref:x,...i,width:a,height:a,stroke:s,strokeWidth:n?24*Number(l)/Number(a):l,className:["lucide",`lucide-${toKebabCase(e)}`,d].join(" "),...o},[...t.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(c)?c:[c]]));return s.displayName=`${e}`,s}},3067:function(e,t,s){"use strict";s.d(t,{Z:function(){return i}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r.Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9865:function(e,t,s){"use strict";s.d(t,{Z:function(){return i}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r.Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},4900:function(e,t,s){"use strict";s.d(t,{Z:function(){return i}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r.Z)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},6618:function(e,t,s){Promise.resolve().then(s.bind(s,8735))},8735:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return MultimediaContentPage}});var r=s(7437),i=s(2265),a=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,a.Z)("Video",[["path",{d:"m22 8-6 4 6 4V8Z",key:"50v9me"}],["rect",{width:"14",height:"12",x:"2",y:"6",rx:"2",ry:"2",key:"1rqjg6"}]]),n=(0,a.Z)("Headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]]),d=(0,a.Z)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),c=(0,a.Z)("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);var o=s(3067),x=s(9865),m=s(4900),h=s(1396),u=s.n(h);function MultimediaContentPage(){let[e,t]=(0,i.useState)("video"),s={video:{title:"视频教学",icon:(0,r.jsx)(l,{className:"w-6 h-6"}),description:"生动的视频案例和专家讲解",content:[{title:"真实案例重现：大学生求职诈骗",duration:"8:32",views:"12.4K",description:"通过情景再现的方式，展示求职诈骗的完整过程和识别要点",thumbnail:"\uD83C\uDFAD",tags:["求职骗局","案例重现","专家解析"]},{title:"防诈骗专家访谈：如何识别网贷陷阱",duration:"15:20",views:"8.7K",description:"邀请反诈专家深度解析网贷诈骗的常见套路和防范方法",thumbnail:"\uD83D\uDC68‍\uD83D\uDCBC",tags:["专家访谈","网贷陷阱","深度解析"]},{title:"动画科普：电信诈骗的演变历程",duration:"6:45",views:"15.2K",description:"用动画形式展示电信诈骗从传统到现代的发展变化",thumbnail:"\uD83C\uDFA8",tags:["动画科普","电信诈骗","历史演变"]}]},audio:{title:"音频内容",icon:(0,r.jsx)(n,{className:"w-6 h-6"}),description:"便于随时收听的音频课程和播客",content:[{title:"防诈骗每日一听：租房安全指南",duration:"12:15",views:"6.8K",description:"每日更新的防诈骗小贴士，今日主题：租房安全",thumbnail:"\uD83C\uDFE0",tags:["每日更新","租房安全","实用指南"]},{title:"真实受害者访谈：我是如何被培训诈骗的",duration:"25:30",views:"9.3K",description:"受害者亲述被骗经历，分享血泪教训",thumbnail:"\uD83C\uDF99️",tags:["真实案例","受害者访谈","培训诈骗"]},{title:"法律专家解读：诈骗案件的法律后果",duration:"18:45",views:"4.2K",description:"法律专家详解诈骗相关法律条文和判决案例",thumbnail:"⚖️",tags:["法律解读","专家观点","案例分析"]}]},interactive:{title:"互动体验",icon:(0,r.jsx)(d,{className:"w-6 h-6"}),description:"沉浸式的互动学习体验",content:[{title:"VR 虚拟现实：身临其境体验诈骗现场",duration:"体验时长 10-15分钟",views:"3.1K",description:"通过 VR 技术让用户身临其境地体验各种诈骗场景",thumbnail:"\uD83E\uDD7D",tags:["VR体验","沉浸式","场景模拟"]},{title:"角色扮演：我是反诈警察",duration:"互动游戏",views:"7.5K",description:"扮演反诈警察，处理各种诈骗案件，学习专业识别技巧",thumbnail:"\uD83D\uDC6E‍♂️",tags:["角色扮演","游戏化","专业技能"]},{title:"模拟对话：与诈骗分子的智斗",duration:"对话练习",views:"5.9K",description:"模拟与诈骗分子的对话，练习应对技巧和话术",thumbnail:"\uD83D\uDCAC",tags:["对话模拟","应对技巧","实战练习"]}]},live:{title:"直播讲座",icon:(0,r.jsx)(c,{className:"w-6 h-6"}),description:"实时互动的专家讲座和答疑",content:[{title:"本周直播：大学生防诈骗专题讲座",duration:"每周三 19:00",views:"预约 2.3K",description:"邀请反诈专家和高校老师，针对大学生群体的防诈骗教育",thumbnail:"\uD83D\uDCFA",tags:["定期直播","专家讲座","互动答疑"]},{title:"案例分析直播：最新诈骗手段解析",duration:"每月第二个周五",views:"预约 1.8K",description:"分析当月最新出现的诈骗手段和防范方法",thumbnail:"\uD83D\uDD0D",tags:["案例分析","最新动态","实时更新"]},{title:"互动问答：你问我答防诈骗",duration:"不定期",views:"关注 4.7K",description:"用户提问，专家实时解答各种防诈骗相关问题",thumbnail:"❓",tags:["互动问答","实时解答","用户参与"]}]}},a=s[e];return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-blue-100",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(u(),{href:"/dashboard",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(o.Z,{className:"w-5 h-5 text-gray-600"})}),(0,r.jsx)(x.Z,{className:"w-8 h-8 text-green-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"多媒体内容中心"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"丰富的视听学习资源"})]})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"card mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"内容分类"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:Object.entries(s).map(s=>{let[i,a]=s;return(0,r.jsxs)("button",{onClick:()=>t(i),className:"p-4 rounded-lg border-2 text-left transition-all ".concat(e===i?"border-green-500 bg-green-50":"border-gray-200 hover:border-green-300"),children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,r.jsx)("div",{className:"p-2 rounded-lg ".concat(e===i?"bg-green-100":"bg-gray-100"),children:a.icon}),(0,r.jsx)("h3",{className:"font-semibold text-gray-900",children:a.title})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:a.description})]},i)})})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,r.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:a.icon}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:a.title}),(0,r.jsx)("p",{className:"text-gray-600",children:a.description})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:a.content.map((e,t)=>(0,r.jsxs)("div",{className:"card hover:shadow-lg transition-shadow",children:[(0,r.jsxs)("div",{className:"relative bg-gray-100 rounded-lg mb-4 h-48 flex items-center justify-center",children:[(0,r.jsx)("div",{className:"text-6xl",children:e.thumbnail}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity",children:(0,r.jsx)("button",{className:"bg-white/90 rounded-full p-3 hover:bg-white transition-colors",children:(0,r.jsx)(m.Z,{className:"w-6 h-6 text-gray-800"})})}),(0,r.jsx)("div",{className:"absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded",children:e.duration})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900 line-clamp-2",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 line-clamp-3",children:e.description}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:e.tags.map((e,t)=>(0,r.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full",children:e},t))}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500",children:[(0,r.jsxs)("span",{children:[e.views," 观看"]}),(0,r.jsx)("button",{className:"text-green-600 hover:text-green-700 font-medium",children:"立即观看"})]})]})]},t))})]}),(0,r.jsxs)("div",{className:"card mt-8 bg-gradient-to-r from-green-500 to-blue-600 text-white",children:[(0,r.jsx)("h3",{className:"text-xl font-bold mb-4",children:"\uD83C\uDFAC 多媒体学习的优势"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-2",children:"视听结合，印象深刻"}),(0,r.jsx)("p",{className:"text-green-100 text-sm",children:"通过视频、音频等多种形式，让防诈骗知识更加生动有趣，提高学习效果。"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-2",children:"真实案例，警示教育"}),(0,r.jsx)("p",{className:"text-green-100 text-sm",children:"基于真实案例制作内容，让用户深刻理解诈骗的危害和防范的重要性。"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-2",children:"专家指导，权威可信"}),(0,r.jsx)("p",{className:"text-green-100 text-sm",children:"邀请反诈专家、法律专家等权威人士，确保内容的专业性和准确性。"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-2",children:"互动体验，身临其境"}),(0,r.jsx)("p",{className:"text-green-100 text-sm",children:"通过 VR、角色扮演等互动方式，让用户在实践中掌握防诈骗技能。"})]})]})]}),(0,r.jsxs)("div",{className:"card mt-6 text-center",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"\uD83D\uDCCB 开发计划"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"多媒体内容正在制作中，我们正在与专家团队合作，打造高质量的教育内容。"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"15+"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"视频制作中"})]}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"8+"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"音频录制中"})]}),(0,r.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:"5+"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"互动体验开发中"})]}),(0,r.jsxs)("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:"2+"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"直播栏目筹备中"})]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)("button",{className:"btn-primary",disabled:!0,children:"订阅更新通知 (开发中)"}),(0,r.jsx)(u(),{href:"/dashboard",className:"btn-secondary",children:"返回主页"})]})]})]})]})}},622:function(e,t,s){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=s(2265),i=Symbol.for("react.element"),a=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,n=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,d={key:!0,ref:!0,__self:!0,__source:!0};function q(e,t,s){var r,a={},c=null,o=null;for(r in void 0!==s&&(c=""+s),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(o=t.ref),t)l.call(t,r)&&!d.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===a[r]&&(a[r]=t[r]);return{$$typeof:i,type:e,key:c,ref:o,props:a,_owner:n.current}}t.Fragment=a,t.jsx=q,t.jsxs=q},7437:function(e,t,s){"use strict";e.exports=s(622)},1396:function(e,t,s){e.exports=s(8326)}},function(e){e.O(0,[326,971,472,744],function(){return e(e.s=6618)}),_N_E=e.O()}]);