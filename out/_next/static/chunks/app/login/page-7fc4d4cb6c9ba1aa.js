(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[626],{2898:function(e,t,n){"use strict";n.d(t,{Z:function(){return createLucideIcon}});var r=n(2265),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let toKebabCase=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),createLucideIcon=(e,t)=>{let n=(0,r.forwardRef)(({color:n="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:c="",children:d,...o},m)=>(0,r.createElement)("svg",{ref:m,...a,width:s,height:s,stroke:n,strokeWidth:l?24*Number(i)/Number(s):i,className:["lucide",`lucide-${toKebabCase(e)}`,c].join(" "),...o},[...t.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(d)?d:[d]]));return n.displayName=`${e}`,n}},8291:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});var r=n(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},9036:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});var r=n(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},7972:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});var r=n(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},7087:function(e,t,n){Promise.resolve().then(n.bind(n,9665))},9665:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return LoginPage}});var r=n(7437),a=n(2265),s=n(9036),i=n(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,i.Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),c=(0,i.Z)("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]]);var d=n(7972),o=n(8291),m=n(1396),u=n.n(m),x=n(4033);function LoginPage(){let e=(0,x.useRouter)(),[t,n]=(0,a.useState)({inviteCode:"",nickname:""}),[i,m]=(0,a.useState)({}),[h,p]=(0,a.useState)(!1),handleInputChange=e=>{let{name:t,value:r}=e.target;n(e=>({...e,[t]:r})),i[t]&&m(e=>({...e,[t]:""}))},validateForm=()=>{let e={};return t.inviteCode.trim()?t.inviteCode.length<4&&(e.inviteCode="邀请码至少需要4位"):e.inviteCode="请输入邀请码",t.nickname.trim()?t.nickname.length<2?e.nickname="昵称至少需要2个字符":t.nickname.length>20&&(e.nickname="昵称不能超过20个字符"):e.nickname="请输入昵称",m(e),0===Object.keys(e).length},handleSubmit=async n=>{if(n.preventDefault(),validateForm()){p(!0);try{await new Promise(e=>setTimeout(e,1e3));let n={id:"user_".concat(Date.now()),inviteCode:t.inviteCode,nickname:t.nickname,createdAt:new Date().toISOString(),contributionScore:0};localStorage.setItem("userData",JSON.stringify(n));let r=localStorage.getItem("starterCompleted");r?e.push("/dashboard"):e.push("/starter-quiz")}catch(e){m({general:"登录失败，请重试"})}finally{p(!1)}}};return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"flex justify-center mb-6",children:(0,r.jsx)("div",{className:"p-4 bg-primary-100 rounded-full",children:(0,r.jsx)(s.Z,{className:"w-12 h-12 text-primary-600"})})}),(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"欢迎参与防诈挑战"}),(0,r.jsx)("p",{className:"text-gray-600",children:"使用邀请码和昵称匿名登录，开始你的防诈骗学习之旅"})]}),(0,r.jsx)("div",{className:"card",children:(0,r.jsxs)("form",{onSubmit:handleSubmit,className:"space-y-6",children:[i.general&&(0,r.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-danger-50 border border-danger-200 rounded-lg text-danger-700",children:[(0,r.jsx)(l,{className:"w-5 h-5"}),(0,r.jsx)("span",{className:"text-sm",children:i.general})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"inviteCode",className:"block text-sm font-medium text-gray-700 mb-2",children:"邀请码 (A)"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(c,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{id:"inviteCode",name:"inviteCode",type:"text",value:t.inviteCode,onChange:handleInputChange,className:"input-field pl-10 ".concat(i.inviteCode?"border-danger-300 focus:ring-danger-500":""),placeholder:"请输入邀请码"})]}),i.inviteCode&&(0,r.jsx)("p",{className:"mt-1 text-sm text-danger-600",children:i.inviteCode})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"nickname",className:"block text-sm font-medium text-gray-700 mb-2",children:"昵称 (B)"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(d.Z,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{id:"nickname",name:"nickname",type:"text",value:t.nickname,onChange:handleInputChange,className:"input-field pl-10 ".concat(i.nickname?"border-danger-300 focus:ring-danger-500":""),placeholder:"请输入你的昵称"})]}),i.nickname&&(0,r.jsx)("p",{className:"mt-1 text-sm text-danger-600",children:i.nickname})]}),(0,r.jsx)("button",{type:"submit",disabled:h,className:"w-full btn-primary flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed",children:h?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),"登录中..."]}):(0,r.jsxs)(r.Fragment,{children:["开始挑战",(0,r.jsx)(o.Z,{className:"w-5 h-5"})]})})]})}),(0,r.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)(l,{className:"w-5 h-5 text-blue-600 mt-0.5"}),(0,r.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,r.jsx)("p",{className:"font-medium mb-1",children:"匿名参与说明："}),(0,r.jsxs)("ul",{className:"space-y-1 text-blue-700",children:[(0,r.jsx)("li",{children:"• 邀请码：由主项目分配的参与凭证"}),(0,r.jsx)("li",{children:"• 昵称：你在游戏中的显示名称"}),(0,r.jsx)("li",{children:"• 所有数据仅保存在本地，保护你的隐私"})]})]})]})}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)(u(),{href:"/",className:"text-primary-600 hover:text-primary-700 text-sm font-medium",children:"← 返回首页"})})]})})}},622:function(e,t,n){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n(2265),a=Symbol.for("react.element"),s=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,l=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function q(e,t,n){var r,s={},d=null,o=null;for(r in void 0!==n&&(d=""+n),void 0!==t.key&&(d=""+t.key),void 0!==t.ref&&(o=t.ref),t)i.call(t,r)&&!c.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===s[r]&&(s[r]=t[r]);return{$$typeof:a,type:e,key:d,ref:o,props:s,_owner:l.current}}t.Fragment=s,t.jsx=q,t.jsxs=q},7437:function(e,t,n){"use strict";e.exports=n(622)},1396:function(e,t,n){e.exports=n(8326)},4033:function(e,t,n){e.exports=n(94)}},function(e){e.O(0,[326,971,472,744],function(){return e(e.s=7087)}),_N_E=e.O()}]);