(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[315],{2898:function(e,s,t){"use strict";t.d(s,{Z:function(){return createLucideIcon}});var r=t(2265),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let toKebabCase=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),createLucideIcon=(e,s)=>{let t=(0,r.forwardRef)(({color:t="currentColor",size:l=24,strokeWidth:c=2,absoluteStrokeWidth:i,className:n="",children:d,...o},x)=>(0,r.createElement)("svg",{ref:x,...a,width:l,height:l,stroke:t,strokeWidth:i?24*Number(c)/Number(l):c,className:["lucide",`lucide-${toKebabCase(e)}`,n].join(" "),...o},[...s.map(([e,s])=>(0,r.createElement)(e,s)),...Array.isArray(d)?d:[d]]));return t.displayName=`${e}`,t}},3067:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},2505:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]])},9865:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},6654:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},5790:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},8194:function(e,s,t){Promise.resolve().then(t.bind(t,6323))},6323:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return CategoryLearningPage}});var r=t(7437),a=t(2265),l=t(3067),c=t(9865),i=t(6654),n=t(5790),d=t(2505),o=t(1396),x=t.n(o);function CategoryLearningPage(){let[e,s]=(0,a.useState)("job-fraud"),t={"job-fraud":{title:"求职防骗",icon:"\uD83D\uDCBC",color:"blue",description:"识别虚假招聘、培训费诈骗等求职陷阱",questionCount:25,completionRate:78,averageScore:85,difficulty:"入门",learningPath:["基础概念：什么是求职诈骗","常见套路：培训费、保证金陷阱","识别技巧：如何辨别虚假招聘","防范措施：正确的求职方式","实战演练：真实案例分析"],recentUpdates:['新增"AI面试诈骗"专题',"更新最新求职诈骗案例","优化题目难度梯度"]},"rental-scam":{title:"租房安全",icon:"\uD83C\uDFE0",color:"green",description:"防范假房东、押金诈骗等租房风险",questionCount:18,completionRate:65,averageScore:79,difficulty:"入门",learningPath:["租房基础：正规租房流程","风险识别：假房东特征","合同要点：租房合同注意事项","资金安全：押金支付技巧","维权方法：遇到问题如何处理"],recentUpdates:['新增"长租公寓陷阱"内容',"更新租房合同模板","增加地区性案例分析"]},"loan-trap":{title:"金融理财",icon:"\uD83D\uDCB0",color:"yellow",description:"识别网贷陷阱、投资诈骗等金融风险",questionCount:32,completionRate:52,averageScore:72,difficulty:"进阶",learningPath:["金融基础：正规金融机构识别","网贷风险：套路贷识别技巧","投资陷阱：虚假理财产品","利率计算：真实成本分析","维权途径：金融纠纷处理"],recentUpdates:['新增"虚拟货币诈骗"专题',"更新最新金融监管政策","增加计算器工具"]},"training-scam":{title:"教育培训",icon:"\uD83C\uDF93",color:"purple",description:"防范培训诈骗、学历造假等教育陷阱",questionCount:15,completionRate:43,averageScore:68,difficulty:"进阶",learningPath:["培训机构：正规资质识别",'承诺陷阱："包过包就业"风险',"费用合理性：培训成本分析","合同条款：培训协议要点","维权指南：培训纠纷处理"],recentUpdates:['新增"在线教育诈骗"内容',"更新培训机构查询方法","增加退费维权指南"]}},o=t[e],m={totalLearners:12580,completedToday:456,averageImprovement:23.5,satisfactionRate:94.2};return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-blue-100",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(x(),{href:"/",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(l.Z,{className:"w-5 h-5 text-gray-600"})}),(0,r.jsx)(c.Z,{className:"w-8 h-8 text-success-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"分类学习"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"针对性学习，高效提升"})]})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"card mb-8 bg-gradient-to-r from-success-500 to-green-600 text-white",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"分类学习数据概览"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:m.totalLearners.toLocaleString()}),(0,r.jsx)("div",{className:"text-sm text-green-200",children:"总学习人数"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:m.completedToday}),(0,r.jsx)("div",{className:"text-sm text-green-200",children:"今日完成"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[m.averageImprovement,"%"]}),(0,r.jsx)("div",{className:"text-sm text-green-200",children:"平均提升"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[m.satisfactionRate,"%"]}),(0,r.jsx)("div",{className:"text-sm text-green-200",children:"满意度"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"card sticky top-8",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"学习分类"}),(0,r.jsx)("div",{className:"space-y-3",children:Object.entries(t).map(t=>{let[a,l]=t;return(0,r.jsx)("button",{onClick:()=>s(a),className:"w-full p-3 rounded-lg text-left transition-all ".concat(e===a?"bg-primary-100 border-primary-300 border-2":"bg-gray-50 hover:bg-gray-100 border-2 border-transparent"),children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("span",{className:"text-2xl",children:l.icon}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:l.title}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[l.questionCount," 道题目"]})]})]})},a)})})]})}),(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,r.jsxs)("div",{className:"card",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,r.jsx)("div",{className:"text-4xl",children:o.icon}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:o.title}),(0,r.jsx)("p",{className:"text-gray-600",children:o.description})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"text-center p-3 bg-blue-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-lg font-bold text-blue-600",children:o.questionCount}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"题目数量"})]}),(0,r.jsxs)("div",{className:"text-center p-3 bg-green-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"text-lg font-bold text-green-600",children:[o.completionRate,"%"]}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"完成率"})]}),(0,r.jsxs)("div",{className:"text-center p-3 bg-yellow-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"text-lg font-bold text-yellow-600",children:[o.averageScore,"%"]}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"平均分"})]}),(0,r.jsxs)("div",{className:"text-center p-3 bg-purple-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-lg font-bold text-purple-600",children:o.difficulty}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"难度等级"})]})]}),(0,r.jsxs)(x(),{href:"/category-practice?category=".concat(e),className:"btn-primary w-full flex items-center justify-center gap-2",children:[(0,r.jsx)(i.Z,{className:"w-5 h-5"}),"开始学习 ",o.title]})]}),(0,r.jsxs)("div",{className:"card",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:"学习路径"}),(0,r.jsx)("div",{className:"space-y-3",children:o.learningPath.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center text-primary-600 text-sm font-bold",children:s+1}),(0,r.jsx)("div",{className:"text-gray-700",children:e})]},s))})]}),(0,r.jsxs)("div",{className:"card",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:"最新更新"}),(0,r.jsx)("div",{className:"space-y-3",children:o.recentUpdates.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-blue-50 rounded-lg",children:[(0,r.jsx)(n.Z,{className:"w-5 h-5 text-blue-600"}),(0,r.jsx)("span",{className:"text-blue-800",children:e})]},s))})]})]})]}),(0,r.jsxs)("div",{className:"card mt-8",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-6",children:[(0,r.jsx)(d.Z,{className:"w-6 h-6 text-yellow-500"}),(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"分类学习排行榜"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[{nickname:"学习达人",score:98,category:"求职防骗",badge:"\uD83C\uDFC6"},{nickname:"防诈专家",score:96,category:"金融理财",badge:"\uD83E\uDD47"},{nickname:"安全卫士",score:95,category:"租房安全",badge:"\uD83E\uDD48"},{nickname:"谨慎学者",score:94,category:"教育培训",badge:"\uD83E\uDD49"}].map((e,s)=>(0,r.jsxs)("div",{className:"p-4 border border-gray-200 rounded-lg text-center",children:[(0,r.jsx)("div",{className:"text-2xl mb-2",children:e.badge}),(0,r.jsx)("div",{className:"font-semibold text-gray-900",children:e.nickname}),(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:e.category}),(0,r.jsxs)("div",{className:"text-lg font-bold text-primary-600",children:[e.score,"分"]})]},s))})]}),(0,r.jsxs)("div",{className:"card mt-8 bg-gradient-to-r from-purple-500 to-indigo-600 text-white",children:[(0,r.jsx)("h3",{className:"text-xl font-bold mb-4",children:"\uD83D\uDCA1 个性化学习建议"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-2",children:"新手推荐"}),(0,r.jsxs)("ul",{className:"text-purple-100 text-sm space-y-1",children:[(0,r.jsx)("li",{children:'• 从"求职防骗"开始，建立基础认知'}),(0,r.jsx)("li",{children:"• 每天学习1-2个分类，循序渐进"}),(0,r.jsx)("li",{children:"• 重点关注与自己相关的场景"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-2",children:"进阶技巧"}),(0,r.jsxs)("ul",{className:"text-purple-100 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• 交叉学习多个分类，形成知识网络"}),(0,r.jsx)("li",{children:"• 定期复习错题，巩固薄弱环节"}),(0,r.jsx)("li",{children:"• 结合实际案例，提升实战能力"})]})]})]})]})]})]})}},622:function(e,s,t){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=t(2265),a=Symbol.for("react.element"),l=Symbol.for("react.fragment"),c=Object.prototype.hasOwnProperty,i=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,n={key:!0,ref:!0,__self:!0,__source:!0};function q(e,s,t){var r,l={},d=null,o=null;for(r in void 0!==t&&(d=""+t),void 0!==s.key&&(d=""+s.key),void 0!==s.ref&&(o=s.ref),s)c.call(s,r)&&!n.hasOwnProperty(r)&&(l[r]=s[r]);if(e&&e.defaultProps)for(r in s=e.defaultProps)void 0===l[r]&&(l[r]=s[r]);return{$$typeof:a,type:e,key:d,ref:o,props:l,_owner:i.current}}s.Fragment=l,s.jsx=q,s.jsxs=q},7437:function(e,s,t){"use strict";e.exports=t(622)},1396:function(e,s,t){e.exports=t(8326)}},function(e){e.O(0,[326,971,472,744],function(){return e(e.s=8194)}),_N_E=e.O()}]);