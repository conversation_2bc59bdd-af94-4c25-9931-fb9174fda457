(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[514],{2898:function(e,s,t){"use strict";t.d(s,{Z:function(){return createLucideIcon}});var a=t(2265),r={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let toKebabCase=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),createLucideIcon=(e,s)=>{let t=(0,a.forwardRef)(({color:t="currentColor",size:l=24,strokeWidth:n=2,absoluteStrokeWidth:c,className:i="",children:d,...o},x)=>(0,a.createElement)("svg",{ref:x,...r,width:l,height:l,stroke:t,strokeWidth:c?24*Number(n)/Number(l):n,className:["lucide",`lucide-${toKebabCase(e)}`,i].join(" "),...o},[...s.map(([e,s])=>(0,a.createElement)(e,s)),...Array.isArray(d)?d:[d]]));return t.displayName=`${e}`,t}},3067:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});var a=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8203:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});var a=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},9168:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});var a=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]])},9163:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});var a=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Medal",[["path",{d:"M7.21 15 2.66 7.14a2 2 0 0 1 .13-2.2L4.4 2.8A2 2 0 0 1 6 2h12a2 2 0 0 1 1.6.8l1.6 2.14a2 2 0 0 1 .14 2.2L16.79 15",key:"143lza"}],["path",{d:"M11 12 5.12 2.2",key:"qhuxz6"}],["path",{d:"m13 12 5.88-9.8",key:"hbye0f"}],["path",{d:"M8 7h8",key:"i86dvs"}],["circle",{cx:"12",cy:"17",r:"5",key:"qbz8iq"}],["path",{d:"M12 18v-2h-.5",key:"fawc4q"}]])},5340:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});var a=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},5790:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});var a=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},8957:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});var a=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},98:function(e,s,t){Promise.resolve().then(t.bind(t,3593))},3593:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return LeaderboardPage}});var a=t(7437),r=t(2265),l=t(9168),n=t(9163),c=t(3067),i=t(8957),d=t(5340),o=t(5790),x=t(8203),m=t(1396),h=t.n(m);function LeaderboardPage(){let[e,s]=(0,r.useState)("weekly"),[t,m]=(0,r.useState)("all"),u={totalPlayers:15420,activeToday:892,averageScore:78.5,topScore:98},getRankIcon=e=>{switch(e){case 1:return(0,a.jsx)(l.Z,{className:"w-6 h-6 text-yellow-500"});case 2:return(0,a.jsx)(n.Z,{className:"w-6 h-6 text-gray-400"});case 3:return(0,a.jsx)(n.Z,{className:"w-6 h-6 text-amber-600"});default:return(0,a.jsx)("span",{className:"w-6 h-6 flex items-center justify-center text-gray-600 font-bold",children:e})}},getRarityColor=e=>{switch(e){case"legendary":return"text-purple-600 bg-purple-100";case"epic":return"text-orange-600 bg-orange-100";case"rare":return"text-blue-600 bg-blue-100";case"uncommon":return"text-green-600 bg-green-100";default:return"text-gray-600 bg-gray-100"}};return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-yellow-50 to-orange-100",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(h(),{href:"/",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,a.jsx)(c.Z,{className:"w-5 h-5 text-gray-600"})}),(0,a.jsx)(i.Z,{className:"w-8 h-8 text-yellow-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"排行榜"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"竞技学习，激发动力"})]})]})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"card mb-8 bg-gradient-to-r from-yellow-500 to-orange-600 text-white",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"竞技统计"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:u.totalPlayers.toLocaleString()}),(0,a.jsx)("div",{className:"text-sm text-yellow-200",children:"总参与人数"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:u.activeToday}),(0,a.jsx)("div",{className:"text-sm text-yellow-200",children:"今日活跃"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[u.averageScore,"%"]}),(0,a.jsx)("div",{className:"text-sm text-yellow-200",children:"平均分数"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[u.topScore,"%"]}),(0,a.jsx)("div",{className:"text-sm text-yellow-200",children:"最高分数"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)("div",{className:"card",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"总排行榜"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("select",{value:e,onChange:e=>s(e.target.value),className:"px-3 py-1 border border-gray-300 rounded-lg text-sm",children:[(0,a.jsx)("option",{value:"daily",children:"今日"}),(0,a.jsx)("option",{value:"weekly",children:"本周"}),(0,a.jsx)("option",{value:"monthly",children:"本月"}),(0,a.jsx)("option",{value:"all",children:"全部"})]}),(0,a.jsxs)("select",{value:t,onChange:e=>m(e.target.value),className:"px-3 py-1 border border-gray-300 rounded-lg text-sm",children:[(0,a.jsx)("option",{value:"all",children:"全部分类"}),(0,a.jsx)("option",{value:"job",children:"求职防骗"}),(0,a.jsx)("option",{value:"rental",children:"租房安全"}),(0,a.jsx)("option",{value:"finance",children:"金融理财"})]})]})]}),(0,a.jsx)("div",{className:"space-y-4",children:[{rank:1,nickname:"防诈大师",score:98,streak:15,category:"综合",avatar:"\uD83D\uDC51",level:"专家级",achievements:["连胜王","满分达人","知识渊博"],joinDate:"2024-01-01",totalQuestions:450},{rank:2,nickname:"安全卫士",score:96,streak:12,category:"求职防骗",avatar:"\uD83D\uDEE1️",level:"高级",achievements:["求职专家","稳定发挥"],joinDate:"2024-01-03",totalQuestions:380},{rank:3,nickname:"谨慎学者",score:95,streak:10,category:"金融理财",avatar:"\uD83D\uDCDA",level:"高级",achievements:["理财达人","学习之星"],joinDate:"2024-01-05",totalQuestions:320},{rank:4,nickname:"防骗达人",score:94,streak:8,category:"租房安全",avatar:"\uD83C\uDFE0",level:"中级",achievements:["租房专家"],joinDate:"2024-01-08",totalQuestions:280},{rank:5,nickname:"智慧守护",score:93,streak:7,category:"综合",avatar:"\uD83E\uDDE0",level:"中级",achievements:["全能选手"],joinDate:"2024-01-10",totalQuestions:250}].map(e=>(0,a.jsx)("div",{className:"p-4 rounded-lg border-2 transition-all ".concat(e.rank<=3?"bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200":"bg-gray-50 border-gray-200"),children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:getRankIcon(e.rank)}),(0,a.jsx)("div",{className:"text-3xl",children:e.avatar}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("span",{className:"font-bold text-gray-900",children:e.nickname}),(0,a.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full",children:e.level})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,a.jsxs)("span",{children:["分数: ",e.score,"%"]}),(0,a.jsxs)("span",{children:["连胜: ",e.streak]}),(0,a.jsxs)("span",{children:["专长: ",e.category]}),(0,a.jsxs)("span",{children:["题目: ",e.totalQuestions]})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1 mt-2",children:e.achievements.map((e,s)=>(0,a.jsx)("span",{className:"px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full",children:e},s))})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-primary-600",children:[e.score,"%"]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["#",e.rank]})]})]})},e.rank))})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"card",children:[(0,a.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"分类冠军"}),(0,a.jsx)("div",{className:"space-y-3",children:Object.entries({job:{name:"防诈小能手",score:97,avatar:"\uD83D\uDCBC"},rental:{name:"房产专家",score:95,avatar:"\uD83C\uDFE0"},finance:{name:"理财高手",score:96,avatar:"\uD83D\uDCB0"},education:{name:"教育达人",score:94,avatar:"\uD83C\uDF93"}}).map(e=>{let[s,t]=e;return(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("span",{className:"text-2xl",children:t.avatar}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:t.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[t.score,"% 正确率"]})]}),(0,a.jsx)(l.Z,{className:"w-4 h-4 text-yellow-500"})]},s)})})]}),(0,a.jsxs)("div",{className:"card",children:[(0,a.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"稀有成就"}),(0,a.jsx)("div",{className:"space-y-3",children:[{name:"连胜王",description:"连续答对15题",icon:"\uD83D\uDD25",rarity:"legendary"},{name:"满分达人",description:"获得满分10次",icon:"\uD83D\uDCAF",rarity:"epic"},{name:"知识渊博",description:"完成所有分类",icon:"\uD83D\uDCD6",rarity:"rare"},{name:"求职专家",description:"求职类题目90%正确率",icon:"\uD83D\uDCBC",rarity:"uncommon"},{name:"理财达人",description:"金融类题目90%正确率",icon:"\uD83D\uDCB0",rarity:"uncommon"}].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 border border-gray-200 rounded-lg",children:[(0,a.jsx)("span",{className:"text-xl",children:e.icon}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(getRarityColor(e.rarity)),children:e.rarity})]},s))})]}),(0,a.jsxs)("div",{className:"card bg-gradient-to-r from-blue-500 to-purple-600 text-white",children:[(0,a.jsx)("h3",{className:"text-lg font-bold mb-4",children:"我的排名"}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl mb-2",children:"\uD83C\uDFAF"}),(0,a.jsx)("div",{className:"text-2xl font-bold mb-1",children:"#42"}),(0,a.jsx)("div",{className:"text-sm text-blue-200 mb-3",children:"当前排名"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-bold",children:"85%"}),(0,a.jsx)("div",{className:"text-blue-200",children:"我的分数"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-bold",children:"5"}),(0,a.jsx)("div",{className:"text-blue-200",children:"连胜次数"})]})]}),(0,a.jsx)(h(),{href:"/demo-quiz",className:"btn-secondary mt-4 w-full",children:"继续挑战"})]})]})]})]}),(0,a.jsxs)("div",{className:"card mt-8",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-6",children:"竞技规则"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,a.jsx)(d.Z,{className:"w-6 h-6 text-blue-600"})}),(0,a.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"分数计算"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"基于正确率和答题速度综合计算"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,a.jsx)(o.Z,{className:"w-6 h-6 text-green-600"})}),(0,a.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"连胜奖励"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"连续答对可获得额外分数加成"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,a.jsx)(x.Z,{className:"w-6 h-6 text-purple-600"})}),(0,a.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"周期更新"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"排行榜每周重置，保持竞争活力"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,a.jsx)(i.Z,{className:"w-6 h-6 text-yellow-600"})}),(0,a.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"奖励机制"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"前三名可获得特殊称号和徽章"})]})]})]})]})]})}},622:function(e,s,t){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var a=t(2265),r=Symbol.for("react.element"),l=Symbol.for("react.fragment"),n=Object.prototype.hasOwnProperty,c=a.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,i={key:!0,ref:!0,__self:!0,__source:!0};function q(e,s,t){var a,l={},d=null,o=null;for(a in void 0!==t&&(d=""+t),void 0!==s.key&&(d=""+s.key),void 0!==s.ref&&(o=s.ref),s)n.call(s,a)&&!i.hasOwnProperty(a)&&(l[a]=s[a]);if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===l[a]&&(l[a]=s[a]);return{$$typeof:r,type:e,key:d,ref:o,props:l,_owner:c.current}}s.Fragment=l,s.jsx=q,s.jsxs=q},7437:function(e,s,t){"use strict";e.exports=t(622)},1396:function(e,s,t){e.exports=t(8326)}},function(e){e.O(0,[326,971,472,744],function(){return e(e.s=98)}),_N_E=e.O()}]);