(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[729],{2898:function(e,s,t){"use strict";t.d(s,{Z:function(){return createLucideIcon}});var r=t(2265),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let toKebabCase=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),createLucideIcon=(e,s)=>{let t=(0,r.forwardRef)(({color:t="currentColor",size:l=24,strokeWidth:n=2,absoluteStrokeWidth:i,className:c="",children:d,...o},x)=>(0,r.createElement)("svg",{ref:x,...a,width:l,height:l,stroke:t,strokeWidth:i?24*Number(n)/Number(l):n,className:["lucide",`lucide-${toKebabCase(e)}`,c].join(" "),...o},[...s.map(([e,s])=>(0,r.createElement)(e,s)),...Array.isArray(d)?d:[d]]));return t.displayName=`${e}`,t}},2894:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},3067:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9670:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2482:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},2176:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]])},9764:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},9521:function(e,s,t){Promise.resolve().then(t.bind(t,3689))},3689:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return RealCasesPage}});var r=t(7437),a=t(2265),l=t(3067),n=t(2894),i=t(2482),c=t(9670),d=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,d.Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);var x=t(2176),m=t(9764),h=t(1396),u=t.n(h);function RealCasesPage(){let[e,s]=(0,a.useState)("all"),[t,d]=(0,a.useState)("all"),h={totalCases:1247,verifiedCases:892,totalViews:156420,helpfulVotes:8934},getRiskColor=e=>{switch(e){case"high":return"text-red-600 bg-red-100";case"medium":return"text-yellow-600 bg-yellow-100";case"low":return"text-green-600 bg-green-100";default:return"text-gray-600 bg-gray-100"}},getRiskLabel=e=>{switch(e){case"high":return"高危";case"medium":return"中危";case"low":return"低危";default:return"未知"}},getCategoryLabel=e=>{switch(e){case"job":return"求职骗局";case"rental":return"租房陷阱";case"finance":return"金融诈骗";case"education":return"教育骗局";default:return"其他"}},g=[{id:"case001",title:'求职遇到"培训费"陷阱，差点被骗5000元',author:"小心求职者",category:"job",riskLevel:"high",amount:5e3,publishedAt:"2024-01-15",views:1250,likes:89,comments:23,verified:!0,tags:["培训费","虚假招聘","求职陷阱"],summary:"应聘时被要求先交培训费，幸好及时识破骗局",content:'我在某招聘网站上看到一个"高薪客服"的职位，月薪8000-12000，工作轻松。投递简历后很快收到面试通知。\n\n面试官说我很符合要求，但需要先参加公司的"专业培训"，培训费5000元，培训结束后会退还并正式入职。\n\n当时我觉得有些奇怪，但面试官说这是为了筛选真正有意向的员工。我差点就交钱了，幸好朋友提醒我这是典型的培训费诈骗。\n\n后来我查了这家公司，发现根本不存在，网站也是假的。现在想想真是后怕，差点就被骗了5000元。',lessons:["正规公司不会要求员工交培训费","入职前要核实公司真实性","遇到要求先交钱的工作要警惕","可以通过企查查等平台验证公司信息"],prevention:["通过官方渠道投递简历","面试时注意观察公司环境","不要轻易交纳任何费用","与朋友家人商量再做决定"]},{id:"case002",title:"租房遇到假房东，押金3000元打水漂",author:"租房小白",category:"rental",riskLevel:"medium",amount:3e3,publishedAt:"2024-01-14",views:980,likes:67,comments:18,verified:!0,tags:["假房东","押金诈骗","租房陷阱"],summary:"通过网络找房遇到假房东，交了押金后人就消失了",content:'在某租房APP上看到一套很便宜的房子，位置好价格低，我立刻联系了"房东"。\n\n"房东"说人在外地，不能现场看房，但可以先交押金锁定房源。他发了很多房子的照片，看起来很真实。\n\n我担心房子被别人租走，就通过微信转账交了3000元押金。约定第二天去看房签合同。\n\n结果第二天到了地址，发现根本联系不上"房东"，微信也被拉黑了。后来才知道那些照片都是从网上盗用的，我遇到了假房东。',lessons:["租房一定要实地看房","不要轻信网上的房源照片","交钱前要核实房东身份","通过正规中介或平台租房更安全"],prevention:["要求查看房产证和身份证","实地看房后再谈价格","通过银行转账留下记录","签订正式租房合同"]},{id:"case003",title:'网贷"砍头息"陷阱，实际年利率超过100%',author:"理财新手",category:"finance",riskLevel:"high",amount:1e4,publishedAt:"2024-01-13",views:1580,likes:124,comments:35,verified:!0,tags:["砍头息","高利贷","网贷陷阱"],summary:"急需用钱申请网贷，遇到砍头息陷阱，实际利率惊人",content:'因为急需1万元周转，我在网上找到一个贷款平台，声称"低息快贷，当天放款"。\n\n申请时平台说月利率只有1.5%，看起来很合理。但放款时发现实际到账只有8000元，平台说扣除了"服务费"、"手续费"等2000元。\n\n还款时我才发现，虽然借了1万元，但实际只拿到8000元，却要按1万元还款。算下来实际年利率超过了100%，这就是典型的"砍头息"。\n\n现在我深陷其中，每月还款压力巨大，真后悔当初没有仔细了解。',lessons:["砍头息是违法的高利贷行为","借款前要仔细计算实际利率","正规金融机构不会预扣费用","遇到资金困难要通过正当渠道解决"],prevention:["选择银行等正规金融机构","仔细阅读借款合同条款","计算真实的借款成本",'不要被"低息"广告迷惑']}].filter(s=>{let r="all"===e||s.category===e,a="all"===t||s.riskLevel===t;return r&&a});return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 to-orange-100",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(u(),{href:"/",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(l.Z,{className:"w-5 h-5 text-gray-600"})}),(0,r.jsx)(n.Z,{className:"w-8 h-8 text-red-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"真实案例"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"真实经历，警示教育"})]})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"card mb-8 bg-gradient-to-r from-red-500 to-orange-600 text-white",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"案例统计"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:h.totalCases}),(0,r.jsx)("div",{className:"text-sm text-red-200",children:"总案例数"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:h.verifiedCases}),(0,r.jsx)("div",{className:"text-sm text-red-200",children:"已验证"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:h.totalViews.toLocaleString()}),(0,r.jsx)("div",{className:"text-sm text-red-200",children:"总浏览量"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:h.helpfulVotes}),(0,r.jsx)("div",{className:"text-sm text-red-200",children:"有用投票"})]})]})]}),(0,r.jsx)("div",{className:"card mb-8",children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-4 items-center",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(i.Z,{className:"w-4 h-4 text-gray-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"筛选："})]}),(0,r.jsxs)("select",{value:e,onChange:e=>s(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg text-sm",children:[(0,r.jsx)("option",{value:"all",children:"全部分类"}),(0,r.jsx)("option",{value:"job",children:"求职骗局"}),(0,r.jsx)("option",{value:"rental",children:"租房陷阱"}),(0,r.jsx)("option",{value:"finance",children:"金融诈骗"}),(0,r.jsx)("option",{value:"education",children:"教育骗局"})]}),(0,r.jsxs)("select",{value:t,onChange:e=>d(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg text-sm",children:[(0,r.jsx)("option",{value:"all",children:"全部风险等级"}),(0,r.jsx)("option",{value:"high",children:"高危"}),(0,r.jsx)("option",{value:"medium",children:"中危"}),(0,r.jsx)("option",{value:"low",children:"低危"})]})]})}),(0,r.jsx)("div",{className:"space-y-6",children:g.map(e=>(0,r.jsx)("div",{className:"card hover:shadow-lg transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-start gap-4 mb-4",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(n.Z,{className:"w-6 h-6 text-red-500"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900",children:e.title}),e.verified&&(0,r.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full",children:"已验证"}),(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(getRiskColor(e.riskLevel)),children:getRiskLabel(e.riskLevel)})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600 mb-3",children:[(0,r.jsxs)("span",{children:["作者: ",e.author]}),(0,r.jsxs)("span",{children:["分类: ",getCategoryLabel(e.category)]}),(0,r.jsxs)("span",{children:["涉及金额: \xa5",e.amount.toLocaleString()]}),(0,r.jsxs)("span",{children:["发布: ",e.publishedAt]})]}),(0,r.jsx)("p",{className:"text-gray-700 mb-4",children:e.summary}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:e.tags.map((e,s)=>(0,r.jsxs)("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded",children:["#",e]},s))}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"案例详情"}),(0,r.jsx)("div",{className:"text-gray-700 text-sm whitespace-pre-line",children:e.content})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"bg-red-50 rounded-lg p-3",children:[(0,r.jsx)("h5",{className:"font-semibold text-red-900 mb-2",children:"⚠️ 经验教训"}),(0,r.jsx)("ul",{className:"text-red-800 text-sm space-y-1",children:e.lessons.map((e,s)=>(0,r.jsxs)("li",{children:["• ",e]},s))})]}),(0,r.jsxs)("div",{className:"bg-green-50 rounded-lg p-3",children:[(0,r.jsx)("h5",{className:"font-semibold text-green-900 mb-2",children:"\uD83D\uDEE1️ 防范措施"}),(0,r.jsx)("ul",{className:"text-green-800 text-sm space-y-1",children:e.prevention.map((e,s)=>(0,r.jsxs)("li",{children:["• ",e]},s))})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-gray-200",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(c.Z,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:e.views})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(o,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:e.likes})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(x.Z,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:e.comments})]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("button",{className:"flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-red-600 transition-colors",children:[(0,r.jsx)(o,{className:"w-4 h-4"}),"有用"]}),(0,r.jsxs)("button",{className:"flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-blue-600 transition-colors",children:[(0,r.jsx)(m.Z,{className:"w-4 h-4"}),"分享"]})]})]})]})]})},e.id))}),(0,r.jsxs)("div",{className:"card mt-8 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-center",children:[(0,r.jsx)("h3",{className:"text-xl font-bold mb-4",children:"分享你的经历，帮助更多人"}),(0,r.jsx)("p",{className:"text-blue-100 mb-6",children:"如果你也有防诈骗的真实经历，欢迎分享给大家，让更多人从中受益。"}),(0,r.jsxs)(u(),{href:"/demo-share",className:"btn-secondary inline-flex items-center gap-2",children:[(0,r.jsx)(m.Z,{className:"w-5 h-5"}),"分享我的经历"]})]})]})]})}},622:function(e,s,t){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=t(2265),a=Symbol.for("react.element"),l=Symbol.for("react.fragment"),n=Object.prototype.hasOwnProperty,i=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function q(e,s,t){var r,l={},d=null,o=null;for(r in void 0!==t&&(d=""+t),void 0!==s.key&&(d=""+s.key),void 0!==s.ref&&(o=s.ref),s)n.call(s,r)&&!c.hasOwnProperty(r)&&(l[r]=s[r]);if(e&&e.defaultProps)for(r in s=e.defaultProps)void 0===l[r]&&(l[r]=s[r]);return{$$typeof:a,type:e,key:d,ref:o,props:l,_owner:i.current}}s.Fragment=l,s.jsx=q,s.jsxs=q},7437:function(e,s,t){"use strict";e.exports=t(622)},1396:function(e,s,t){e.exports=t(8326)}},function(e){e.O(0,[326,971,472,744],function(){return e(e.s=9521)}),_N_E=e.O()}]);