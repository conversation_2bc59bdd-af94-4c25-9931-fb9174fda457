(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[129],{2898:function(e,s,t){"use strict";t.d(s,{Z:function(){return createLucideIcon}});var r=t(2265),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let toKebabCase=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),createLucideIcon=(e,s)=>{let t=(0,r.forwardRef)(({color:t="currentColor",size:c=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:n="",children:d,...x},m)=>(0,r.createElement)("svg",{ref:m,...a,width:c,height:c,stroke:t,strokeWidth:l?24*Number(i)/Number(c):i,className:["lucide",`lucide-${toKebabCase(e)}`,n].join(" "),...x},[...s.map(([e,s])=>(0,r.createElement)(e,s)),...Array.isArray(d)?d:[d]]));return t.displayName=`${e}`,t}},3067:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},3008:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},9670:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},9036:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},5750:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},9503:function(e,s,t){Promise.resolve().then(t.bind(t,1116))},1116:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return AnonymousFeaturePage}});var r=t(7437),a=t(2265),c=t(3067),i=t(9036),l=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,l.Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);var d=t(9670),x=t(5750),m=t(3008),o=t(1396),h=t.n(o);function AnonymousFeaturePage(){let[e,s]=(0,a.useState)(!1),t={totalUsers:15420,activeToday:892,averageScore:78.5,privacyRating:9.8};return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(h(),{href:"/",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(c.Z,{className:"w-5 h-5 text-gray-600"})}),(0,r.jsx)(i.Z,{className:"w-8 h-8 text-primary-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"匿名参与"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"保护隐私，安心学习"})]})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"card mb-8 bg-gradient-to-r from-primary-500 to-blue-600 text-white",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,r.jsx)("div",{className:"p-3 bg-white/20 rounded-lg",children:(0,r.jsx)(i.Z,{className:"w-8 h-8"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"隐私优先的学习体验"}),(0,r.jsx)("p",{className:"text-primary-100",children:"无需实名注册，通过邀请码+昵称的方式匿名参与，让你在保护隐私的同时安心学习防诈骗知识。"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t.totalUsers.toLocaleString()}),(0,r.jsx)("div",{className:"text-sm text-primary-200",children:"匿名用户"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t.activeToday}),(0,r.jsx)("div",{className:"text-sm text-primary-200",children:"今日活跃"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[t.averageScore,"%"]}),(0,r.jsx)("div",{className:"text-sm text-primary-200",children:"平均成绩"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[t.privacyRating,"/10"]}),(0,r.jsx)("div",{className:"text-sm text-primary-200",children:"隐私评分"})]})]})]}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"隐私保护特性"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[{title:"邀请码机制",description:"通过邀请码参与，无需提供真实身份信息",icon:"\uD83D\uDD11",details:"每个邀请码由系统随机生成，不与任何个人信息关联，确保参与者身份完全匿名。"},{title:"昵称系统",description:"自定义昵称显示，保护真实姓名隐私",icon:"\uD83D\uDC64",details:"用户可以使用任意昵称参与游戏，系统不会要求验证真实姓名，充分保护个人隐私。"},{title:"本地存储",description:"数据仅保存在本地浏览器，不上传服务器",icon:"\uD83D\uDCBE",details:"所有游戏数据、学习记录都保存在用户本地，随时可以清除，完全掌控自己的数据。"},{title:"无追踪设计",description:"不收集设备信息，不进行用户行为追踪",icon:"\uD83D\uDEAB",details:"系统设计遵循隐私优先原则，不收集IP地址、设备指纹等可识别信息。"}].map((e,s)=>(0,r.jsx)("div",{className:"card hover:shadow-lg transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-start gap-4",children:[(0,r.jsx)("div",{className:"text-3xl",children:e.icon}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.title}),(0,r.jsx)("p",{className:"text-gray-600 mb-3",children:e.description}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:e.details})]})]})},s))})]}),(0,r.jsxs)("div",{className:"card mb-8",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-6",children:"匿名参与流程"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,r.jsx)("span",{className:"text-primary-600 font-bold",children:"1"})}),(0,r.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"获取邀请码"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"通过官方渠道获取邀请码"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,r.jsx)("span",{className:"text-primary-600 font-bold",children:"2"})}),(0,r.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"设置昵称"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"选择一个喜欢的昵称"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,r.jsx)("span",{className:"text-primary-600 font-bold",children:"3"})}),(0,r.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"开始学习"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"立即开始防诈骗学习"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,r.jsx)("span",{className:"text-primary-600 font-bold",children:"4"})}),(0,r.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"数据掌控"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"随时导出或清除数据"})]})]})]}),(0,r.jsxs)("div",{className:"card mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"用户反馈"}),(0,r.jsxs)("button",{onClick:()=>s(!e),className:"flex items-center gap-2 text-primary-600 hover:text-primary-700",children:[e?(0,r.jsx)(n,{className:"w-4 h-4"}):(0,r.jsx)(d.Z,{className:"w-4 h-4"}),e?"隐藏详情":"显示详情"]})]}),(0,r.jsx)("div",{className:"space-y-4",children:[{nickname:"防诈小卫士",content:"很喜欢这种匿名参与的方式，可以放心学习防诈骗知识，不用担心个人信息泄露。",score:95,timeAgo:"2小时前"},{nickname:"谨慎学习者",content:"邀请码+昵称的设计很贴心，既能参与学习又保护隐私，这样的设计很人性化。",score:88,timeAgo:"5小时前"},{nickname:"安全意识强",content:"数据本地存储让我很放心，随时可以清除，完全掌控自己的学习数据。",score:92,timeAgo:"1天前"}].map((s,t)=>(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(x.Z,{className:"w-4 h-4 text-primary-600"})}),(0,r.jsx)("span",{className:"font-medium text-gray-900",children:s.nickname})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm text-gray-500",children:s.timeAgo}),(0,r.jsxs)("span",{className:"px-2 py-1 bg-success-100 text-success-700 text-xs rounded-full",children:[s.score,"分"]})]})]}),(0,r.jsx)("p",{className:"text-gray-700",children:s.content}),e&&(0,r.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center gap-4 text-xs text-gray-500",children:[(0,r.jsx)("span",{children:"✓ 已验证匿名用户"}),(0,r.jsx)("span",{children:"✓ 数据本地存储"}),(0,r.jsx)("span",{children:"✓ 隐私完全保护"})]})})]},t))})]}),(0,r.jsx)("div",{className:"card bg-green-50 border-green-200",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)(m.Z,{className:"w-6 h-6 text-green-600 mt-1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-bold text-green-900 mb-2",children:"我们的隐私承诺"}),(0,r.jsxs)("ul",{className:"text-green-800 space-y-2",children:[(0,r.jsx)("li",{children:"• 绝不收集用户真实身份信息"}),(0,r.jsx)("li",{children:"• 所有数据仅保存在用户本地设备"}),(0,r.jsx)("li",{children:"• 不进行任何形式的用户行为追踪"}),(0,r.jsx)("li",{children:"• 用户可随时完全删除所有数据"}),(0,r.jsx)("li",{children:"• 开源透明，接受社区监督"})]})]})]})}),(0,r.jsx)("div",{className:"text-center mt-8",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)(h(),{href:"/demo-quiz",className:"btn-primary",children:"立即体验匿名学习"}),(0,r.jsx)(h(),{href:"/login",className:"btn-secondary",children:"注册匿名账户"})]})})]})]})}},622:function(e,s,t){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=t(2265),a=Symbol.for("react.element"),c=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,l=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,n={key:!0,ref:!0,__self:!0,__source:!0};function q(e,s,t){var r,c={},d=null,x=null;for(r in void 0!==t&&(d=""+t),void 0!==s.key&&(d=""+s.key),void 0!==s.ref&&(x=s.ref),s)i.call(s,r)&&!n.hasOwnProperty(r)&&(c[r]=s[r]);if(e&&e.defaultProps)for(r in s=e.defaultProps)void 0===c[r]&&(c[r]=s[r]);return{$$typeof:a,type:e,key:d,ref:x,props:c,_owner:l.current}}s.Fragment=c,s.jsx=q,s.jsxs=q},7437:function(e,s,t){"use strict";e.exports=t(622)},1396:function(e,s,t){e.exports=t(8326)}},function(e){e.O(0,[326,971,472,744],function(){return e(e.s=9503)}),_N_E=e.O()}]);