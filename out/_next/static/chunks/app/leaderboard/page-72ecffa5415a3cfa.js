(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[173],{2898:function(e,t,a){"use strict";a.d(t,{Z:function(){return createLucideIcon}});var r=a(2265),s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let toKebabCase=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),createLucideIcon=(e,t)=>{let a=(0,r.forwardRef)(({color:a="currentColor",size:c=24,strokeWidth:n=2,absoluteStrokeWidth:l,className:i="",children:d,...o},m)=>(0,r.createElement)("svg",{ref:m,...s,width:c,height:c,stroke:a,strokeWidth:l?24*Number(n)/Number(c):n,className:["lucide",`lucide-${toKebabCase(e)}`,i].join(" "),...o},[...t.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(d)?d:[d]]));return a.displayName=`${e}`,a}},3067:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});var r=a(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r.Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},6141:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});var r=a(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r.Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},9168:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});var r=a(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r.Z)("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]])},2482:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});var r=a(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r.Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},9163:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});var r=a(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r.Z)("Medal",[["path",{d:"M7.21 15 2.66 7.14a2 2 0 0 1 .13-2.2L4.4 2.8A2 2 0 0 1 6 2h12a2 2 0 0 1 1.6.8l1.6 2.14a2 2 0 0 1 .14 2.2L16.79 15",key:"143lza"}],["path",{d:"M11 12 5.12 2.2",key:"qhuxz6"}],["path",{d:"m13 12 5.88-9.8",key:"hbye0f"}],["path",{d:"M8 7h8",key:"i86dvs"}],["circle",{cx:"12",cy:"17",r:"5",key:"qbz8iq"}],["path",{d:"M12 18v-2h-.5",key:"fawc4q"}]])},6654:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});var r=a(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r.Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},8957:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});var r=a(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r.Z)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},8712:function(e,t,a){Promise.resolve().then(a.bind(a,8392))},8392:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return LeaderboardPage}});var r=a(7437),s=a(2265),c=a(9168),n=a(9163),l=a(3067),i=a(8957),d=a(2482),o=a(6141),m=a(6654),x=a(1396),h=a.n(x),u=a(7809);let f=[{rank:1,nickname:"防诈达人",score:25,timeUsed:180,category:"job-fraud",date:"2024-01-15"},{rank:2,nickname:"安全小卫士",score:23,timeUsed:165,category:"telecom-fraud",date:"2024-01-15"},{rank:3,nickname:"智慧学子",score:22,timeUsed:190,category:"loan-trap",date:"2024-01-14"},{rank:4,nickname:"警觉青年",score:20,timeUsed:145,category:"rental-scam",date:"2024-01-14"},{rank:5,nickname:"反诈先锋",score:19,timeUsed:200,category:"training-scam",date:"2024-01-13"},{rank:6,nickname:"谨慎同学",score:18,timeUsed:175,category:"part-time-scam",date:"2024-01-13"},{rank:7,nickname:"明智选择",score:17,timeUsed:160,category:"fake-authority",date:"2024-01-12"},{rank:8,nickname:"理性思考",score:16,timeUsed:185,category:"job-fraud",date:"2024-01-12"},{rank:9,nickname:"细心观察",score:15,timeUsed:170,category:"telecom-fraud",date:"2024-01-11"},{rank:10,nickname:"冷静分析",score:14,timeUsed:195,category:"loan-trap",date:"2024-01-11"}];function LeaderboardPage(){let[e,t]=(0,s.useState)("all"),[a,x]=(0,s.useState)("all"),[y,g]=(0,s.useState)(f),p=y.filter(t=>{if("all"!==e&&t.category!==e)return!1;let r=new Date(t.date),s=new Date;switch(a){case"today":return r.toDateString()===s.toDateString();case"week":let c=new Date(s.getTime()-6048e5);return r>=c;case"month":let n=new Date(s.getTime()-2592e6);return r>=n;default:return!0}}),formatTime=e=>"".concat(Math.floor(e/60),":").concat((e%60).toString().padStart(2,"0")),getRankIcon=e=>{switch(e){case 1:return(0,r.jsx)(c.Z,{className:"w-6 h-6 text-yellow-500"});case 2:return(0,r.jsx)(n.Z,{className:"w-6 h-6 text-gray-400"});case 3:return(0,r.jsx)(n.Z,{className:"w-6 h-6 text-amber-600"});default:return(0,r.jsx)("div",{className:"w-6 h-6 flex items-center justify-center text-gray-500 font-bold",children:e})}};return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(h(),{href:"/dashboard",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(l.Z,{className:"w-5 h-5 text-gray-600"})}),(0,r.jsx)(i.Z,{className:"w-8 h-8 text-yellow-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"排行榜"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"查看挑战高手排名"})]})]})})})}),(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"card mb-8",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,r.jsx)(d.Z,{className:"w-4 h-4 inline mr-1"}),"诈骗类型"]}),(0,r.jsxs)("select",{value:e,onChange:e=>t(e.target.value),className:"input-field",children:[(0,r.jsx)("option",{value:"all",children:"全部类型"}),Object.entries(u.H).map(e=>{let[t,a]=e;return(0,r.jsx)("option",{value:t,children:a},t)})]})]}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,r.jsx)(o.Z,{className:"w-4 h-4 inline mr-1"}),"时间范围"]}),(0,r.jsxs)("select",{value:a,onChange:e=>x(e.target.value),className:"input-field",children:[(0,r.jsx)("option",{value:"all",children:"全部时间"}),(0,r.jsx)("option",{value:"today",children:"今日"}),(0,r.jsx)("option",{value:"week",children:"本周"}),(0,r.jsx)("option",{value:"month",children:"本月"})]})]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"card text-center",children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-primary-600 mb-2",children:p.length}),(0,r.jsx)("div",{className:"text-gray-600",children:"参与用户"})]}),(0,r.jsxs)("div",{className:"card text-center",children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-success-600 mb-2",children:p.length>0?Math.max(...p.map(e=>e.score)):0}),(0,r.jsx)("div",{className:"text-gray-600",children:"最高分数"})]}),(0,r.jsxs)("div",{className:"card text-center",children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:p.length>0?formatTime(Math.min(...p.map(e=>e.timeUsed))):"0:00"}),(0,r.jsx)("div",{className:"text-gray-600",children:"最快用时"})]})]}),(0,r.jsxs)("div",{className:"card",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-6",children:[(0,r.jsx)(i.Z,{className:"w-6 h-6 text-yellow-500"}),(0,r.jsxs)("h2",{className:"text-xl font-bold text-gray-900",children:["all"===e?"全部":u.H[e]," 排行榜"]})]}),0===p.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(m.Z,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-500",children:"暂无符合条件的排行数据"})]}):(0,r.jsx)("div",{className:"space-y-3",children:p.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-lg border-2 transition-all ".concat(e.rank<=3?"border-yellow-200 bg-yellow-50":"border-gray-200 bg-gray-50"),children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:getRankIcon(e.rank)}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-semibold text-gray-900",children:e.nickname}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[e.category&&u.H[e.category]," • ",e.date]})]})]}),(0,r.jsx)("div",{className:"text-right",children:(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-lg font-bold text-primary-600",children:e.score}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"题数"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-lg font-bold text-blue-600",children:formatTime(e.timeUsed)}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"用时"})]})]})})]},t))})]}),(0,r.jsx)("div",{className:"text-center mt-8",children:(0,r.jsxs)("div",{className:"card bg-gradient-to-r from-primary-500 to-purple-600 text-white",children:[(0,r.jsx)("h3",{className:"text-xl font-bold mb-2",children:"想要上榜吗？"}),(0,r.jsx)("p",{className:"text-primary-100 mb-4",children:"参与每日挑战，提升你的防诈骗能力，争取更好的排名！"}),(0,r.jsx)(h(),{href:"/challenge",className:"btn-primary bg-white text-primary-600 hover:bg-gray-100",children:"开始挑战"})]})})]})]})}},7809:function(e,t,a){"use strict";a.d(t,{H:function(){return r}});let r={"job-fraud":"求职骗局","rental-scam":"租房陷阱","loan-trap":"网贷陷阱","training-scam":"培训诈骗","telecom-fraud":"电信诈骗","fake-authority":"冒充公检法","part-time-scam":"兼职诈骗"}},622:function(e,t,a){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=a(2265),s=Symbol.for("react.element"),c=Symbol.for("react.fragment"),n=Object.prototype.hasOwnProperty,l=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,i={key:!0,ref:!0,__self:!0,__source:!0};function q(e,t,a){var r,c={},d=null,o=null;for(r in void 0!==a&&(d=""+a),void 0!==t.key&&(d=""+t.key),void 0!==t.ref&&(o=t.ref),t)n.call(t,r)&&!i.hasOwnProperty(r)&&(c[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===c[r]&&(c[r]=t[r]);return{$$typeof:s,type:e,key:d,ref:o,props:c,_owner:l.current}}t.Fragment=c,t.jsx=q,t.jsxs=q},7437:function(e,t,a){"use strict";e.exports=a(622)},1396:function(e,t,a){e.exports=a(8326)}},function(e){e.O(0,[326,971,472,744],function(){return e(e.s=8712)}),_N_E=e.O()}]);