<!DOCTYPE html><html lang="zh-CN"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/97115f26cb754c8c.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-69bfb055b70a1563.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-a0c76284e27c8afa.js" async="" crossorigin=""></script><script src="/_next/static/chunks/472-af003cc8e47f5b1f.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-52f18067e7ca02da.js" async="" crossorigin=""></script><script src="/_next/static/chunks/326-cf5b0283528485c7.js" async=""></script><script src="/_next/static/chunks/app/not-found-5c4df079dd57eb5a.js" async=""></script><script src="/_next/static/chunks/app/multimedia-content/page-ac1fee181c10580a.js" async=""></script><title>大学生防诈挑战游戏</title><meta name="description" content="通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱"/><meta name="author" content="防诈挑战游戏团队"/><meta name="keywords" content="防诈骗,大学生,安全教育,答题游戏"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body><div class="min-h-screen"><div class="min-h-screen bg-gradient-to-br from-green-50 to-blue-100"><header class="bg-white shadow-sm border-b"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4"><div class="flex items-center gap-3"><a class="p-2 hover:bg-gray-100 rounded-lg transition-colors" href="/dashboard/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left w-5 h-5 text-gray-600"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg></a><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-book-open w-8 h-8 text-green-600"><path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path><path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path></svg><div><h1 class="text-2xl font-bold text-gray-900">多媒体内容中心</h1><p class="text-sm text-gray-600">丰富的视听学习资源</p></div></div></div></header><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="card mb-8"><h2 class="text-xl font-bold text-gray-900 mb-6">内容分类</h2><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"><button class="p-4 rounded-lg border-2 text-left transition-all border-green-500 bg-green-50"><div class="flex items-center gap-3 mb-2"><div class="p-2 rounded-lg bg-green-100"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-video w-6 h-6"><path d="m22 8-6 4 6 4V8Z"></path><rect width="14" height="12" x="2" y="6" rx="2" ry="2"></rect></svg></div><h3 class="font-semibold text-gray-900">视频教学</h3></div><p class="text-sm text-gray-600">生动的视频案例和专家讲解</p></button><button class="p-4 rounded-lg border-2 text-left transition-all border-gray-200 hover:border-green-300"><div class="flex items-center gap-3 mb-2"><div class="p-2 rounded-lg bg-gray-100"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-headphones w-6 h-6"><path d="M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3"></path></svg></div><h3 class="font-semibold text-gray-900">音频内容</h3></div><p class="text-sm text-gray-600">便于随时收听的音频课程和播客</p></button><button class="p-4 rounded-lg border-2 text-left transition-all border-gray-200 hover:border-green-300"><div class="flex items-center gap-3 mb-2"><div class="p-2 rounded-lg bg-gray-100"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-camera w-6 h-6"><path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"></path><circle cx="12" cy="13" r="3"></circle></svg></div><h3 class="font-semibold text-gray-900">互动体验</h3></div><p class="text-sm text-gray-600">沉浸式的互动学习体验</p></button><button class="p-4 rounded-lg border-2 text-left transition-all border-gray-200 hover:border-green-300"><div class="flex items-center gap-3 mb-2"><div class="p-2 rounded-lg bg-gray-100"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mic w-6 h-6"><path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path><path d="M19 10v2a7 7 0 0 1-14 0v-2"></path><line x1="12" x2="12" y1="19" y2="22"></line></svg></div><h3 class="font-semibold text-gray-900">直播讲座</h3></div><p class="text-sm text-gray-600">实时互动的专家讲座和答疑</p></button></div></div><div class="space-y-6"><div class="flex items-center gap-3 mb-6"><div class="p-3 bg-green-100 rounded-lg"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-video w-6 h-6"><path d="m22 8-6 4 6 4V8Z"></path><rect width="14" height="12" x="2" y="6" rx="2" ry="2"></rect></svg></div><div><h3 class="text-2xl font-bold text-gray-900">视频教学</h3><p class="text-gray-600">生动的视频案例和专家讲解</p></div></div><div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6"><div class="card hover:shadow-lg transition-shadow"><div class="relative bg-gray-100 rounded-lg mb-4 h-48 flex items-center justify-center"><div class="text-6xl">🎭</div><div class="absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity"><button class="bg-white/90 rounded-full p-3 hover:bg-white transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-6 h-6 text-gray-800"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg></button></div><div class="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">8:32</div></div><div class="space-y-3"><h4 class="font-semibold text-gray-900 line-clamp-2">真实案例重现：大学生求职诈骗</h4><p class="text-sm text-gray-600 line-clamp-3">通过情景再现的方式，展示求职诈骗的完整过程和识别要点</p><div class="flex flex-wrap gap-2"><span class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">求职骗局</span><span class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">案例重现</span><span class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">专家解析</span></div><div class="flex items-center justify-between text-sm text-gray-500"><span>12.4K<!-- --> 观看</span><button class="text-green-600 hover:text-green-700 font-medium">立即观看</button></div></div></div><div class="card hover:shadow-lg transition-shadow"><div class="relative bg-gray-100 rounded-lg mb-4 h-48 flex items-center justify-center"><div class="text-6xl">👨‍💼</div><div class="absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity"><button class="bg-white/90 rounded-full p-3 hover:bg-white transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-6 h-6 text-gray-800"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg></button></div><div class="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">15:20</div></div><div class="space-y-3"><h4 class="font-semibold text-gray-900 line-clamp-2">防诈骗专家访谈：如何识别网贷陷阱</h4><p class="text-sm text-gray-600 line-clamp-3">邀请反诈专家深度解析网贷诈骗的常见套路和防范方法</p><div class="flex flex-wrap gap-2"><span class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">专家访谈</span><span class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">网贷陷阱</span><span class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">深度解析</span></div><div class="flex items-center justify-between text-sm text-gray-500"><span>8.7K<!-- --> 观看</span><button class="text-green-600 hover:text-green-700 font-medium">立即观看</button></div></div></div><div class="card hover:shadow-lg transition-shadow"><div class="relative bg-gray-100 rounded-lg mb-4 h-48 flex items-center justify-center"><div class="text-6xl">🎨</div><div class="absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity"><button class="bg-white/90 rounded-full p-3 hover:bg-white transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-6 h-6 text-gray-800"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg></button></div><div class="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">6:45</div></div><div class="space-y-3"><h4 class="font-semibold text-gray-900 line-clamp-2">动画科普：电信诈骗的演变历程</h4><p class="text-sm text-gray-600 line-clamp-3">用动画形式展示电信诈骗从传统到现代的发展变化</p><div class="flex flex-wrap gap-2"><span class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">动画科普</span><span class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">电信诈骗</span><span class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">历史演变</span></div><div class="flex items-center justify-between text-sm text-gray-500"><span>15.2K<!-- --> 观看</span><button class="text-green-600 hover:text-green-700 font-medium">立即观看</button></div></div></div></div></div><div class="card mt-8 bg-gradient-to-r from-green-500 to-blue-600 text-white"><h3 class="text-xl font-bold mb-4">🎬 多媒体学习的优势</h3><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div><h4 class="font-semibold mb-2">视听结合，印象深刻</h4><p class="text-green-100 text-sm">通过视频、音频等多种形式，让防诈骗知识更加生动有趣，提高学习效果。</p></div><div><h4 class="font-semibold mb-2">真实案例，警示教育</h4><p class="text-green-100 text-sm">基于真实案例制作内容，让用户深刻理解诈骗的危害和防范的重要性。</p></div><div><h4 class="font-semibold mb-2">专家指导，权威可信</h4><p class="text-green-100 text-sm">邀请反诈专家、法律专家等权威人士，确保内容的专业性和准确性。</p></div><div><h4 class="font-semibold mb-2">互动体验，身临其境</h4><p class="text-green-100 text-sm">通过 VR、角色扮演等互动方式，让用户在实践中掌握防诈骗技能。</p></div></div></div><div class="card mt-6 text-center"><h3 class="text-xl font-bold text-gray-900 mb-4">📋 开发计划</h3><p class="text-gray-600 mb-6">多媒体内容正在制作中，我们正在与专家团队合作，打造高质量的教育内容。</p><div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"><div class="bg-green-50 border border-green-200 rounded-lg p-4"><div class="text-2xl font-bold text-green-600">15+</div><div class="text-sm text-gray-600">视频制作中</div></div><div class="bg-blue-50 border border-blue-200 rounded-lg p-4"><div class="text-2xl font-bold text-blue-600">8+</div><div class="text-sm text-gray-600">音频录制中</div></div><div class="bg-purple-50 border border-purple-200 rounded-lg p-4"><div class="text-2xl font-bold text-purple-600">5+</div><div class="text-sm text-gray-600">互动体验开发中</div></div><div class="bg-orange-50 border border-orange-200 rounded-lg p-4"><div class="text-2xl font-bold text-orange-600">2+</div><div class="text-sm text-gray-600">直播栏目筹备中</div></div></div><div class="flex flex-col sm:flex-row gap-4 justify-center"><button class="btn-primary" disabled="">订阅更新通知 (开发中)</button><a class="btn-secondary" href="/dashboard/">返回主页</a></div></div></div></div></div><script src="/_next/static/chunks/webpack-69bfb055b70a1563.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/css/97115f26cb754c8c.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L2\"\n"])</script><script>self.__next_f.push([1,"3:I[3728,[],\"\"]\n5:I[9928,[],\"\"]\n6:I[6954,[],\"\"]\n7:I[7264,[],\"\"]\n8:I[8326,[\"326\",\"static/chunks/326-cf5b0283528485c7.js\",\"160\",\"static/chunks/app/not-found-5c4df079dd57eb5a.js\"],\"\"]\na:I[8297,[],\"\"]\nb:I[8735,[\"326\",\"static/chunks/326-cf5b0283528485c7.js\",\"463\",\"static/chunks/app/multimedia-content/page-ac1fee181c10580a.js\"],\"\"]\n"])</script><script>self.__next_f.push([1,"2:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/97115f26cb754c8c.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L3\",null,{\"buildId\":\"-csu5AGVUyiuSzdvekIsf\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/multimedia-content/\",\"initialTree\":[\"\",{\"children\":[\"multimedia-content\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],\"initialHead\":[false,\"$L4\"],\"globalErrorComponent\":\"$5\",\"children\":[null,[\"$\",\"html\",null,{\"lang\":\"zh-CN\",\"children\":[\"$\",\"body\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"min-h-screen\",\"children\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4 sm:px-6 lg:px-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-md w-full text-center\",\"children\":[\"$\",\"div\",null,{\"className\":\"card\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mb-6\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-alert-triangle w-16 h-16 text-yellow-500 mx-auto mb-4\",\"children\":[[\"$\",\"path\",\"c3ski4\",{\"d\":\"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z\"}],[\"$\",\"path\",\"juzpu7\",{\"d\":\"M12 9v4\"}],[\"$\",\"path\",\"p32p05\",{\"d\":\"M12 17h.01\"}],\"$undefined\"]}],[\"$\",\"h1\",null,{\"className\":\"text-6xl font-bold text-gray-900 mb-2\",\"children\":\"404\"}],[\"$\",\"h2\",null,{\"className\":\"text-2xl font-semibold text-gray-700 mb-4\",\"children\":\"页面未找到\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600\",\"children\":\"抱歉，你访问的页面不存在。可能是链接错误或页面已被移除。\"}]]}],[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"btn-primary w-full flex items-center justify-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-home w-5 h-5\",\"children\":[[\"$\",\"path\",\"y5dka4\",{\"d\":\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"}],[\"$\",\"polyline\",\"e2us08\",{\"points\":\"9 22 9 12 15 12 15 22\"}],\"$undefined\"]}],\"返回首页\"]}],[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"btn-secondary w-full flex items-center justify-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-left w-5 h-5\",\"children\":[[\"$\",\"path\",\"1l729n\",{\"d\":\"m12 19-7-7 7-7\"}],[\"$\",\"path\",\"x3x0zl\",{\"d\":\"M19 12H5\"}],\"$undefined\"]}],\"返回上一页\"]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\"children\":[\"$\",\"p\",null,{\"className\":\"text-blue-800 text-sm\",\"children\":[\"💡 \",[\"$\",\"strong\",null,{\"children\":\"提示：\"}],\"如果你是通过链接访问的，请检查链接是否正确。 如果问题持续存在，请联系我们。\"]}]}]]}]}]}],\"notFoundStyles\":[],\"childProp\":{\"current\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"multimedia-content\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"childProp\":{\"current\":[\"$L9\",[\"$\",\"$La\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$b\",\"isStaticGeneration\":true}],null],\"segment\":\"__PAGE__\"},\"styles\":null}],\"segment\":\"multimedia-content\"},\"styles\":null}]}]}]}],null]}]]\n"])</script><script>self.__next_f.push([1,"4:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"大学生防诈挑战游戏\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱\"}],[\"$\",\"meta\",\"4\",{\"name\":\"author\",\"content\":\"防诈挑战游戏团队\"}],[\"$\",\"meta\",\"5\",{\"name\":\"keywords\",\"content\":\"防诈骗,大学生,安全教育,答题游戏\"}]]\n9:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>