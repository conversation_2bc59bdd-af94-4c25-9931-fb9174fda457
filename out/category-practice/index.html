<!DOCTYPE html><html lang="zh-CN"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/97115f26cb754c8c.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-69bfb055b70a1563.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-a0c76284e27c8afa.js" async="" crossorigin=""></script><script src="/_next/static/chunks/472-af003cc8e47f5b1f.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-52f18067e7ca02da.js" async="" crossorigin=""></script><script src="/_next/static/chunks/326-cf5b0283528485c7.js" async=""></script><script src="/_next/static/chunks/app/not-found-5c4df079dd57eb5a.js" async=""></script><script src="/_next/static/chunks/app/category-practice/page-8e662fe0b8fd42a9.js" async=""></script><title>大学生防诈挑战游戏</title><meta name="description" content="通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱"/><meta name="author" content="防诈挑战游戏团队"/><meta name="keywords" content="防诈骗,大学生,安全教育,答题游戏"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body><div class="min-h-screen"><div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100"><header class="bg-white shadow-sm border-b"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4"><div class="flex items-center gap-3"><a class="p-2 hover:bg-gray-100 rounded-lg transition-colors" href="/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left w-5 h-5 text-gray-600"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg></a><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-book-open w-8 h-8 text-primary-600"><path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path><path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path></svg><div><h1 class="text-2xl font-bold text-gray-900">分类刷题</h1><p class="text-sm text-gray-600">选择感兴趣的领域，针对性学习防诈骗知识</p></div></div></div></header><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="card mb-8"><h3 class="text-lg font-semibold text-gray-900 mb-4">难度筛选</h3><div class="flex flex-wrap gap-3"><button class="px-4 py-2 rounded-full text-sm font-medium transition-all bg-primary-600 text-white">全部</button><button class="px-4 py-2 rounded-full text-sm font-medium transition-all bg-gray-100 text-gray-700 hover:bg-gray-200">入门</button><button class="px-4 py-2 rounded-full text-sm font-medium transition-all bg-gray-100 text-gray-700 hover:bg-gray-200">进阶</button><button class="px-4 py-2 rounded-full text-sm font-medium transition-all bg-gray-100 text-gray-700 hover:bg-gray-200">高级</button></div></div><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"><div class="card hover:shadow-lg transition-all duration-300 border-2 from-blue-500 to-blue-600 border-blue-200 bg-blue-50"><div class="flex items-center gap-4 mb-4"><div class="text-4xl">💼</div><div class="flex-1"><h3 class="text-xl font-bold text-gray-900">求职防骗</h3><p class="text-sm text-gray-600">识别虚假招聘、培训费诈骗等求职陷阱</p></div></div><div class="flex items-center gap-2 mb-4"><span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700">入门</span><span class="text-sm text-gray-500">15<!-- --> 道题目</span></div><div class="space-y-3 mb-6"><div class="flex justify-between text-sm"><span class="text-gray-600">完成进度</span><span class="font-medium">8<!-- -->/<!-- -->15</span></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-blue-500 to-blue-600" style="width:53.333333333333336%"></div></div><div class="flex items-center gap-2 text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trophy w-4 h-4 text-yellow-500"><path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6"></path><path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18"></path><path d="M4 22h16"></path><path d="M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22"></path><path d="M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22"></path><path d="M18 2H6v7a6 6 0 0 0 12 0V2Z"></path></svg><span class="text-gray-600">最佳成绩: <!-- -->87<!-- -->%</span></div></div><div class="flex gap-3"><button class="flex-1 btn-primary flex items-center justify-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-4 h-4"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg>开始练习</button><button class="btn-secondary flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-4 h-4"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>复习</button></div></div><div class="card hover:shadow-lg transition-all duration-300 border-2 from-green-500 to-green-600 border-green-200 bg-green-50"><div class="flex items-center gap-4 mb-4"><div class="text-4xl">🏠</div><div class="flex-1"><h3 class="text-xl font-bold text-gray-900">租房安全</h3><p class="text-sm text-gray-600">防范假房东、押金诈骗等租房风险</p></div></div><div class="flex items-center gap-2 mb-4"><span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700">入门</span><span class="text-sm text-gray-500">12<!-- --> 道题目</span></div><div class="space-y-3 mb-6"><div class="flex justify-between text-sm"><span class="text-gray-600">完成进度</span><span class="font-medium">5<!-- -->/<!-- -->12</span></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-green-500 to-green-600" style="width:41.66666666666667%"></div></div><div class="flex items-center gap-2 text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trophy w-4 h-4 text-yellow-500"><path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6"></path><path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18"></path><path d="M4 22h16"></path><path d="M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22"></path><path d="M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22"></path><path d="M18 2H6v7a6 6 0 0 0 12 0V2Z"></path></svg><span class="text-gray-600">最佳成绩: <!-- -->75<!-- -->%</span></div></div><div class="flex gap-3"><button class="flex-1 btn-primary flex items-center justify-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-4 h-4"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg>开始练习</button><button class="btn-secondary flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-4 h-4"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>复习</button></div></div><div class="card hover:shadow-lg transition-all duration-300 border-2 from-yellow-500 to-yellow-600 border-yellow-200 bg-yellow-50"><div class="flex items-center gap-4 mb-4"><div class="text-4xl">💰</div><div class="flex-1"><h3 class="text-xl font-bold text-gray-900">金融理财</h3><p class="text-sm text-gray-600">识别网贷陷阱、投资诈骗等金融风险</p></div></div><div class="flex items-center gap-2 mb-4"><span class="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-700">进阶</span><span class="text-sm text-gray-500">18<!-- --> 道题目</span></div><div class="space-y-3 mb-6"><div class="flex justify-between text-sm"><span class="text-gray-600">完成进度</span><span class="font-medium">3<!-- -->/<!-- -->18</span></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-yellow-500 to-yellow-600" style="width:16.666666666666664%"></div></div><div class="flex items-center gap-2 text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trophy w-4 h-4 text-yellow-500"><path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6"></path><path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18"></path><path d="M4 22h16"></path><path d="M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22"></path><path d="M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22"></path><path d="M18 2H6v7a6 6 0 0 0 12 0V2Z"></path></svg><span class="text-gray-600">最佳成绩: <!-- -->65<!-- -->%</span></div></div><div class="flex gap-3"><button class="flex-1 btn-primary flex items-center justify-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-4 h-4"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg>开始练习</button><button class="btn-secondary flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-4 h-4"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>复习</button></div></div><div class="card hover:shadow-lg transition-all duration-300 border-2 from-purple-500 to-purple-600 border-purple-200 bg-purple-50"><div class="flex items-center gap-4 mb-4"><div class="text-4xl">🎓</div><div class="flex-1"><h3 class="text-xl font-bold text-gray-900">教育培训</h3><p class="text-sm text-gray-600">防范培训诈骗、学历造假等教育陷阱</p></div></div><div class="flex items-center gap-2 mb-4"><span class="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-700">进阶</span><span class="text-sm text-gray-500">10<!-- --> 道题目</span></div><div class="space-y-3 mb-6"><div class="flex justify-between text-sm"><span class="text-gray-600">完成进度</span><span class="font-medium">0<!-- -->/<!-- -->10</span></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-purple-500 to-purple-600" style="width:0%"></div></div></div><div class="flex gap-3"><button class="flex-1 btn-primary flex items-center justify-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-4 h-4"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg>开始练习</button></div></div><div class="card hover:shadow-lg transition-all duration-300 border-2 from-indigo-500 to-indigo-600 border-indigo-200 bg-indigo-50"><div class="flex items-center gap-4 mb-4"><div class="text-4xl">📱</div><div class="flex-1"><h3 class="text-xl font-bold text-gray-900">网络通信</h3><p class="text-sm text-gray-600">识别电信诈骗、钓鱼网站等网络风险</p></div></div><div class="flex items-center gap-2 mb-4"><span class="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-700">高级</span><span class="text-sm text-gray-500">20<!-- --> 道题目</span></div><div class="space-y-3 mb-6"><div class="flex justify-between text-sm"><span class="text-gray-600">完成进度</span><span class="font-medium">2<!-- -->/<!-- -->20</span></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-indigo-500 to-indigo-600" style="width:10%"></div></div><div class="flex items-center gap-2 text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trophy w-4 h-4 text-yellow-500"><path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6"></path><path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18"></path><path d="M4 22h16"></path><path d="M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22"></path><path d="M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22"></path><path d="M18 2H6v7a6 6 0 0 0 12 0V2Z"></path></svg><span class="text-gray-600">最佳成绩: <!-- -->45<!-- -->%</span></div></div><div class="flex gap-3"><button class="flex-1 btn-primary flex items-center justify-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-4 h-4"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg>开始练习</button><button class="btn-secondary flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-4 h-4"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>复习</button></div></div><div class="card hover:shadow-lg transition-all duration-300 border-2 from-red-500 to-red-600 border-red-200 bg-red-50"><div class="flex items-center gap-4 mb-4"><div class="text-4xl">⚖️</div><div class="flex-1"><h3 class="text-xl font-bold text-gray-900">冒充权威</h3><p class="text-sm text-gray-600">识别冒充公检法、政府机构等权威诈骗</p></div></div><div class="flex items-center gap-2 mb-4"><span class="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-700">高级</span><span class="text-sm text-gray-500">8<!-- --> 道题目</span></div><div class="space-y-3 mb-6"><div class="flex justify-between text-sm"><span class="text-gray-600">完成进度</span><span class="font-medium">0<!-- -->/<!-- -->8</span></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-red-500 to-red-600" style="width:0%"></div></div></div><div class="flex gap-3"><button class="flex-1 btn-primary flex items-center justify-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-4 h-4"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg>开始练习</button></div></div><div class="card hover:shadow-lg transition-all duration-300 border-2 from-pink-500 to-pink-600 border-pink-200 bg-pink-50"><div class="flex items-center gap-4 mb-4"><div class="text-4xl">💻</div><div class="flex-1"><h3 class="text-xl font-bold text-gray-900">兼职副业</h3><p class="text-sm text-gray-600">防范刷单诈骗、兼职陷阱等副业风险</p></div></div><div class="flex items-center gap-2 mb-4"><span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700">入门</span><span class="text-sm text-gray-500">14<!-- --> 道题目</span></div><div class="space-y-3 mb-6"><div class="flex justify-between text-sm"><span class="text-gray-600">完成进度</span><span class="font-medium">6<!-- -->/<!-- -->14</span></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-pink-500 to-pink-600" style="width:42.857142857142854%"></div></div><div class="flex items-center gap-2 text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trophy w-4 h-4 text-yellow-500"><path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6"></path><path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18"></path><path d="M4 22h16"></path><path d="M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22"></path><path d="M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22"></path><path d="M18 2H6v7a6 6 0 0 0 12 0V2Z"></path></svg><span class="text-gray-600">最佳成绩: <!-- -->92<!-- -->%</span></div></div><div class="flex gap-3"><button class="flex-1 btn-primary flex items-center justify-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-4 h-4"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg>开始练习</button><button class="btn-secondary flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-4 h-4"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>复习</button></div></div></div><div class="card mt-8 bg-gradient-to-r from-primary-500 to-purple-600 text-white"><h3 class="text-xl font-bold mb-4">💡 学习建议</h3><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div><h4 class="font-semibold mb-2">新手推荐路径</h4><ol class="text-primary-100 text-sm space-y-1"><li>1. 求职防骗 → 基础必学</li><li>2. 租房安全 → 生活必备</li><li>3. 兼职副业 → 收入相关</li><li>4. 金融理财 → 进阶学习</li></ol></div><div><h4 class="font-semibold mb-2">学习小贴士</h4><ul class="text-primary-100 text-sm space-y-1"><li>• 建议每天练习1-2个分类</li><li>• 错题要及时复习巩固</li><li>• 结合实际生活场景思考</li><li>• 分享经验帮助他人学习</li></ul></div></div></div><div class="grid grid-cols-1 md:grid-cols-4 gap-6 mt-8"><div class="card text-center"><div class="text-2xl font-bold text-primary-600">97</div><div class="text-sm text-gray-600">总题目数</div></div><div class="card text-center"><div class="text-2xl font-bold text-success-600">24</div><div class="text-sm text-gray-600">已完成</div></div><div class="card text-center"><div class="text-2xl font-bold text-yellow-600">5</div><div class="text-sm text-gray-600">已掌握分类</div></div><div class="card text-center"><div class="text-2xl font-bold text-purple-600">52<!-- -->%</div><div class="text-sm text-gray-600">平均成绩</div></div></div></div></div></div><script src="/_next/static/chunks/webpack-69bfb055b70a1563.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/css/97115f26cb754c8c.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L2\"\n"])</script><script>self.__next_f.push([1,"3:I[3728,[],\"\"]\n5:I[9928,[],\"\"]\n6:I[6954,[],\"\"]\n7:I[7264,[],\"\"]\n8:I[8326,[\"326\",\"static/chunks/326-cf5b0283528485c7.js\",\"160\",\"static/chunks/app/not-found-5c4df079dd57eb5a.js\"],\"\"]\na:I[8297,[],\"\"]\nb:I[181,[\"326\",\"static/chunks/326-cf5b0283528485c7.js\",\"470\",\"static/chunks/app/category-practice/page-8e662fe0b8fd42a9.js\"],\"\"]\n"])</script><script>self.__next_f.push([1,"2:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/97115f26cb754c8c.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L3\",null,{\"buildId\":\"-csu5AGVUyiuSzdvekIsf\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/category-practice/\",\"initialTree\":[\"\",{\"children\":[\"category-practice\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],\"initialHead\":[false,\"$L4\"],\"globalErrorComponent\":\"$5\",\"children\":[null,[\"$\",\"html\",null,{\"lang\":\"zh-CN\",\"children\":[\"$\",\"body\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"min-h-screen\",\"children\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4 sm:px-6 lg:px-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-md w-full text-center\",\"children\":[\"$\",\"div\",null,{\"className\":\"card\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mb-6\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-alert-triangle w-16 h-16 text-yellow-500 mx-auto mb-4\",\"children\":[[\"$\",\"path\",\"c3ski4\",{\"d\":\"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z\"}],[\"$\",\"path\",\"juzpu7\",{\"d\":\"M12 9v4\"}],[\"$\",\"path\",\"p32p05\",{\"d\":\"M12 17h.01\"}],\"$undefined\"]}],[\"$\",\"h1\",null,{\"className\":\"text-6xl font-bold text-gray-900 mb-2\",\"children\":\"404\"}],[\"$\",\"h2\",null,{\"className\":\"text-2xl font-semibold text-gray-700 mb-4\",\"children\":\"页面未找到\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600\",\"children\":\"抱歉，你访问的页面不存在。可能是链接错误或页面已被移除。\"}]]}],[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"btn-primary w-full flex items-center justify-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-home w-5 h-5\",\"children\":[[\"$\",\"path\",\"y5dka4\",{\"d\":\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"}],[\"$\",\"polyline\",\"e2us08\",{\"points\":\"9 22 9 12 15 12 15 22\"}],\"$undefined\"]}],\"返回首页\"]}],[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"btn-secondary w-full flex items-center justify-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-left w-5 h-5\",\"children\":[[\"$\",\"path\",\"1l729n\",{\"d\":\"m12 19-7-7 7-7\"}],[\"$\",\"path\",\"x3x0zl\",{\"d\":\"M19 12H5\"}],\"$undefined\"]}],\"返回上一页\"]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\"children\":[\"$\",\"p\",null,{\"className\":\"text-blue-800 text-sm\",\"children\":[\"💡 \",[\"$\",\"strong\",null,{\"children\":\"提示：\"}],\"如果你是通过链接访问的，请检查链接是否正确。 如果问题持续存在，请联系我们。\"]}]}]]}]}]}],\"notFoundStyles\":[],\"childProp\":{\"current\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"category-practice\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"childProp\":{\"current\":[\"$L9\",[\"$\",\"$La\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$b\",\"isStaticGeneration\":true}],null],\"segment\":\"__PAGE__\"},\"styles\":null}],\"segment\":\"category-practice\"},\"styles\":null}]}]}]}],null]}]]\n"])</script><script>self.__next_f.push([1,"4:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"大学生防诈挑战游戏\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱\"}],[\"$\",\"meta\",\"4\",{\"name\":\"author\",\"content\":\"防诈挑战游戏团队\"}],[\"$\",\"meta\",\"5\",{\"name\":\"keywords\",\"content\":\"防诈骗,大学生,安全教育,答题游戏\"}]]\n9:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>