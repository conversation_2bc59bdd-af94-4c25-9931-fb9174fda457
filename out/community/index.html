<!DOCTYPE html><html lang="zh-CN"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/97115f26cb754c8c.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-69bfb055b70a1563.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-a0c76284e27c8afa.js" async="" crossorigin=""></script><script src="/_next/static/chunks/472-af003cc8e47f5b1f.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-52f18067e7ca02da.js" async="" crossorigin=""></script><script src="/_next/static/chunks/326-cf5b0283528485c7.js" async=""></script><script src="/_next/static/chunks/app/not-found-5c4df079dd57eb5a.js" async=""></script><script src="/_next/static/chunks/app/community/page-b8d0d040bfe87a02.js" async=""></script><title>大学生防诈挑战游戏</title><meta name="description" content="通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱"/><meta name="author" content="防诈挑战游戏团队"/><meta name="keywords" content="防诈骗,大学生,安全教育,答题游戏"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body><div class="min-h-screen"><div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100"><header class="bg-white shadow-sm border-b"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4"><div class="flex items-center gap-3"><a class="p-2 hover:bg-gray-100 rounded-lg transition-colors" href="/dashboard/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left w-5 h-5 text-gray-600"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg></a><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-8 h-8 text-primary-600"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg><div><h1 class="text-2xl font-bold text-gray-900">社区</h1><p class="text-sm text-gray-600">分享防诈骗经验，互相学习</p></div></div></div></header><div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="card mb-8"><h3 class="text-lg font-semibold text-gray-900 mb-4">分类筛选</h3><div class="flex flex-wrap gap-2"><button class="px-4 py-2 rounded-full text-sm font-medium transition-all bg-primary-600 text-white">全部</button><button class="px-4 py-2 rounded-full text-sm font-medium transition-all bg-gray-100 text-gray-700 hover:bg-gray-200">求职骗局</button><button class="px-4 py-2 rounded-full text-sm font-medium transition-all bg-gray-100 text-gray-700 hover:bg-gray-200">租房陷阱</button><button class="px-4 py-2 rounded-full text-sm font-medium transition-all bg-gray-100 text-gray-700 hover:bg-gray-200">网贷陷阱</button><button class="px-4 py-2 rounded-full text-sm font-medium transition-all bg-gray-100 text-gray-700 hover:bg-gray-200">培训诈骗</button><button class="px-4 py-2 rounded-full text-sm font-medium transition-all bg-gray-100 text-gray-700 hover:bg-gray-200">电信诈骗</button><button class="px-4 py-2 rounded-full text-sm font-medium transition-all bg-gray-100 text-gray-700 hover:bg-gray-200">冒充公检法</button><button class="px-4 py-2 rounded-full text-sm font-medium transition-all bg-gray-100 text-gray-700 hover:bg-gray-200">兼职诈骗</button></div></div><div class="card mb-8 bg-gradient-to-r from-blue-500 to-purple-600 text-white"><div class="flex items-start gap-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle w-6 h-6 text-yellow-300 mt-1"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path><path d="M12 9v4"></path><path d="M12 17h.01"></path></svg><div><h3 class="text-lg font-bold mb-2">社区公告</h3><p class="text-blue-100 mb-4">欢迎大家分享防诈骗经验！请注意保护个人隐私，不要透露具体的个人信息。 让我们一起建设一个安全、友善的学习社区。</p><div class="text-sm text-blue-200">• 分享真实经历，帮助他人避免诈骗<br/>• 保护个人隐私，不透露敏感信息<br/>• 理性讨论，文明交流</div></div></div></div><div class="space-y-6"><div class="card hover:shadow-lg transition-shadow"><div class="flex items-start justify-between mb-4"><div class="flex items-center gap-3"><div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-5 h-5 text-primary-600"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg></div><div><div class="flex items-center gap-2"><span class="font-semibold text-gray-900">防诈达人</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-circle w-4 h-4 text-blue-500"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><path d="m9 11 3 3L22 4"></path></svg></div><div class="flex items-center gap-2 text-sm text-gray-500"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-4 h-4"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>15/1/2024<span class="px-2 py-1 bg-gray-100 rounded-full text-xs">求职骗局</span></div></div></div></div><h3 class="text-lg font-semibold text-gray-900 mb-3">刚刚遇到的求职诈骗，差点上当！</h3><p class="text-gray-700 mb-4 leading-relaxed">今天接到一个电话说是某知名公司HR，要我去面试。但是要求我先交500元的&quot;资料审核费&quot;，我想起在防诈游戏里学到的知识，立即拒绝了。正规公司绝对不会要求求职者交费！</p><div class="flex items-center justify-between pt-4 border-t border-gray-200"><div class="flex items-center gap-4"><button class="flex items-center gap-2 text-gray-500 hover:text-primary-600 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-thumbs-up w-4 h-4"><path d="M7 10v12"></path><path d="M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z"></path></svg><span class="text-sm">23</span></button><button class="flex items-center gap-2 text-gray-500 hover:text-primary-600 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-circle w-4 h-4"><path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"></path></svg><span class="text-sm">8</span></button></div><button class="text-sm text-primary-600 hover:text-primary-700 font-medium">查看详情</button></div></div><div class="card hover:shadow-lg transition-shadow"><div class="flex items-start justify-between mb-4"><div class="flex items-center gap-3"><div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-5 h-5 text-primary-600"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg></div><div><div class="flex items-center gap-2"><span class="font-semibold text-gray-900">谨慎学子</span></div><div class="flex items-center gap-2 text-sm text-gray-500"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-4 h-4"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>14/1/2024<span class="px-2 py-1 bg-gray-100 rounded-full text-xs">租房陷阱</span></div></div></div></div><h3 class="text-lg font-semibold text-gray-900 mb-3">租房遇到假房东，幸好及时发现</h3><p class="text-gray-700 mb-4 leading-relaxed">在网上看到一个价格很便宜的房子，房东说要先交定金。我要求看房产证，对方各种推脱。后来发现照片是盗用的，差点被骗！大家租房一定要实地看房，核实房东身份。</p><div class="flex items-center justify-between pt-4 border-t border-gray-200"><div class="flex items-center gap-4"><button class="flex items-center gap-2 text-gray-500 hover:text-primary-600 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-thumbs-up w-4 h-4"><path d="M7 10v12"></path><path d="M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z"></path></svg><span class="text-sm">18</span></button><button class="flex items-center gap-2 text-gray-500 hover:text-primary-600 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-circle w-4 h-4"><path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"></path></svg><span class="text-sm">12</span></button></div><button class="text-sm text-primary-600 hover:text-primary-700 font-medium">查看详情</button></div></div><div class="card hover:shadow-lg transition-shadow"><div class="flex items-start justify-between mb-4"><div class="flex items-center gap-3"><div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-5 h-5 text-primary-600"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg></div><div><div class="flex items-center gap-2"><span class="font-semibold text-gray-900">理性思考</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-circle w-4 h-4 text-blue-500"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><path d="m9 11 3 3L22 4"></path></svg></div><div class="flex items-center gap-2 text-sm text-gray-500"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-4 h-4"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>13/1/2024<span class="px-2 py-1 bg-gray-100 rounded-full text-xs">网贷陷阱</span></div></div></div></div><h3 class="text-lg font-semibold text-gray-900 mb-3">网贷诈骗套路深，大家要小心</h3><p class="text-gray-700 mb-4 leading-relaxed">最近收到很多&quot;无抵押快速放款&quot;的短信，点进去看要求先交手续费。记住：正规贷款绝对不会要求预付费用！需要贷款的同学一定要通过银行等正规渠道。</p><div class="flex items-center justify-between pt-4 border-t border-gray-200"><div class="flex items-center gap-4"><button class="flex items-center gap-2 text-gray-500 hover:text-primary-600 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-thumbs-up w-4 h-4"><path d="M7 10v12"></path><path d="M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z"></path></svg><span class="text-sm">31</span></button><button class="flex items-center gap-2 text-gray-500 hover:text-primary-600 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-circle w-4 h-4"><path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"></path></svg><span class="text-sm">15</span></button></div><button class="text-sm text-primary-600 hover:text-primary-700 font-medium">查看详情</button></div></div><div class="card hover:shadow-lg transition-shadow"><div class="flex items-start justify-between mb-4"><div class="flex items-center gap-3"><div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-5 h-5 text-primary-600"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg></div><div><div class="flex items-center gap-2"><span class="font-semibold text-gray-900">警觉青年</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-circle w-4 h-4 text-blue-500"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><path d="m9 11 3 3L22 4"></path></svg></div><div class="flex items-center gap-2 text-sm text-gray-500"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-4 h-4"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>12/1/2024<span class="px-2 py-1 bg-gray-100 rounded-full text-xs">冒充公检法</span></div></div></div></div><h3 class="text-lg font-semibold text-gray-900 mb-3">冒充公检法电话，差点被吓到</h3><p class="text-gray-700 mb-4 leading-relaxed">昨天接到电话说我涉嫌洗钱，要求我配合调查转账到&quot;安全账户&quot;。刚开始真的被吓到了，但想起游戏里的知识，公检法绝对不会电话要求转账！立即挂断并报警。</p><div class="flex items-center justify-between pt-4 border-t border-gray-200"><div class="flex items-center gap-4"><button class="flex items-center gap-2 text-gray-500 hover:text-primary-600 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-thumbs-up w-4 h-4"><path d="M7 10v12"></path><path d="M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z"></path></svg><span class="text-sm">27</span></button><button class="flex items-center gap-2 text-gray-500 hover:text-primary-600 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-circle w-4 h-4"><path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"></path></svg><span class="text-sm">9</span></button></div><button class="text-sm text-primary-600 hover:text-primary-700 font-medium">查看详情</button></div></div><div class="card hover:shadow-lg transition-shadow"><div class="flex items-start justify-between mb-4"><div class="flex items-center gap-3"><div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-5 h-5 text-primary-600"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg></div><div><div class="flex items-center gap-2"><span class="font-semibold text-gray-900">细心观察</span></div><div class="flex items-center gap-2 text-sm text-gray-500"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-4 h-4"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>12/1/2024<span class="px-2 py-1 bg-gray-100 rounded-full text-xs">兼职诈骗</span></div></div></div></div><h3 class="text-lg font-semibold text-gray-900 mb-3">兼职刷单诈骗经历分享</h3><p class="text-gray-700 mb-4 leading-relaxed">朋友介绍了一个&quot;刷单兼职&quot;，说日赚200很轻松。要求先垫付商品款，承诺立即返还并给佣金。幸好我在防诈游戏里学过，所有要求垫付的兼职都是诈骗！</p><div class="flex items-center justify-between pt-4 border-t border-gray-200"><div class="flex items-center gap-4"><button class="flex items-center gap-2 text-gray-500 hover:text-primary-600 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-thumbs-up w-4 h-4"><path d="M7 10v12"></path><path d="M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z"></path></svg><span class="text-sm">22</span></button><button class="flex items-center gap-2 text-gray-500 hover:text-primary-600 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-circle w-4 h-4"><path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"></path></svg><span class="text-sm">11</span></button></div><button class="text-sm text-primary-600 hover:text-primary-700 font-medium">查看详情</button></div></div></div><div class="card mt-8 text-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-circle w-12 h-12 text-primary-600 mx-auto mb-4"><path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"></path></svg><h3 class="text-lg font-semibold text-gray-900 mb-2">分享你的经验</h3><p class="text-gray-600 mb-4">遇到过诈骗或有防诈骗心得？分享给大家，帮助更多人提高警惕！</p><button class="btn-primary" disabled="">发布帖子 (开发中)</button></div></div></div></div><script src="/_next/static/chunks/webpack-69bfb055b70a1563.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/css/97115f26cb754c8c.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L2\"\n"])</script><script>self.__next_f.push([1,"3:I[3728,[],\"\"]\n5:I[9928,[],\"\"]\n6:I[6954,[],\"\"]\n7:I[7264,[],\"\"]\n8:I[8326,[\"326\",\"static/chunks/326-cf5b0283528485c7.js\",\"160\",\"static/chunks/app/not-found-5c4df079dd57eb5a.js\"],\"\"]\na:I[8297,[],\"\"]\nb:I[852,[\"326\",\"static/chunks/326-cf5b0283528485c7.js\",\"616\",\"static/chunks/app/community/page-b8d0d040bfe87a02.js\"],\"\"]\n"])</script><script>self.__next_f.push([1,"2:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/97115f26cb754c8c.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L3\",null,{\"buildId\":\"-csu5AGVUyiuSzdvekIsf\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/community/\",\"initialTree\":[\"\",{\"children\":[\"community\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],\"initialHead\":[false,\"$L4\"],\"globalErrorComponent\":\"$5\",\"children\":[null,[\"$\",\"html\",null,{\"lang\":\"zh-CN\",\"children\":[\"$\",\"body\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"min-h-screen\",\"children\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4 sm:px-6 lg:px-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-md w-full text-center\",\"children\":[\"$\",\"div\",null,{\"className\":\"card\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mb-6\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-alert-triangle w-16 h-16 text-yellow-500 mx-auto mb-4\",\"children\":[[\"$\",\"path\",\"c3ski4\",{\"d\":\"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z\"}],[\"$\",\"path\",\"juzpu7\",{\"d\":\"M12 9v4\"}],[\"$\",\"path\",\"p32p05\",{\"d\":\"M12 17h.01\"}],\"$undefined\"]}],[\"$\",\"h1\",null,{\"className\":\"text-6xl font-bold text-gray-900 mb-2\",\"children\":\"404\"}],[\"$\",\"h2\",null,{\"className\":\"text-2xl font-semibold text-gray-700 mb-4\",\"children\":\"页面未找到\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600\",\"children\":\"抱歉，你访问的页面不存在。可能是链接错误或页面已被移除。\"}]]}],[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"btn-primary w-full flex items-center justify-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-home w-5 h-5\",\"children\":[[\"$\",\"path\",\"y5dka4\",{\"d\":\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"}],[\"$\",\"polyline\",\"e2us08\",{\"points\":\"9 22 9 12 15 12 15 22\"}],\"$undefined\"]}],\"返回首页\"]}],[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"btn-secondary w-full flex items-center justify-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-left w-5 h-5\",\"children\":[[\"$\",\"path\",\"1l729n\",{\"d\":\"m12 19-7-7 7-7\"}],[\"$\",\"path\",\"x3x0zl\",{\"d\":\"M19 12H5\"}],\"$undefined\"]}],\"返回上一页\"]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\"children\":[\"$\",\"p\",null,{\"className\":\"text-blue-800 text-sm\",\"children\":[\"💡 \",[\"$\",\"strong\",null,{\"children\":\"提示：\"}],\"如果你是通过链接访问的，请检查链接是否正确。 如果问题持续存在，请联系我们。\"]}]}]]}]}]}],\"notFoundStyles\":[],\"childProp\":{\"current\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"community\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"childProp\":{\"current\":[\"$L9\",[\"$\",\"$La\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$b\",\"isStaticGeneration\":true}],null],\"segment\":\"__PAGE__\"},\"styles\":null}],\"segment\":\"community\"},\"styles\":null}]}]}]}],null]}]]\n"])</script><script>self.__next_f.push([1,"4:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"大学生防诈挑战游戏\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱\"}],[\"$\",\"meta\",\"4\",{\"name\":\"author\",\"content\":\"防诈挑战游戏团队\"}],[\"$\",\"meta\",\"5\",{\"name\":\"keywords\",\"content\":\"防诈骗,大学生,安全教育,答题游戏\"}]]\n9:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>