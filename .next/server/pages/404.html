<!DOCTYPE html><html lang="zh-CN"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/97115f26cb754c8c.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-69bfb055b70a1563.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-a0c76284e27c8afa.js" async="" crossorigin=""></script><script src="/_next/static/chunks/472-af003cc8e47f5b1f.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-52f18067e7ca02da.js" async="" crossorigin=""></script><script src="/_next/static/chunks/326-cf5b0283528485c7.js" async=""></script><script src="/_next/static/chunks/app/not-found-5c4df079dd57eb5a.js" async=""></script><title>大学生防诈挑战游戏</title><meta name="description" content="通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱"/><meta name="author" content="防诈挑战游戏团队"/><meta name="keywords" content="防诈骗,大学生,安全教育,答题游戏"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body><div class="min-h-screen"><div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4 sm:px-6 lg:px-8"><div class="max-w-md w-full text-center"><div class="card"><div class="mb-6"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle w-16 h-16 text-yellow-500 mx-auto mb-4"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path><path d="M12 9v4"></path><path d="M12 17h.01"></path></svg><h1 class="text-6xl font-bold text-gray-900 mb-2">404</h1><h2 class="text-2xl font-semibold text-gray-700 mb-4">页面未找到</h2><p class="text-gray-600">抱歉，你访问的页面不存在。可能是链接错误或页面已被移除。</p></div><div class="space-y-4"><a class="btn-primary w-full flex items-center justify-center gap-2" href="/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-home w-5 h-5"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>返回首页</a><a class="btn-secondary w-full flex items-center justify-center gap-2" href="/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left w-5 h-5"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg>返回上一页</a></div><div class="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg"><p class="text-blue-800 text-sm">💡 <strong>提示：</strong>如果你是通过链接访问的，请检查链接是否正确。 如果问题持续存在，请联系我们。</p></div></div></div></div></div><script src="/_next/static/chunks/webpack-69bfb055b70a1563.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/css/97115f26cb754c8c.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L2\"\n"])</script><script>self.__next_f.push([1,"3:I[3728,[],\"\"]\n5:I[9928,[],\"\"]\n6:I[6954,[],\"\"]\n7:I[7264,[],\"\"]\n8:I[8326,[\"326\",\"static/chunks/326-cf5b0283528485c7.js\",\"160\",\"static/chunks/app/not-found-5c4df079dd57eb5a.js\"],\"\"]\n"])</script><script>self.__next_f.push([1,"2:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/97115f26cb754c8c.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L3\",null,{\"buildId\":\"-csu5AGVUyiuSzdvekIsf\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/_not-found/\",\"initialTree\":[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],\"initialHead\":[false,\"$L4\"],\"globalErrorComponent\":\"$5\",\"children\":[null,[\"$\",\"html\",null,{\"lang\":\"zh-CN\",\"children\":[\"$\",\"body\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"min-h-screen\",\"children\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4 sm:px-6 lg:px-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-md w-full text-center\",\"children\":[\"$\",\"div\",null,{\"className\":\"card\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mb-6\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-alert-triangle w-16 h-16 text-yellow-500 mx-auto mb-4\",\"children\":[[\"$\",\"path\",\"c3ski4\",{\"d\":\"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z\"}],[\"$\",\"path\",\"juzpu7\",{\"d\":\"M12 9v4\"}],[\"$\",\"path\",\"p32p05\",{\"d\":\"M12 17h.01\"}],\"$undefined\"]}],[\"$\",\"h1\",null,{\"className\":\"text-6xl font-bold text-gray-900 mb-2\",\"children\":\"404\"}],[\"$\",\"h2\",null,{\"className\":\"text-2xl font-semibold text-gray-700 mb-4\",\"children\":\"页面未找到\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600\",\"children\":\"抱歉，你访问的页面不存在。可能是链接错误或页面已被移除。\"}]]}],[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"btn-primary w-full flex items-center justify-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-home w-5 h-5\",\"children\":[[\"$\",\"path\",\"y5dka4\",{\"d\":\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"}],[\"$\",\"polyline\",\"e2us08\",{\"points\":\"9 22 9 12 15 12 15 22\"}],\"$undefined\"]}],\"返回首页\"]}],[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"btn-secondary w-full flex items-center justify-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-left w-5 h-5\",\"children\":[[\"$\",\"path\",\"1l729n\",{\"d\":\"m12 19-7-7 7-7\"}],[\"$\",\"path\",\"x3x0zl\",{\"d\":\"M19 12H5\"}],\"$undefined\"]}],\"返回上一页\"]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\"children\":[\"$\",\"p\",null,{\"className\":\"text-blue-800 text-sm\",\"children\":[\"💡 \",[\"$\",\"strong\",null,{\"children\":\"提示：\"}],\"如果你是通过链接访问的，请检查链接是否正确。 如果问题持续存在，请联系我们。\"]}]}]]}]}]}],\"notFoundStyles\":[],\"childProp\":{\"current\":[\"$L9\",[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4 sm:px-6 lg:px-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-md w-full text-center\",\"children\":[\"$\",\"div\",null,{\"className\":\"card\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mb-6\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-alert-triangle w-16 h-16 text-yellow-500 mx-auto mb-4\",\"children\":[[\"$\",\"path\",\"c3ski4\",{\"d\":\"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z\"}],[\"$\",\"path\",\"juzpu7\",{\"d\":\"M12 9v4\"}],[\"$\",\"path\",\"p32p05\",{\"d\":\"M12 17h.01\"}],\"$undefined\"]}],[\"$\",\"h1\",null,{\"className\":\"text-6xl font-bold text-gray-900 mb-2\",\"children\":\"404\"}],[\"$\",\"h2\",null,{\"className\":\"text-2xl font-semibold text-gray-700 mb-4\",\"children\":\"页面未找到\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600\",\"children\":\"抱歉，你访问的页面不存在。可能是链接错误或页面已被移除。\"}]]}],[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"btn-primary w-full flex items-center justify-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-home w-5 h-5\",\"children\":[[\"$\",\"path\",\"y5dka4\",{\"d\":\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"}],[\"$\",\"polyline\",\"e2us08\",{\"points\":\"9 22 9 12 15 12 15 22\"}],\"$undefined\"]}],\"返回首页\"]}],[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"btn-secondary w-full flex items-center justify-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-left w-5 h-5\",\"children\":[[\"$\",\"path\",\"1l729n\",{\"d\":\"m12 19-7-7 7-7\"}],[\"$\",\"path\",\"x3x0zl\",{\"d\":\"M19 12H5\"}],\"$undefined\"]}],\"返回上一页\"]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\"children\":[\"$\",\"p\",null,{\"className\":\"text-blue-800 text-sm\",\"children\":[\"💡 \",[\"$\",\"strong\",null,{\"children\":\"提示：\"}],\"如果你是通过链接访问的，请检查链接是否正确。 如果问题持续存在，请联系我们。\"]}]}]]}]}]}],null],\"segment\":\"__PAGE__\"},\"styles\":null}]}]}]}],null]}]]\n"])</script><script>self.__next_f.push([1,"4:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"大学生防诈挑战游戏\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱\"}],[\"$\",\"meta\",\"4\",{\"name\":\"author\",\"content\":\"防诈挑战游戏团队\"}],[\"$\",\"meta\",\"5\",{\"name\":\"keywords\",\"content\":\"防诈骗,大学生,安全教育,答题游戏\"}]]\n9:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>