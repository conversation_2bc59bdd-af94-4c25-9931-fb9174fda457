(()=>{var e={};e.id=5761,e.ids=[5761],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},9087:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>n});var r=a(7096),t=a(6132),l=a(7284),i=a.n(l),d=a(2564),c={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>d[e]);a.d(s,c);let n=["",{children:["ai-features",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,5609)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/ai-features/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],o=["/home/<USER>/Desktop/vscode/防诈挑战游戏/app/ai-features/page.tsx"],x="/ai-features/page",m={require:a,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/ai-features/page",pathname:"/ai-features",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},6970:(e,s,a)=>{Promise.resolve().then(a.bind(a,4253))},4253:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>AIFeaturesPage});var r=a(784),t=a(9885),l=a(5957),i=a(696),d=a(5256),c=a(1672),n=a(9803),o=a(1814),x=a(5924),m=a(6494),p=a(1440),h=a.n(p);function AIFeaturesPage(){let[e,s]=(0,t.useState)("question-gen"),a={"question-gen":{title:"AI 智能出题",icon:r.jsx(l.Z,{className:"w-6 h-6"}),description:"基于最新诈骗案例和用户答题数据，AI 自动生成高质量题目",features:["实时案例分析：从新闻、社交媒体抓取最新诈骗案例","难度智能调节：根据用户水平自动调整题目难度","多样化题型：支持单选、多选、情景判断等多种题型","质量评估：AI 自动评估题目质量和教育价值"],mockData:{generatedQuestions:1247,qualityScore:94.2,categories:["求职骗局","网贷陷阱","电信诈骗"],lastUpdate:"2分钟前"}},personalized:{title:"个性化推荐",icon:r.jsx(i.Z,{className:"w-6 h-6"}),description:"基于用户行为和学习进度，提供个性化的学习路径和题目推荐",features:["学习路径规划：根据用户薄弱环节制定学习计划","智能题目推荐：推荐最适合当前水平的题目","知识图谱构建：构建个人防诈骗知识体系","进度跟踪：实时跟踪学习进度和效果"],mockData:{userLevel:"中级",weakPoints:["租房陷阱","培训诈骗"],recommendedTopics:3,learningProgress:67}},analytics:{title:"智能数据分析",icon:r.jsx(d.Z,{className:"w-6 h-6"}),description:"深度分析用户行为和学习效果，提供数据驱动的优化建议",features:["学习效果分析：多维度评估学习效果","行为模式识别：识别用户学习习惯和偏好","风险预警：预测用户可能遇到的诈骗风险","优化建议：基于数据提供个性化改进建议"],mockData:{totalUsers:15420,avgImprovement:23.5,riskReduction:78,satisfactionRate:92.3}},team:{title:"团队协作挑战",icon:r.jsx(c.Z,{className:"w-6 h-6"}),description:"支持团队组建、协作学习和集体挑战，增强学习互动性",features:["智能组队：基于能力水平自动匹配队友","协作任务：设计需要团队合作完成的挑战","集体竞赛：组织跨团队的知识竞赛","社交学习：促进用户间的知识分享和讨论"],mockData:{activeTeams:342,avgTeamSize:4.2,completionRate:89,collaborationScore:8.7}}},p=a[e];return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100",children:[r.jsx("header",{className:"bg-white shadow-sm border-b",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[r.jsx(h(),{href:"/dashboard",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:r.jsx(n.Z,{className:"w-5 h-5 text-gray-600"})}),r.jsx(o.Z,{className:"w-8 h-8 text-purple-600"}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"AI 功能演示"}),r.jsx("p",{className:"text-sm text-gray-600",children:"体验未来的智能防诈骗教育"})]})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"card mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-6",children:[r.jsx(x.Z,{className:"w-6 h-6 text-purple-600"}),r.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"AI 功能模块"})]}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:Object.entries(a).map(([a,t])=>(0,r.jsxs)("button",{onClick:()=>s(a),className:`p-4 rounded-lg border-2 text-left transition-all ${e===a?"border-purple-500 bg-purple-50":"border-gray-200 hover:border-purple-300"}`,children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[r.jsx("div",{className:`p-2 rounded-lg ${e===a?"bg-purple-100":"bg-gray-100"}`,children:t.icon}),r.jsx("h3",{className:"font-semibold text-gray-900",children:t.title})]}),r.jsx("p",{className:"text-sm text-gray-600",children:t.description})]},a))})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"card",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[r.jsx("div",{className:"p-3 bg-purple-100 rounded-lg",children:p.icon}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-xl font-bold text-gray-900",children:p.title}),r.jsx("p",{className:"text-gray-600",children:p.description})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("h4",{className:"font-semibold text-gray-900",children:"核心功能："}),r.jsx("ul",{className:"space-y-3",children:p.features.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-start gap-3",children:[r.jsx("div",{className:"flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mt-0.5",children:r.jsx(m.Z,{className:"w-4 h-4 text-purple-600"})}),r.jsx("span",{className:"text-gray-700",children:e})]},s))})]})]}),(0,r.jsxs)("div",{className:"card bg-gradient-to-r from-purple-500 to-indigo-600 text-white",children:[r.jsx("h4",{className:"text-lg font-bold mb-2",children:"\uD83D\uDE80 开发状态"}),r.jsx("p",{className:"text-purple-100 mb-4",children:"此功能正在开发中，预计在下个版本中发布。 当前展示的是功能原型和模拟数据。"}),(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[r.jsx("span",{className:"text-sm",children:"开发进度"}),r.jsx("span",{className:"text-sm font-semibold",children:"65%"})]}),r.jsx("div",{className:"w-full bg-white/20 rounded-full h-2",children:r.jsx("div",{className:"bg-white h-2 rounded-full",style:{width:"65%"}})})]})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"card",children:[r.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:"实时数据 (模拟)"}),r.jsx("div",{className:"grid grid-cols-2 gap-4",children:Object.entries(p.mockData).map(([e,s])=>(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 text-center",children:[r.jsx("div",{className:"text-2xl font-bold text-purple-600 mb-1",children:"number"==typeof s?s.toLocaleString():s}),r.jsx("div",{className:"text-sm text-gray-600 capitalize",children:e.replace(/([A-Z])/g," $1").toLowerCase()})]},e))})]}),(0,r.jsxs)("div",{className:"card",children:[r.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:"功能演示"}),"question-gen"===e&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[r.jsx("h5",{className:"font-semibold text-blue-900 mb-2",children:"AI 生成题目示例："}),r.jsx("p",{className:"text-blue-800 text-sm mb-3",children:'"你收到一条短信，声称你的快递因地址不详被退回，需要点击链接重新填写地址信息。你应该："'}),r.jsx("div",{className:"text-xs text-blue-600",children:"✨ 基于最新快递诈骗案例自动生成"})]}),r.jsx("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:r.jsx("div",{className:"text-sm text-green-800",children:"质量评分: 94.2/100 | 难度: 中等 | 分类: 电信诈骗"})})]}),"personalized"===e&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[r.jsx("h5",{className:"font-semibold text-yellow-900 mb-2",children:"个性化推荐："}),r.jsx("p",{className:"text-yellow-800 text-sm mb-2",children:"基于你的答题记录，建议重点学习："}),(0,r.jsxs)("ul",{className:"text-yellow-700 text-sm space-y-1",children:[r.jsx("li",{children:"• 租房陷阱识别技巧"}),r.jsx("li",{children:"• 培训诈骗防范方法"}),r.jsx("li",{children:"• 合同条款注意事项"})]})]}),r.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:r.jsx("div",{className:"text-sm text-blue-800",children:"预计学习时间: 15分钟 | 提升效果: +18%"})})]}),"analytics"===e&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[r.jsx("h5",{className:"font-semibold text-purple-900 mb-2",children:"智能分析报告："}),(0,r.jsxs)("div",{className:"space-y-2 text-purple-800 text-sm",children:[r.jsx("p",{children:"• 用户整体防诈骗意识提升 23.5%"}),r.jsx("p",{children:"• 最容易混淆的诈骗类型：网贷陷阱"}),r.jsx("p",{children:"• 建议增加实际案例练习"})]})]}),r.jsx("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:r.jsx("div",{className:"text-sm text-green-800",children:"风险降低: 78% | 用户满意度: 92.3%"})})]}),"team"===e&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"bg-indigo-50 border border-indigo-200 rounded-lg p-4",children:[r.jsx("h5",{className:"font-semibold text-indigo-900 mb-2",children:"团队挑战示例："}),r.jsx("p",{className:"text-indigo-800 text-sm mb-2",children:'"防诈小分队" 正在进行协作挑战：'}),(0,r.jsxs)("ul",{className:"text-indigo-700 text-sm space-y-1",children:[r.jsx("li",{children:"• 队员A: 负责求职诈骗案例分析"}),r.jsx("li",{children:"• 队员B: 负责租房陷阱识别"}),r.jsx("li",{children:"• 队员C: 负责综合防范策略"})]})]}),r.jsx("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-3",children:r.jsx("div",{className:"text-sm text-orange-800",children:"团队进度: 67% | 协作评分: 8.7/10"})})]})]})]})]}),(0,r.jsxs)("div",{className:"card mt-8 text-center",children:[r.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"期待您的反馈"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"这些 AI 功能还在开发中，您的意见对我们很重要！"}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[r.jsx("button",{className:"btn-primary",disabled:!0,children:"提交反馈 (开发中)"}),r.jsx("button",{className:"btn-secondary",disabled:!0,children:"申请内测 (开发中)"}),r.jsx(h(),{href:"/dashboard",className:"btn-secondary",children:"返回主页"})]})]})]})]})}},5609:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>i,__esModule:()=>l,default:()=>c});var r=a(5153);let t=(0,r.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/ai-features/page.tsx`),{__esModule:l,$$typeof:i}=t,d=t.default,c=d}};var s=require("../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),a=s.X(0,[9678,5607,9097,2035],()=>__webpack_exec__(9087));module.exports=a})();