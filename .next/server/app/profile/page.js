(()=>{var e={};e.id=4178,e.ids=[4178],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},5842:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>o,pages:()=>x,routeModule:()=>h,tree:()=>d});var a=t(7096),r=t(6132),l=t(7284),i=t.n(l),c=t(2564),n={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);t.d(s,n);let d=["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,6408)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/profile/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],x=["/home/<USER>/Desktop/vscode/防诈挑战游戏/app/profile/page.tsx"],o="/profile/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9306:(e,s,t)=>{Promise.resolve().then(t.bind(t,1503))},1503:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>ProfilePage});var a=t(784),r=t(9885),l=t(9803),i=t(3680),c=t(92),n=t(696),d=t(1438),x=t(517),o=t(5574),m=t(6303),h=t(1440),p=t.n(h),g=t(7114);function ProfilePage(){let e=(0,g.useRouter)(),[s,t]=(0,r.useState)(null),[h,j]=(0,r.useState)(null),[u,N]=(0,r.useState)([]);(0,r.useEffect)(()=>{let s=localStorage.getItem("userData");if(!s){e.push("/login");return}t(JSON.parse(s));let a=localStorage.getItem("starterResult");a&&j(JSON.parse(a));let r=localStorage.getItem("lastChallengeResult");r&&N([JSON.parse(r)])},[e]);let formatTime=e=>`${Math.floor(e/60)}:${(e%60).toString().padStart(2,"0")}`;return s?(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[a.jsx("header",{className:"bg-white shadow-sm border-b",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(p(),{href:"/dashboard",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(l.Z,{className:"w-5 h-5 text-gray-600"})}),a.jsx(i.Z,{className:"w-8 h-8 text-primary-600"}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"个人中心"}),a.jsx("p",{className:"text-sm text-gray-600",children:"管理你的账户和数据"})]})]}),(0,a.jsxs)("button",{onClick:()=>{confirm("确定要退出登录吗？")&&(localStorage.clear(),e.push("/"))},className:"btn-danger flex items-center gap-2",children:[a.jsx(c.Z,{className:"w-4 h-4"}),"退出登录"]})]})})}),(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"card mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[a.jsx("div",{className:"w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center",children:a.jsx(i.Z,{className:"w-8 h-8 text-primary-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:s.nickname}),(0,a.jsxs)("p",{className:"text-gray-600",children:["邀请码: ",s.inviteCode]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["注册时间: ",new Date(s.createdAt).toLocaleDateString()]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-primary-50 rounded-lg p-4 text-center",children:[a.jsx("div",{className:"text-2xl font-bold text-primary-600",children:s.contributionScore||0}),a.jsx("div",{className:"text-sm text-gray-600",children:"贡献值"})]}),(0,a.jsxs)("div",{className:"bg-success-50 rounded-lg p-4 text-center",children:[a.jsx("div",{className:"text-2xl font-bold text-success-600",children:h?`${h.score}/${h.total}`:"未完成"}),a.jsx("div",{className:"text-sm text-gray-600",children:"入门题成绩"})]}),(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 text-center",children:[a.jsx("div",{className:"text-2xl font-bold text-blue-600",children:u.length}),a.jsx("div",{className:"text-sm text-gray-600",children:"挑战次数"})]})]})]}),h&&(0,a.jsxs)("div",{className:"card mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[a.jsx(n.Z,{className:"w-6 h-6 text-success-600"}),a.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:"入门挑战成绩"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-lg font-bold text-success-600",children:h.score}),a.jsx("div",{className:"text-sm text-gray-600",children:"正确题数"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-lg font-bold text-blue-600",children:[Math.round(h.score/h.total*100),"%"]}),a.jsx("div",{className:"text-sm text-gray-600",children:"正确率"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-lg font-bold text-purple-600",children:formatTime(h.timeUsed)}),a.jsx("div",{className:"text-sm text-gray-600",children:"用时"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-lg font-bold text-gray-600",children:new Date(h.completedAt).toLocaleDateString()}),a.jsx("div",{className:"text-sm text-gray-600",children:"完成日期"})]})]})]}),(0,a.jsxs)("div",{className:"card mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[a.jsx(d.Z,{className:"w-6 h-6 text-yellow-500"}),a.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:"挑战历史"})]}),0===u.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx(x.Z,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-500",children:"还没有挑战记录"}),a.jsx(p(),{href:"/challenge",className:"btn-primary mt-4",children:"开始挑战"})]}):a.jsx("div",{className:"space-y-4",children:u.map((e,s)=>a.jsx("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-semibold text-gray-900",children:["每日挑战 #",s+1]}),a.jsx("div",{className:"text-sm text-gray-600",children:new Date(e.date).toLocaleDateString()})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-lg font-bold text-primary-600",children:[e.score," 题"]}),a.jsx("div",{className:"text-sm text-gray-600",children:formatTime(e.timeUsed)})]})]})},s))})]}),(0,a.jsxs)("div",{className:"card",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"数据管理"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-blue-900",children:"导出数据"}),a.jsx("div",{className:"text-sm text-blue-700",children:"下载你的所有游戏数据"})]}),(0,a.jsxs)("button",{onClick:()=>{let e={userData:s,starterResult:h,challengeHistory:u,exportDate:new Date().toISOString()},t=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),a=URL.createObjectURL(t),r=document.createElement("a");r.href=a,r.download=`防诈挑战数据-${s?.nickname}-${new Date().toLocaleDateString()}.json`,r.click(),URL.revokeObjectURL(a)},className:"btn-primary flex items-center gap-2",children:[a.jsx(o.Z,{className:"w-4 h-4"}),"导出"]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-red-900",children:"清除所有数据"}),a.jsx("div",{className:"text-sm text-red-700",children:"删除所有本地存储的数据，此操作不可恢复"})]}),(0,a.jsxs)("button",{onClick:()=>{confirm("确定要清除所有数据吗？此操作不可恢复！")&&(localStorage.clear(),e.push("/"))},className:"btn-danger flex items-center gap-2",children:[a.jsx(m.Z,{className:"w-4 h-4"}),"清除"]})]})]})]}),(0,a.jsxs)("div",{className:"mt-8 p-4 bg-gray-50 border border-gray-200 rounded-lg",children:[a.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"隐私保护说明"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[a.jsx("li",{children:"• 所有数据仅保存在你的浏览器本地存储中"}),a.jsx("li",{children:"• 我们不会收集或上传你的个人信息"}),a.jsx("li",{children:"• 你可以随时导出或删除自己的数据"}),a.jsx("li",{children:"• 清除浏览器数据会同时删除游戏记录"})]})]})]})]}):a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"})})}},6408:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>l,default:()=>n});var a=t(5153);let r=(0,a.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/profile/page.tsx`),{__esModule:l,$$typeof:i}=r,c=r.default,n=c}};var s=require("../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[9678,5607,1016,5276,1592,2035],()=>__webpack_exec__(5842));module.exports=t})();