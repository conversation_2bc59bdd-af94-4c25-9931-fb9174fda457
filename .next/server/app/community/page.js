(()=>{var e={};e.id=4616,e.ids=[4616],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},7470:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>d,pages:()=>m,routeModule:()=>p,tree:()=>o});var a=s(7096),r=s(6132),i=s(7284),l=s.n(i),n=s(2564),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let o=["",{children:["community",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,2463)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/community/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],m=["/home/<USER>/Desktop/vscode/防诈挑战游戏/app/community/page.tsx"],d="/community/page",x={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/community/page",pathname:"/community",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},7285:(e,t,s)=>{Promise.resolve().then(s.bind(s,1776))},1776:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>CommunityPage});var a=s(784),r=s(9885),i=s(9803),l=s(1672),n=s(4409),c=s(169),o=s(2032),m=s(5441),d=s(5183),x=s(1440),p=s.n(x);let h=[{id:"1",author:"防诈达人",title:"刚刚遇到的求职诈骗，差点上当！",content:'今天接到一个电话说是某知名公司HR，要我去面试。但是要求我先交500元的"资料审核费"，我想起在防诈游戏里学到的知识，立即拒绝了。正规公司绝对不会要求求职者交费！',category:"求职骗局",likes:23,comments:8,createdAt:"2024-01-15T10:30:00Z",isVerified:!0},{id:"2",author:"谨慎学子",title:"租房遇到假房东，幸好及时发现",content:"在网上看到一个价格很便宜的房子，房东说要先交定金。我要求看房产证，对方各种推脱。后来发现照片是盗用的，差点被骗！大家租房一定要实地看房，核实房东身份。",category:"租房陷阱",likes:18,comments:12,createdAt:"2024-01-14T15:20:00Z",isVerified:!1},{id:"3",author:"理性思考",title:"网贷诈骗套路深，大家要小心",content:'最近收到很多"无抵押快速放款"的短信，点进去看要求先交手续费。记住：正规贷款绝对不会要求预付费用！需要贷款的同学一定要通过银行等正规渠道。',category:"网贷陷阱",likes:31,comments:15,createdAt:"2024-01-13T09:45:00Z",isVerified:!0},{id:"4",author:"警觉青年",title:"冒充公检法电话，差点被吓到",content:'昨天接到电话说我涉嫌洗钱，要求我配合调查转账到"安全账户"。刚开始真的被吓到了，但想起游戏里的知识，公检法绝对不会电话要求转账！立即挂断并报警。',category:"冒充公检法",likes:27,comments:9,createdAt:"2024-01-12T14:10:00Z",isVerified:!0},{id:"5",author:"细心观察",title:"兼职刷单诈骗经历分享",content:'朋友介绍了一个"刷单兼职"，说日赚200很轻松。要求先垫付商品款，承诺立即返还并给佣金。幸好我在防诈游戏里学过，所有要求垫付的兼职都是诈骗！',category:"兼职诈骗",likes:22,comments:11,createdAt:"2024-01-11T16:30:00Z",isVerified:!1}];function CommunityPage(){let[e,t]=(0,r.useState)("all"),[s]=(0,r.useState)(h),x={all:"全部",求职骗局:"求职骗局",租房陷阱:"租房陷阱",网贷陷阱:"网贷陷阱",培训诈骗:"培训诈骗",电信诈骗:"电信诈骗",冒充公检法:"冒充公检法",兼职诈骗:"兼职诈骗"},u="all"===e?s:s.filter(t=>t.category===e),formatTimeAgo=e=>{let t=new Date(e),s=new Date,a=Math.floor((s.getTime()-t.getTime())/36e5);if(a<1)return"刚刚";if(a<24)return`${a}小时前`;let r=Math.floor(a/24);return r<7?`${r}天前`:t.toLocaleDateString()};return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[a.jsx("header",{className:"bg-white shadow-sm border-b",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(p(),{href:"/dashboard",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(i.Z,{className:"w-5 h-5 text-gray-600"})}),a.jsx(l.Z,{className:"w-8 h-8 text-primary-600"}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"社区"}),a.jsx("p",{className:"text-sm text-gray-600",children:"分享防诈骗经验，互相学习"})]})]})})}),(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"card mb-8",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"分类筛选"}),a.jsx("div",{className:"flex flex-wrap gap-2",children:["all","求职骗局","租房陷阱","网贷陷阱","培训诈骗","电信诈骗","冒充公检法","兼职诈骗"].map(s=>a.jsx("button",{onClick:()=>t(s),className:`px-4 py-2 rounded-full text-sm font-medium transition-all ${e===s?"bg-primary-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:x[s]},s))})]}),a.jsx("div",{className:"card mb-8 bg-gradient-to-r from-blue-500 to-purple-600 text-white",children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[a.jsx(n.Z,{className:"w-6 h-6 text-yellow-300 mt-1"}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-bold mb-2",children:"社区公告"}),a.jsx("p",{className:"text-blue-100 mb-4",children:"欢迎大家分享防诈骗经验！请注意保护个人隐私，不要透露具体的个人信息。 让我们一起建设一个安全、友善的学习社区。"}),(0,a.jsxs)("div",{className:"text-sm text-blue-200",children:["• 分享真实经历，帮助他人避免诈骗",a.jsx("br",{}),"• 保护个人隐私，不透露敏感信息",a.jsx("br",{}),"• 理性讨论，文明交流"]})]})]})}),a.jsx("div",{className:"space-y-6",children:0===u.length?(0,a.jsxs)("div",{className:"card text-center py-12",children:[a.jsx(c.Z,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-500",children:"暂无相关帖子"})]}):u.map(e=>(0,a.jsxs)("div",{className:"card hover:shadow-lg transition-shadow",children:[a.jsx("div",{className:"flex items-start justify-between mb-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx("div",{className:"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center",children:a.jsx(l.Z,{className:"w-5 h-5 text-primary-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx("span",{className:"font-semibold text-gray-900",children:e.author}),e.isVerified&&a.jsx(o.Z,{className:"w-4 h-4 text-blue-500"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[a.jsx(m.Z,{className:"w-4 h-4"}),formatTimeAgo(e.createdAt),a.jsx("span",{className:"px-2 py-1 bg-gray-100 rounded-full text-xs",children:e.category})]})]})]})}),a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:e.title}),a.jsx("p",{className:"text-gray-700 mb-4 leading-relaxed",children:e.content}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("button",{className:"flex items-center gap-2 text-gray-500 hover:text-primary-600 transition-colors",children:[a.jsx(d.Z,{className:"w-4 h-4"}),a.jsx("span",{className:"text-sm",children:e.likes})]}),(0,a.jsxs)("button",{className:"flex items-center gap-2 text-gray-500 hover:text-primary-600 transition-colors",children:[a.jsx(c.Z,{className:"w-4 h-4"}),a.jsx("span",{className:"text-sm",children:e.comments})]})]}),a.jsx("button",{className:"text-sm text-primary-600 hover:text-primary-700 font-medium",children:"查看详情"})]})]},e.id))}),(0,a.jsxs)("div",{className:"card mt-8 text-center",children:[a.jsx(c.Z,{className:"w-12 h-12 text-primary-600 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"分享你的经验"}),a.jsx("p",{className:"text-gray-600 mb-4",children:"遇到过诈骗或有防诈骗心得？分享给大家，帮助更多人提高警惕！"}),a.jsx("button",{className:"btn-primary",disabled:!0,children:"发布帖子 (开发中)"})]})]})]})}},2463:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>l,__esModule:()=>i,default:()=>c});var a=s(5153);let r=(0,a.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/community/page.tsx`),{__esModule:i,$$typeof:l}=r,n=r.default,c=n}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[9678,5607,6815,327,7726,2035],()=>__webpack_exec__(7470));module.exports=s})();