(()=>{var e={};e.id=7953,e.ids=[7953],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},215:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>u,tree:()=>d});var r=t(7096),a=t(6132),l=t(7284),i=t.n(l),n=t(2564),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["demo-quiz",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4906)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],o=["/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx"],m="/demo-quiz/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/demo-quiz/page",pathname:"/demo-quiz",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8856:(e,s,t)=>{Promise.resolve().then(t.bind(t,4640))},4640:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>DemoQuizPage});var r=t(784),a=t(9885),l=t(2032),i=t(4409),n=t(1057),c=t(7631),d=t(6386),o=t(8937),m=t(5441),x=t(7114),u=t(1440),h=t.n(u),p=t(2995);function DemoQuizPage(){(0,x.useRouter)();let[e,s]=(0,a.useState)(0),[t,u]=(0,a.useState)([]),[g,b]=(0,a.useState)(!1),[j,f]=(0,a.useState)(0),[N]=(0,a.useState)(Date.now()),[v,y]=(0,a.useState)(!1),w=p.F[e],_=e===p.F.length-1,q=t.filter((e,s)=>e===p.F[s].correctAnswer).length;(0,a.useEffect)(()=>{let e=setInterval(()=>{f(Math.floor((Date.now()-N)/1e3))},1e3);return()=>clearInterval(e)},[N]);let handleAnswerSelect=s=>{let r=[...t];r[e]=s,u(r)},formatTime=e=>`${Math.floor(e/60)}:${(e%60).toString().padStart(2,"0")}`;if(g){let s=q+(t[e]===w.correctAnswer?1:0),a=Math.round(s/p.F.length*100);return r.jsx("div",{className:"min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-blue-50 to-indigo-100",children:r.jsx("div",{className:"max-w-2xl w-full",children:(0,r.jsxs)("div",{className:"card text-center",children:[(0,r.jsxs)("div",{className:"mb-6",children:[a>=80?r.jsx(l.Z,{className:"w-16 h-16 text-success-500 mx-auto mb-4"}):a>=60?r.jsx(i.Z,{className:"w-16 h-16 text-yellow-500 mx-auto mb-4"}):r.jsx(n.Z,{className:"w-16 h-16 text-danger-500 mx-auto mb-4"}),r.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"体验完成！"}),r.jsx("p",{className:"text-gray-600",children:"感谢体验防诈骗知识测试"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"bg-primary-50 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-primary-600",children:[s,"/",p.F.length]}),r.jsx("div",{className:"text-sm text-gray-600",children:"正确题数"})]}),(0,r.jsxs)("div",{className:"bg-success-50 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-success-600",children:[a,"%"]}),r.jsx("div",{className:"text-sm text-gray-600",children:"正确率"})]}),(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[r.jsx("div",{className:"text-2xl font-bold text-blue-600",children:formatTime(j)}),r.jsx("div",{className:"text-sm text-gray-600",children:"用时"})]})]}),r.jsx("div",{className:"mb-8",children:a>=80?r.jsx("div",{className:"bg-success-50 border border-success-200 rounded-lg p-4",children:r.jsx("p",{className:"text-success-800 font-medium",children:"\uD83C\uDF89 优秀！你已经具备了良好的防诈骗意识！"})}):a>=60?r.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:r.jsx("p",{className:"text-yellow-800 font-medium",children:"⚠️ 不错！你的防诈骗知识还需要加强。"})}):r.jsx("div",{className:"bg-danger-50 border border-danger-200 rounded-lg p-4",children:r.jsx("p",{className:"text-danger-800 font-medium",children:"⚠️ 需要提高！建议认真学习防诈骗知识。"})})}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:[r.jsx("h3",{className:"font-semibold text-blue-900 mb-2",children:"想要更多功能？"}),r.jsx("p",{className:"text-blue-800 text-sm mb-3",children:"注册完整账户，解锁每日挑战、排行榜、社区交流等更多功能！"})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsxs)(h(),{href:"/login",className:"btn-primary flex items-center gap-2",children:["注册完整体验",r.jsx(c.Z,{className:"w-5 h-5"})]}),(0,r.jsxs)(h(),{href:"/demo-share",className:"btn-secondary flex items-center gap-2",children:["分享成绩",r.jsx(d.Z,{className:"w-5 h-5"})]}),r.jsx(h(),{href:"/",className:"btn-secondary",children:"返回首页"})]})]})})})}return r.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-4",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"text-center mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-4",children:[r.jsx(o.Z,{className:"w-8 h-8 text-primary-600"}),r.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"免费体验测试"})]}),r.jsx("p",{className:"text-gray-600",children:"10道防诈骗基础题目，测试你的防范意识"})]}),(0,r.jsxs)("div",{className:"card mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(m.Z,{className:"w-5 h-5 text-gray-500"}),(0,r.jsxs)("span",{className:"text-gray-600",children:["用时: ",formatTime(j)]})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[e+1," / ",p.F.length]})]}),r.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:r.jsx("div",{className:"bg-primary-600 h-2 rounded-full transition-all duration-300",style:{width:`${(e+1)/p.F.length*100}%`}})})]}),(0,r.jsxs)("div",{className:"card mb-6",children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-6",children:w.title}),r.jsx("div",{className:"space-y-3",children:w.options.map((s,a)=>r.jsx("button",{onClick:()=>handleAnswerSelect(a),className:`quiz-option ${t[e]===a?"selected":""} ${v?a===w.correctAnswer?"correct":t[e]===a?"incorrect":"":""}`,disabled:v,children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[r.jsx("div",{className:"flex-shrink-0 w-6 h-6 rounded-full border-2 border-current flex items-center justify-center text-sm font-medium",children:String.fromCharCode(65+a)}),(0,r.jsxs)("div",{className:"text-left",children:[r.jsx("div",{className:"font-medium",children:s.text}),v&&r.jsx("div",{className:"mt-2 text-sm opacity-80",children:s.explanation})]})]})},a))})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx(h(),{href:"/",className:"btn-secondary",children:"返回首页"}),(0,r.jsxs)("div",{className:"flex gap-3",children:[void 0!==t[e]&&!v&&r.jsx("button",{onClick:()=>{y(!0)},className:"btn-secondary",children:"查看解析"}),(0,r.jsxs)("button",{onClick:()=>{if(void 0===t[e]){alert("请选择一个答案");return}if(_){b(!0);let s={score:q+(t[e]===w.correctAnswer?1:0),total:p.F.length,timeUsed:j,completedAt:new Date().toISOString(),isDemo:!0};localStorage.setItem("demoResult",JSON.stringify(s))}else s(e+1),y(!1)},disabled:void 0===t[e],className:"btn-primary flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed",children:[_?"完成测试":"下一题",r.jsx(c.Z,{className:"w-5 h-5"})]})]})]})]})})}},4906:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>l,default:()=>c});var r=t(5153);let a=(0,r.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx`),{__esModule:l,$$typeof:i}=a,n=a.default,c=n}};var s=require("../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[9678,5607,8998,8749,2035,2995],()=>__webpack_exec__(215));module.exports=t})();