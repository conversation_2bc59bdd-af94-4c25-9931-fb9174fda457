/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/demo-quiz/page";
exports.ids = ["app/demo-quiz/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdemo-quiz%2Fpage&page=%2Fdemo-quiz%2Fpage&appPaths=%2Fdemo-quiz%2Fpage&pagePath=private-next-app-dir%2Fdemo-quiz%2Fpage.tsx&appDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdemo-quiz%2Fpage&page=%2Fdemo-quiz%2Fpage&appPaths=%2Fdemo-quiz%2Fpage&pagePath=private-next-app-dir%2Fdemo-quiz%2Fpage.tsx&appDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'demo-quiz',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/demo-quiz/page.tsx */ \"(rsc)/./app/demo-quiz/page.tsx\")), \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\")), \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/demo-quiz/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/demo-quiz/page\",\n        pathname: \"/demo-quiz\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdemo-quiz%2Fpage&page=%2Fdemo-quiz%2Fpage&appPaths=%2Fdemo-quiz%2Fpage&pagePath=private-next-app-dir%2Fdemo-quiz%2Fpage.tsx&appDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp%2Fdemo-quiz%2Fpage.tsx&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp%2Fdemo-quiz%2Fpage.tsx&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/demo-quiz/page.tsx */ \"(ssr)/./app/demo-quiz/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRnolMkZEZXNrdG9wJTJGdnNjb2RlJTJGJUU5JTk4JUIyJUU4JUFGJTg4JUU2JThDJTkxJUU2JTg4JTk4JUU2JUI4JUI4JUU2JTg4JThGJTJGYXBwJTJGZGVtby1xdWl6JTJGcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJhdWQtcHJldmVudGlvbi1nYW1lLz85OGY1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvei9EZXNrdG9wL3ZzY29kZS/pmLLor4jmjJHmiJjmuLjmiI8vYXBwL2RlbW8tcXVpei9wYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp%2Fdemo-quiz%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp%2Fglobals.css&server=true!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp%2Fglobals.css&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRnolMkZEZXNrdG9wJTJGdnNjb2RlJTJGJUU5JTk4JUIyJUU4JUFGJTg4JUU2JThDJTkxJUU2JTg4JTk4JUU2JUI4JUI4JUU2JTg4JThGJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGYXBwLXJvdXRlci5qcyZtb2R1bGVzPSUyRmhvbWUlMkZ6JTJGRGVza3RvcCUyRnZzY29kZSUyRiVFOSU5OCVCMiVFOCVBRiU4OCVFNiU4QyU5MSVFNiU4OCU5OCVFNiVCOCVCOCVFNiU4OCU4RiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmVycm9yLWJvdW5kYXJ5LmpzJm1vZHVsZXM9JTJGaG9tZSUyRnolMkZEZXNrdG9wJTJGdnNjb2RlJTJGJUU5JTk4JUIyJUU4JUFGJTg4JUU2JThDJTkxJUU2JTg4JTk4JUU2JUI4JUI4JUU2JTg4JThGJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPSUyRmhvbWUlMkZ6JTJGRGVza3RvcCUyRnZzY29kZSUyRiVFOSU5OCVCMiVFOCVBRiU4OCVFNiU4QyU5MSVFNiU4OCU5OCVFNiVCOCVCOCVFNiU4OCU4RiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRm5vdC1mb3VuZC1ib3VuZGFyeS5qcyZtb2R1bGVzPSUyRmhvbWUlMkZ6JTJGRGVza3RvcCUyRnZzY29kZSUyRiVFOSU5OCVCMiVFOCVBRiU4OCVFNiU4QyU5MSVFNiU4OCU5OCVFNiVCOCVCOCVFNiU4OCU4RiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRnJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMmbW9kdWxlcz0lMkZob21lJTJGeiUyRkRlc2t0b3AlMkZ2c2NvZGUlMkYlRTklOTglQjIlRTglQUYlODglRTYlOEMlOTElRTYlODglOTglRTYlQjglQjglRTYlODglOEYlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQXlIO0FBQ3pILDBPQUE2SDtBQUM3SCx3T0FBNEg7QUFDNUgsa1BBQWlJO0FBQ2pJLHNRQUEySTtBQUMzSSIsInNvdXJjZXMiOlsid2VicGFjazovL2ZyYXVkLXByZXZlbnRpb24tZ2FtZS8/ZjNjNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL3ovRGVza3RvcC92c2NvZGUv6Ziy6K+I5oyR5oiY5ri45oiPL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYXBwLXJvdXRlci5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvei9EZXNrdG9wL3ZzY29kZS/pmLLor4jmjJHmiJjmuLjmiI8vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeS5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvei9EZXNrdG9wL3ZzY29kZS/pmLLor4jmjJHmiJjmuLjmiI8vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9sYXlvdXQtcm91dGVyLmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS96L0Rlc2t0b3AvdnNjb2RlL+mYsuiviOaMkeaImOa4uOaIjy9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1ib3VuZGFyeS5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvei9EZXNrdG9wL3ZzY29kZS/pmLLor4jmjJHmiJjmuLjmiI8vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9yZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS96L0Rlc2t0b3AvdnNjb2RlL+mYsuiviOaMkeaImOa4uOaIjy9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3N0YXRpYy1nZW5lcmF0aW9uLXNlYXJjaHBhcmFtcy1iYWlsb3V0LXByb3ZpZGVyLmpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRnolMkZEZXNrdG9wJTJGdnNjb2RlJTJGJUU5JTk4JUIyJUU4JUFGJTg4JUU2JThDJTkxJUU2JTg4JTk4JUU2JUI4JUI4JUU2JTg4JThGJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZsaW5rLmpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2ZyYXVkLXByZXZlbnRpb24tZ2FtZS8/OTVjNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL3ovRGVza3RvcC92c2NvZGUv6Ziy6K+I5oyR5oiY5ri45oiPL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2xpbmsuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/demo-quiz/page.tsx":
/*!********************************!*\
  !*** ./app/demo-quiz/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DemoQuizPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_BookOpen_CheckCircle_Clock_Share2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,BookOpen,CheckCircle,Clock,Share2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_BookOpen_CheckCircle_Clock_Share2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,BookOpen,CheckCircle,Clock,Share2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_BookOpen_CheckCircle_Clock_Share2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,BookOpen,CheckCircle,Clock,Share2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_BookOpen_CheckCircle_Clock_Share2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,BookOpen,CheckCircle,Clock,Share2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_BookOpen_CheckCircle_Clock_Share2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,BookOpen,CheckCircle,Clock,Share2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_BookOpen_CheckCircle_Clock_Share2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,BookOpen,CheckCircle,Clock,Share2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_BookOpen_CheckCircle_Clock_Share2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,BookOpen,CheckCircle,Clock,Share2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _data_questions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/data/questions */ \"(ssr)/./data/questions.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction DemoQuizPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentQuestionIndex, setCurrentQuestionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedAnswers, setSelectedAnswers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showResult, setShowResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [timeUsed, setTimeUsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [startTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const [showExplanation, setShowExplanation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const currentQuestion = _data_questions__WEBPACK_IMPORTED_MODULE_4__.STARTER_QUESTIONS[currentQuestionIndex];\n    const isLastQuestion = currentQuestionIndex === _data_questions__WEBPACK_IMPORTED_MODULE_4__.STARTER_QUESTIONS.length - 1;\n    const correctAnswers = selectedAnswers.filter((answer, index)=>answer === _data_questions__WEBPACK_IMPORTED_MODULE_4__.STARTER_QUESTIONS[index].correctAnswer).length;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setTimeUsed(Math.floor((Date.now() - startTime) / 1000));\n        }, 1000);\n        return ()=>clearInterval(timer);\n    }, [\n        startTime\n    ]);\n    const handleAnswerSelect = (answerIndex)=>{\n        const newAnswers = [\n            ...selectedAnswers\n        ];\n        newAnswers[currentQuestionIndex] = answerIndex;\n        setSelectedAnswers(newAnswers);\n    };\n    const handleNext = ()=>{\n        if (selectedAnswers[currentQuestionIndex] === undefined) {\n            alert(\"请选择一个答案\");\n            return;\n        }\n        if (isLastQuestion) {\n            setShowResult(true);\n            // 保存体验结果\n            const result = {\n                score: correctAnswers + (selectedAnswers[currentQuestionIndex] === currentQuestion.correctAnswer ? 1 : 0),\n                total: _data_questions__WEBPACK_IMPORTED_MODULE_4__.STARTER_QUESTIONS.length,\n                timeUsed,\n                completedAt: new Date().toISOString(),\n                isDemo: true\n            };\n            localStorage.setItem(\"demoResult\", JSON.stringify(result));\n        } else {\n            setCurrentQuestionIndex(currentQuestionIndex + 1);\n            setShowExplanation(false);\n        }\n    };\n    const handleShowExplanation = ()=>{\n        setShowExplanation(true);\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins}:${secs.toString().padStart(2, \"0\")}`;\n    };\n    if (showResult) {\n        const finalScore = correctAnswers + (selectedAnswers[currentQuestionIndex] === currentQuestion.correctAnswer ? 1 : 0);\n        const percentage = Math.round(finalScore / _data_questions__WEBPACK_IMPORTED_MODULE_4__.STARTER_QUESTIONS.length * 100);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-blue-50 to-indigo-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-2xl w-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                percentage >= 80 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_BookOpen_CheckCircle_Clock_Share2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-16 h-16 text-success-500 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 17\n                                }, this) : percentage >= 60 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_BookOpen_CheckCircle_Clock_Share2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-16 h-16 text-yellow-500 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_BookOpen_CheckCircle_Clock_Share2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-16 h-16 text-danger-500 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                    children: \"体验完成！\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"感谢体验防诈骗知识测试\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-primary-50 rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-primary-600\",\n                                            children: [\n                                                finalScore,\n                                                \"/\",\n                                                _data_questions__WEBPACK_IMPORTED_MODULE_4__.STARTER_QUESTIONS.length\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"正确题数\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-success-50 rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-success-600\",\n                                            children: [\n                                                percentage,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"正确率\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-50 rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: formatTime(timeUsed)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"用时\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: percentage >= 80 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-success-50 border border-success-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-success-800 font-medium\",\n                                    children: \"\\uD83C\\uDF89 优秀！你已经具备了良好的防诈骗意识！\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 17\n                            }, this) : percentage >= 60 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-800 font-medium\",\n                                    children: \"⚠️ 不错！你的防诈骗知识还需要加强。\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-danger-50 border border-danger-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-danger-800 font-medium\",\n                                    children: \"⚠️ 需要提高！建议认真学习防诈骗知识。\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-blue-900 mb-2\",\n                                    children: \"想要更多功能？\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-800 text-sm mb-3\",\n                                    children: \"注册完整账户，解锁每日挑战、排行榜、社区交流等更多功能！\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/login\",\n                                    className: \"btn-primary flex items-center gap-2\",\n                                    children: [\n                                        \"注册完整体验\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_BookOpen_CheckCircle_Clock_Share2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/demo-share\",\n                                    className: \"btn-secondary flex items-center gap-2\",\n                                    children: [\n                                        \"分享成绩\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_BookOpen_CheckCircle_Clock_Share2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/\",\n                                    className: \"btn-secondary\",\n                                    children: \"返回首页\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center gap-2 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_BookOpen_CheckCircle_Clock_Share2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-8 h-8 text-primary-600\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"免费体验测试\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"10道防诈骗基础题目，测试你的防范意识\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_BookOpen_CheckCircle_Clock_Share2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-5 h-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                \"用时: \",\n                                                formatTime(timeUsed)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        currentQuestionIndex + 1,\n                                        \" / \",\n                                        _data_questions__WEBPACK_IMPORTED_MODULE_4__.STARTER_QUESTIONS.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-200 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-primary-600 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: `${(currentQuestionIndex + 1) / _data_questions__WEBPACK_IMPORTED_MODULE_4__.STARTER_QUESTIONS.length * 100}%`\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-6\",\n                            children: currentQuestion.title\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: currentQuestion.options.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleAnswerSelect(index),\n                                    className: `quiz-option ${selectedAnswers[currentQuestionIndex] === index ? \"selected\" : \"\"} ${showExplanation ? index === currentQuestion.correctAnswer ? \"correct\" : selectedAnswers[currentQuestionIndex] === index ? \"incorrect\" : \"\" : \"\"}`,\n                                    disabled: showExplanation,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 w-6 h-6 rounded-full border-2 border-current flex items-center justify-center text-sm font-medium\",\n                                                children: String.fromCharCode(65 + index)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: option.text\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    showExplanation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 text-sm opacity-80\",\n                                                        children: option.explanation\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/\",\n                            className: \"btn-secondary\",\n                            children: \"返回首页\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                selectedAnswers[currentQuestionIndex] !== undefined && !showExplanation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleShowExplanation,\n                                    className: \"btn-secondary\",\n                                    children: \"查看解析\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleNext,\n                                    disabled: selectedAnswers[currentQuestionIndex] === undefined,\n                                    className: \"btn-primary flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: [\n                                        isLastQuestion ? \"完成测试\" : \"下一题\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_BookOpen_CheckCircle_Clock_Share2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n            lineNumber: 160,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/demo-quiz/page.tsx\n");

/***/ }),

/***/ "(ssr)/./data/questions.ts":
/*!***************************!*\
  !*** ./data/questions.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STARTER_QUESTIONS: () => (/* binding */ STARTER_QUESTIONS)\n/* harmony export */ });\n// 入门题库 - 10道精选题目\nconst STARTER_QUESTIONS = [\n    {\n        id: \"starter-1\",\n        title: '你在求职网站上看到一份\"高薪轻松\"的工作，要求先交纳\"培训费\"或\"保证金\"，你应该：',\n        options: [\n            {\n                text: \"立即交费，抢占先机\",\n                explanation: \"正规公司不会要求求职者预先交费，这是典型的求职诈骗手段\"\n            },\n            {\n                text: \"先交一部分试试\",\n                explanation: \"任何要求预付费用的招聘都是诈骗，不要抱有侥幸心理\"\n            },\n            {\n                text: \"拒绝交费，举报该信息\",\n                explanation: \"正确！正规招聘不会要求求职者交费，应该立即举报\"\n            },\n            {\n                text: \"和朋友商量后再决定\",\n                explanation: \"虽然商量是好习惯，但这种明显的诈骗应该直接拒绝\"\n            }\n        ],\n        correctAnswer: 2,\n        category: \"job-fraud\",\n        difficulty: \"basic\",\n        explanation: '正规公司招聘员工是为了创造价值，不会向求职者收取任何费用。凡是要求交\"培训费\"、\"保证金\"、\"服装费\"等的招聘信息都是诈骗。',\n        status: \"approved\",\n        createdAt: new Date(\"2024-01-01\"),\n        approvedAt: new Date(\"2024-01-01\")\n    },\n    {\n        id: \"starter-2\",\n        title: '接到自称\"公安局\"的电话，说你涉嫌洗钱需要配合调查，要求你将银行卡内资金转到\"安全账户\"，你应该：',\n        options: [\n            {\n                text: \"立即按要求转账配合调查\",\n                explanation: \"这是典型的冒充公检法诈骗，真正的执法部门不会通过电话要求转账\"\n            },\n            {\n                text: \"要求对方提供警官证号码\",\n                explanation: \"虽然有防范意识，但诈骗分子可能提供虚假信息，最好的做法是直接挂断\"\n            },\n            {\n                text: \"挂断电话，到就近派出所核实\",\n                explanation: \"正确！遇到此类电话应立即挂断，到官方机构核实情况\"\n            },\n            {\n                text: \"先转一部分钱试探真假\",\n                explanation: \"绝对不能转账！这是诈骗分子常用的心理战术\"\n            }\n        ],\n        correctAnswer: 2,\n        category: \"fake-authority\",\n        difficulty: \"basic\",\n        explanation: '公检法机关不会通过电话要求当事人转账汇款，更不存在所谓的\"安全账户\"。接到此类电话应立即挂断，通过官方渠道核实。',\n        status: \"approved\",\n        createdAt: new Date(\"2024-01-01\"),\n        approvedAt: new Date(\"2024-01-01\")\n    },\n    {\n        id: \"starter-3\",\n        title: '在网上看到\"日赚500元，在家兼职\"的广告，要求先购买\"工作包\"或\"激活码\"，你的判断是：',\n        options: [\n            {\n                text: \"可能是真的，先试试\",\n                explanation: \"这是典型的兼职诈骗，正规兼职不需要预付费用\"\n            },\n            {\n                text: \"肯定是诈骗，直接忽略\",\n                explanation: \"正确！要求预付费用的兼职广告都是诈骗\"\n            },\n            {\n                text: \"联系客服详细了解\",\n                explanation: '诈骗分子会有专门的\"客服\"进行洗脑，不要主动联系'\n            },\n            {\n                text: \"看看别人的评价再决定\",\n                explanation: \"网上的评价可能是刷出来的，不能作为判断依据\"\n            }\n        ],\n        correctAnswer: 1,\n        category: \"part-time-scam\",\n        difficulty: \"basic\",\n        explanation: '正规的兼职工作是雇主付费给员工，而不是员工先付费给雇主。任何要求预付\"工作包\"、\"激活码\"、\"保证金\"的兼职都是诈骗。',\n        status: \"approved\",\n        createdAt: new Date(\"2024-01-01\"),\n        approvedAt: new Date(\"2024-01-01\")\n    },\n    {\n        id: \"starter-4\",\n        title: \"收到短信称你的快递丢失，需要点击链接填写信息申请赔偿，你应该：\",\n        options: [\n            {\n                text: \"立即点击链接填写信息\",\n                explanation: \"这可能是钓鱼链接，会窃取你的个人信息和银行卡信息\"\n            },\n            {\n                text: \"先确认是否真的有快递丢失\",\n                explanation: \"正确！应该通过官方渠道核实快递状态，不要轻信短信\"\n            },\n            {\n                text: \"转发给朋友帮忙判断\",\n                explanation: \"不要转发可疑链接，可能会害了朋友\"\n            },\n            {\n                text: \"直接删除短信\",\n                explanation: \"删除是对的，但最好还是核实一下是否真有快递\"\n            }\n        ],\n        correctAnswer: 1,\n        category: \"telecom-fraud\",\n        difficulty: \"basic\",\n        explanation: \"快递公司的正规赔偿流程不会通过短信链接进行。收到此类短信应通过快递公司官方客服或APP核实情况。\",\n        status: \"approved\",\n        createdAt: new Date(\"2024-01-01\"),\n        approvedAt: new Date(\"2024-01-01\")\n    },\n    {\n        id: \"starter-5\",\n        title: '在租房时，房东要求你先交一年房租和押金，但拒绝签署正式租房合同，声称\"信任就够了\"，你应该：',\n        options: [\n            {\n                text: \"相信房东，先交钱再说\",\n                explanation: \"没有合同保障的租房存在巨大风险，可能遇到假房东诈骗\"\n            },\n            {\n                text: \"要求签署正式合同才交钱\",\n                explanation: \"正确！正规租房必须签署合同，这是保护双方权益的基本要求\"\n            },\n            {\n                text: \"只交押金，房租按月付\",\n                explanation: \"虽然降低了风险，但没有合同仍然不安全\"\n            },\n            {\n                text: \"找中介公司代为处理\",\n                explanation: \"找正规中介是好方法，但关键还是要有正式合同\"\n            }\n        ],\n        correctAnswer: 1,\n        category: \"rental-scam\",\n        difficulty: \"basic\",\n        explanation: \"正规租房必须签署租房合同，明确双方的权利义务。拒绝签合同的房东很可能是骗子，要么是假房东，要么想在后续过程中耍赖。\",\n        status: \"approved\",\n        createdAt: new Date(\"2024-01-01\"),\n        approvedAt: new Date(\"2024-01-01\")\n    },\n    {\n        id: \"starter-6\",\n        title: '网上看到\"免费领取iPhone\"的活动，只需要填写个人信息和银行卡号\"验证身份\"，你认为：',\n        options: [\n            {\n                text: \"免费的不要白不要，赶紧参加\",\n                explanation: \"天下没有免费的午餐，这是典型的信息收集诈骗\"\n            },\n            {\n                text: \"填写信息但不填银行卡号\",\n                explanation: \"个人信息也很重要，不应该随意泄露给不明网站\"\n            },\n            {\n                text: \"这是诈骗，不要参与\",\n                explanation: '正确！这种\"免费领取\"活动通常是为了收集个人信息进行诈骗'\n            },\n            {\n                text: \"先看看活动是否真实\",\n                explanation: \"即使活动看起来真实，也不应该向不明网站提供敏感信息\"\n            }\n        ],\n        correctAnswer: 2,\n        category: \"telecom-fraud\",\n        difficulty: \"basic\",\n        explanation: '诈骗分子经常用\"免费领取\"、\"中奖\"等噱头收集个人信息，然后用于后续的精准诈骗。不要向不明网站提供个人敏感信息。',\n        status: \"approved\",\n        createdAt: new Date(\"2024-01-01\"),\n        approvedAt: new Date(\"2024-01-01\")\n    },\n    {\n        id: \"starter-7\",\n        title: '朋友推荐你参加一个\"投资理财培训班\"，声称\"包教包会，月收益30%\"，学费需要预交5000元，你应该：',\n        options: [\n            {\n                text: \"朋友推荐的应该可信，报名参加\",\n                explanation: \"即使是朋友推荐，也要理性判断。月收益30%是不现实的承诺\"\n            },\n            {\n                text: \"先交一部分学费试试效果\",\n                explanation: \"任何承诺高收益的培训都要谨慎，不要轻易交费\"\n            },\n            {\n                text: \"拒绝参加，提醒朋友注意风险\",\n                explanation: \"正确！月收益30%是不可能的，这很可能是传销或诈骗\"\n            },\n            {\n                text: \"要求先免费试听再决定\",\n                explanation: \"虽然谨慎，但承诺如此高收益的培训本身就不可信\"\n            }\n        ],\n        correctAnswer: 2,\n        category: \"training-scam\",\n        difficulty: \"basic\",\n        explanation: \"正规的投资理财年收益能达到10%就已经很不错了，月收益30%是完全不现实的。这种培训通常是传销或诈骗的幌子。\",\n        status: \"approved\",\n        createdAt: new Date(\"2024-01-01\"),\n        approvedAt: new Date(\"2024-01-01\")\n    },\n    {\n        id: \"starter-8\",\n        title: '急需用钱时，看到网贷广告\"无需抵押，当天放款，利息超低\"，申请时要求先交\"手续费\"，你应该：',\n        options: [\n            {\n                text: \"急需用钱，先交手续费\",\n                explanation: \"正规贷款机构不会要求预交手续费，这是典型的贷款诈骗\"\n            },\n            {\n                text: \"拒绝交费，寻找正规贷款渠道\",\n                explanation: \"正确！正规贷款不需要预交费用，应该通过银行等正规渠道贷款\"\n            },\n            {\n                text: \"讨价还价，减少手续费\",\n                explanation: \"无论多少手续费都不应该交，这就是诈骗\"\n            },\n            {\n                text: \"要求对方提供营业执照\",\n                explanation: \"虽然有防范意识，但要求预交费用的贷款本身就是诈骗\"\n            }\n        ],\n        correctAnswer: 1,\n        category: \"loan-trap\",\n        difficulty: \"basic\",\n        explanation: '正规的贷款机构（如银行）不会要求借款人预先支付任何费用。所有要求预交\"手续费\"、\"保证金\"、\"激活费\"的贷款都是诈骗。',\n        status: \"approved\",\n        createdAt: new Date(\"2024-01-01\"),\n        approvedAt: new Date(\"2024-01-01\")\n    },\n    {\n        id: \"starter-9\",\n        title: '收到银行短信说你的信用卡被盗刷，需要立即点击链接\"冻结账户\"，你应该：',\n        options: [\n            {\n                text: \"立即点击链接冻结账户\",\n                explanation: \"这可能是钓鱼短信，真正的银行不会通过短信链接处理账户问题\"\n            },\n            {\n                text: \"拨打银行官方客服电话核实\",\n                explanation: \"正确！应该通过官方渠道核实情况，不要点击短信中的链接\"\n            },\n            {\n                text: \"先查看信用卡账单\",\n                explanation: \"查看账单是好习惯，但更重要的是通过官方渠道核实短信真伪\"\n            },\n            {\n                text: \"转发给朋友帮忙判断\",\n                explanation: \"不要转发可疑短信，应该直接联系银行官方客服\"\n            }\n        ],\n        correctAnswer: 1,\n        category: \"telecom-fraud\",\n        difficulty: \"basic\",\n        explanation: \"银行的正规通知不会要求客户点击短信链接处理账户问题。收到此类短信应拨打银行官方客服电话核实，不要点击任何链接。\",\n        status: \"approved\",\n        createdAt: new Date(\"2024-01-01\"),\n        approvedAt: new Date(\"2024-01-01\")\n    },\n    {\n        id: \"starter-10\",\n        title: '在社交媒体上看到\"代购名牌包包，价格只要专柜的三分之一\"的广告，要求先付全款再发货，你认为：',\n        options: [\n            {\n                text: \"价格便宜，值得一试\",\n                explanation: \"价格过低的名牌商品很可能是假货，或者是诈骗\"\n            },\n            {\n                text: \"要求货到付款\",\n                explanation: \"虽然降低了风险，但仍可能收到假货，最好通过正规渠道购买\"\n            },\n            {\n                text: \"这很可能是诈骗，不要购买\",\n                explanation: \"正确！价格异常低廉的名牌商品很可能是诈骗或假货\"\n            },\n            {\n                text: \"先付定金，收货后付尾款\",\n                explanation: \"即使分期付款，也存在收到假货或被骗定金的风险\"\n            }\n        ],\n        correctAnswer: 2,\n        category: \"telecom-fraud\",\n        difficulty: \"basic\",\n        explanation: \"正品名牌商品有严格的价格体系，价格异常低廉的商品要么是假货，要么是诈骗。应该通过官方渠道或正规商城购买。\",\n        status: \"approved\",\n        createdAt: new Date(\"2024-01-01\"),\n        approvedAt: new Date(\"2024-01-01\")\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./data/questions.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"11b37adaa9c3\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mcmF1ZC1wcmV2ZW50aW9uLWdhbWUvLi9hcHAvZ2xvYmFscy5jc3M/ODJlZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjExYjM3YWRhYTljM1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/demo-quiz/page.tsx":
/*!********************************!*\
  !*** ./app/demo-quiz/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-quiz/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: \"大学生防诈挑战游戏\",\n    description: \"通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱\",\n    keywords: \"防诈骗,大学生,安全教育,答题游戏\",\n    authors: [\n        {\n            name: \"防诈挑战游戏团队\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxVQUFVO0lBQ1ZDLFNBQVM7UUFBQztZQUFFQyxNQUFNO1FBQVc7S0FBRTtBQUNqQyxFQUFDO0FBRU0sTUFBTUMsV0FBVztJQUN0QkMsT0FBTztJQUNQQyxjQUFjO0FBQ2hCLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7c0JBQ0MsNEVBQUNDO2dCQUFJQyxXQUFVOzBCQUNaTDs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJhdWQtcHJldmVudGlvbi1nYW1lLy4vYXBwL2xheW91dC50c3g/OTk4OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICflpKflrabnlJ/pmLLor4jmjJHmiJjmuLjmiI8nLFxuICBkZXNjcmlwdGlvbjogJ+mAmui/h+etlOmimOaMkeaImOaPkOWNh+mYsuiviOmql+aEj+ivhu+8jOS/neaKpOiHquW3sei/nOemu+WQhOenjeiviOmql+mZt+mYsScsXG4gIGtleXdvcmRzOiAn6Ziy6K+I6aqXLOWkp+WtpueUnyzlronlhajmlZnogrIs562U6aKY5ri45oiPJyxcbiAgYXV0aG9yczogW3sgbmFtZTogJ+mYsuiviOaMkeaImOa4uOaIj+WboumYnycgfV0sXG59XG5cbmV4cG9ydCBjb25zdCB2aWV3cG9ydCA9IHtcbiAgd2lkdGg6ICdkZXZpY2Utd2lkdGgnLFxuICBpbml0aWFsU2NhbGU6IDEsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHk+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuXCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJrZXl3b3JkcyIsImF1dGhvcnMiLCJuYW1lIiwidmlld3BvcnQiLCJ3aWR0aCIsImluaXRpYWxTY2FsZSIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Home!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Home!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Home!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-16 h-16 text-yellow-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 10,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-6xl font-bold text-gray-900 mb-2\",\n                                children: \"404\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 11,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-700 mb-4\",\n                                children: \"页面未找到\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 12,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"抱歉，你访问的页面不存在。可能是链接错误或页面已被移除。\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 13,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"btn-primary w-full flex items-center justify-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"返回首页\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"btn-secondary w-full flex items-center justify-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"返回上一页\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-800 text-sm\",\n                            children: [\n                                \"\\uD83D\\uDCA1 \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"提示：\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 18\n                                }, this),\n                                \"如果你是通过链接访问的，请检查链接是否正确。 如果问题持续存在，请联系我们。\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                lineNumber: 8,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/not-found.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdemo-quiz%2Fpage&page=%2Fdemo-quiz%2Fpage&appPaths=%2Fdemo-quiz%2Fpage&pagePath=private-next-app-dir%2Fdemo-quiz%2Fpage.tsx&appDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();