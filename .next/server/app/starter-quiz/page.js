(()=>{var e={};e.id=8626,e.ids=[8626],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},2745:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>u,tree:()=>d});var r=t(7096),a=t(6132),l=t(7284),i=t.n(l),n=t(2564),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["starter-quiz",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8796)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/starter-quiz/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],o=["/home/<USER>/Desktop/vscode/防诈挑战游戏/app/starter-quiz/page.tsx"],x="/starter-quiz/page",m={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/starter-quiz/page",pathname:"/starter-quiz",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8514:(e,s,t)=>{Promise.resolve().then(t.bind(t,8752))},8752:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>StarterQuizPage});var r=t(784),a=t(9885),l=t(2032),i=t(4409),n=t(1057),c=t(7631),d=t(8937),o=t(5441),x=t(7114),m=t(2995);function StarterQuizPage(){let e=(0,x.useRouter)(),[s,t]=(0,a.useState)(0),[u,h]=(0,a.useState)([]),[p,g]=(0,a.useState)(!1),[j,b]=(0,a.useState)(0),[f]=(0,a.useState)(Date.now()),[v,N]=(0,a.useState)(!1),y=m.F[s],w=s===m.F.length-1,_=u.filter((e,s)=>e===m.F[s].correctAnswer).length;(0,a.useEffect)(()=>{let s=localStorage.getItem("userData");if(!s){e.push("/login");return}let t=localStorage.getItem("starterCompleted");if(t){e.push("/dashboard");return}let r=setInterval(()=>{b(Math.floor((Date.now()-f)/1e3))},1e3);return()=>clearInterval(r)},[e,f]);let handleAnswerSelect=e=>{let t=[...u];t[s]=e,h(t)},formatTime=e=>`${Math.floor(e/60)}:${(e%60).toString().padStart(2,"0")}`;if(p){let t=_+(u[s]===y.correctAnswer?1:0),a=Math.round(t/m.F.length*100);return r.jsx("div",{className:"min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8",children:r.jsx("div",{className:"max-w-2xl w-full",children:(0,r.jsxs)("div",{className:"card text-center",children:[(0,r.jsxs)("div",{className:"mb-6",children:[a>=80?r.jsx(l.Z,{className:"w-16 h-16 text-success-500 mx-auto mb-4"}):a>=60?r.jsx(i.Z,{className:"w-16 h-16 text-yellow-500 mx-auto mb-4"}):r.jsx(n.Z,{className:"w-16 h-16 text-danger-500 mx-auto mb-4"}),r.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"入门挑战完成！"}),r.jsx("p",{className:"text-gray-600",children:"恭喜你完成了防诈骗基础知识测试"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"bg-primary-50 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-primary-600",children:[t,"/",m.F.length]}),r.jsx("div",{className:"text-sm text-gray-600",children:"正确题数"})]}),(0,r.jsxs)("div",{className:"bg-success-50 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-success-600",children:[a,"%"]}),r.jsx("div",{className:"text-sm text-gray-600",children:"正确率"})]}),(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[r.jsx("div",{className:"text-2xl font-bold text-blue-600",children:formatTime(j)}),r.jsx("div",{className:"text-sm text-gray-600",children:"用时"})]})]}),r.jsx("div",{className:"mb-8",children:a>=80?r.jsx("div",{className:"bg-success-50 border border-success-200 rounded-lg p-4",children:r.jsx("p",{className:"text-success-800 font-medium",children:"\uD83C\uDF89 优秀！你已经具备了良好的防诈骗意识，现在可以参与每日挑战了！"})}):a>=60?r.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:r.jsx("p",{className:"text-yellow-800 font-medium",children:"⚠️ 不错！你的防诈骗知识还需要加强，建议多参与挑战提升能力。"})}):r.jsx("div",{className:"bg-danger-50 border border-danger-200 rounded-lg p-4",children:r.jsx("p",{className:"text-danger-800 font-medium",children:"⚠️ 需要提高！你的防诈骗意识还比较薄弱，建议认真学习相关知识。"})})}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsxs)("button",{onClick:()=>e.push("/dashboard"),className:"btn-primary flex items-center gap-2",children:["进入主界面",r.jsx(c.Z,{className:"w-5 h-5"})]}),r.jsx("button",{onClick:()=>{localStorage.removeItem("starterCompleted"),window.location.reload()},className:"btn-secondary",children:"重新测试"})]})]})})})}return r.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-4",children:[r.jsx(d.Z,{className:"w-8 h-8 text-primary-600"}),r.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"入门挑战"})]}),r.jsx("p",{className:"text-gray-600",children:"完成这10道基础题目，解锁每日挑战模式"})]}),(0,r.jsxs)("div",{className:"card mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(o.Z,{className:"w-5 h-5 text-gray-500"}),(0,r.jsxs)("span",{className:"text-gray-600",children:["用时: ",formatTime(j)]})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[s+1," / ",m.F.length]})]}),r.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:r.jsx("div",{className:"bg-primary-600 h-2 rounded-full transition-all duration-300",style:{width:`${(s+1)/m.F.length*100}%`}})})]}),(0,r.jsxs)("div",{className:"card mb-8",children:[r.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-6",children:y.title}),r.jsx("div",{className:"space-y-3",children:y.options.map((e,t)=>r.jsx("button",{onClick:()=>handleAnswerSelect(t),className:`quiz-option ${u[s]===t?"selected":""} ${v?t===y.correctAnswer?"correct":u[s]===t?"incorrect":"":""}`,disabled:v,children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[r.jsx("div",{className:"flex-shrink-0 w-6 h-6 rounded-full border-2 border-current flex items-center justify-center text-sm font-medium",children:String.fromCharCode(65+t)}),(0,r.jsxs)("div",{className:"text-left",children:[r.jsx("div",{className:"font-medium",children:e.text}),v&&r.jsx("div",{className:"mt-2 text-sm opacity-80",children:e.explanation})]})]})},t))})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("button",{onClick:()=>e.push("/dashboard"),className:"btn-secondary",children:"退出测试"}),(0,r.jsxs)("div",{className:"flex gap-3",children:[void 0!==u[s]&&!v&&r.jsx("button",{onClick:()=>{N(!0)},className:"btn-secondary",children:"查看解析"}),(0,r.jsxs)("button",{onClick:()=>{if(void 0===u[s]){alert("请选择一个答案");return}w?(g(!0),localStorage.setItem("starterCompleted","true"),localStorage.setItem("starterResult",JSON.stringify({score:_+(u[s]===y.correctAnswer?1:0),total:m.F.length,timeUsed:j,completedAt:new Date().toISOString()}))):(t(s+1),N(!1))},disabled:void 0===u[s],className:"btn-primary flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed",children:[w?"完成测试":"下一题",r.jsx(c.Z,{className:"w-5 h-5"})]})]})]})]})})}},8796:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>l,default:()=>c});var r=t(5153);let a=(0,r.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/starter-quiz/page.tsx`),{__esModule:l,$$typeof:i}=a,n=a.default,c=n}};var s=require("../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[9678,5607,8998,6110,2035,2995],()=>__webpack_exec__(2745));module.exports=t})();