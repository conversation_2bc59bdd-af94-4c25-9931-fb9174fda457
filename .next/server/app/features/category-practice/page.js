(()=>{var e={};e.id=4699,e.ids=[4699],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},5953:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>c.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>u,tree:()=>n});var a=t(7096),r=t(6132),i=t(7284),c=t.n(i),l=t(2564),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let n=["",{children:["features",{children:["category-practice",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2454)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/category-practice/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],o=["/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/category-practice/page.tsx"],x="/features/category-practice/page",m={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/features/category-practice/page",pathname:"/features/category-practice",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},7042:(e,s,t)=>{Promise.resolve().then(t.bind(t,8023))},8023:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>CategoryPracticePage});var a=t(784),r=t(9885),i=t(2032),c=t(7552),l=t(5441),d=t(9803),n=t(696),o=t(1440),x=t.n(o);function CategoryPracticePage(){let[e,s]=(0,r.useState)("beginner"),t={totalCategories:7,completedCategories:3,totalQuestions:180,answeredQuestions:85,averageAccuracy:82.5,studyTime:45},getDifficultyColor=e=>{switch(e){case"beginner":return"text-green-600 bg-green-100";case"intermediate":return"text-yellow-600 bg-yellow-100";case"advanced":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},getDifficultyLabel=e=>{switch(e){case"beginner":return"入门";case"intermediate":return"进阶";case"advanced":return"高级";default:return"未知"}},getStatusIcon=e=>{switch(e){case"completed":return a.jsx(i.Z,{className:"w-5 h-5 text-green-600"});case"current":return a.jsx(c.Z,{className:"w-5 h-5 text-blue-600"});case"upcoming":return a.jsx(l.Z,{className:"w-5 h-5 text-gray-400"});default:return null}};return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-indigo-50 to-purple-100",children:[a.jsx("header",{className:"bg-white shadow-sm border-b",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(x(),{href:"/",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(d.Z,{className:"w-5 h-5 text-gray-600"})}),a.jsx(n.Z,{className:"w-8 h-8 text-indigo-600"}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"分类刷题"}),a.jsx("p",{className:"text-sm text-gray-600",children:"针对性强化，系统提升"})]})]})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"card mb-8 bg-gradient-to-r from-indigo-500 to-purple-600 text-white",children:[a.jsx("h2",{className:"text-2xl font-bold mb-6",children:"学习进度统计"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[t.completedCategories,"/",t.totalCategories]}),a.jsx("div",{className:"text-sm text-indigo-200",children:"完成分类"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[t.answeredQuestions,"/",t.totalQuestions]}),a.jsx("div",{className:"text-sm text-indigo-200",children:"答题进度"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[t.averageAccuracy,"%"]}),a.jsx("div",{className:"text-sm text-indigo-200",children:"平均正确率"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[t.studyTime,"h"]}),a.jsx("div",{className:"text-sm text-indigo-200",children:"学习时长"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[a.jsx("div",{className:"lg:col-span-2",children:(0,a.jsxs)("div",{className:"card mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[a.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"练习分类"}),a.jsx("div",{className:"flex gap-2",children:["beginner","intermediate","advanced"].map(t=>a.jsx("button",{onClick:()=>s(t),className:`px-3 py-1 rounded-lg text-sm transition-all ${e===t?getDifficultyColor(t):"bg-gray-100 text-gray-600 hover:bg-gray-200"}`,children:getDifficultyLabel(t)},t))})]}),a.jsx("div",{className:"space-y-4",children:[{id:"job-fraud",title:"求职防骗",icon:"\uD83D\uDCBC",color:"blue",description:"识别虚假招聘、培训费诈骗等求职陷阱",difficulty:{beginner:{questions:15,completed:15,accuracy:90},intermediate:{questions:12,completed:8,accuracy:85},advanced:{questions:8,completed:0,accuracy:0}},topics:["虚假招聘识别","培训费陷阱","保证金诈骗","网络兼职风险"],recentProgress:85,estimatedTime:"25分钟",lastStudied:"2024-01-15"},{id:"rental-scam",title:"租房安全",icon:"\uD83C\uDFE0",color:"green",description:"防范假房东、押金诈骗等租房风险",difficulty:{beginner:{questions:12,completed:12,accuracy:88},intermediate:{questions:10,completed:5,accuracy:80},advanced:{questions:6,completed:0,accuracy:0}},topics:["假房东识别","押金安全","租房合同","中介陷阱"],recentProgress:70,estimatedTime:"20分钟",lastStudied:"2024-01-14"},{id:"finance-trap",title:"金融理财",icon:"\uD83D\uDCB0",color:"yellow",description:"识别网贷陷阱、投资诈骗等金融风险",difficulty:{beginner:{questions:18,completed:10,accuracy:75},intermediate:{questions:15,completed:0,accuracy:0},advanced:{questions:12,completed:0,accuracy:0}},topics:["网贷陷阱","投资诈骗","理财产品","利率计算"],recentProgress:45,estimatedTime:"35分钟",lastStudied:"2024-01-13"},{id:"education-scam",title:"教育培训",icon:"\uD83C\uDF93",color:"purple",description:"防范培训诈骗、学历造假等教育陷阱",difficulty:{beginner:{questions:10,completed:0,accuracy:0},intermediate:{questions:8,completed:0,accuracy:0},advanced:{questions:5,completed:0,accuracy:0}},topics:["培训机构","学历认证","技能证书","就业承诺"],recentProgress:0,estimatedTime:"30分钟",lastStudied:null}].map(s=>{let t=s.difficulty[e],r=t.questions>0?t.completed/t.questions*100:0;return a.jsx("div",{className:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[a.jsx("div",{className:"text-3xl",children:s.icon}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[a.jsx("h4",{className:"text-lg font-semibold text-gray-900",children:s.title}),a.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(e)}`,children:getDifficultyLabel(e)})]}),a.jsx("p",{className:"text-gray-600 mb-3",children:s.description}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-3",children:[(0,a.jsxs)("div",{className:"text-center p-2 bg-blue-50 rounded",children:[a.jsx("div",{className:"font-bold text-blue-600",children:t.questions}),a.jsx("div",{className:"text-xs text-gray-600",children:"题目数"})]}),(0,a.jsxs)("div",{className:"text-center p-2 bg-green-50 rounded",children:[a.jsx("div",{className:"font-bold text-green-600",children:t.completed}),a.jsx("div",{className:"text-xs text-gray-600",children:"已完成"})]}),(0,a.jsxs)("div",{className:"text-center p-2 bg-yellow-50 rounded",children:[(0,a.jsxs)("div",{className:"font-bold text-yellow-600",children:[t.accuracy,"%"]}),a.jsx("div",{className:"text-xs text-gray-600",children:"正确率"})]}),(0,a.jsxs)("div",{className:"text-center p-2 bg-purple-50 rounded",children:[a.jsx("div",{className:"font-bold text-purple-600",children:s.estimatedTime}),a.jsx("div",{className:"text-xs text-gray-600",children:"预计时长"})]})]}),a.jsx("div",{className:"flex flex-wrap gap-2 mb-3",children:s.topics.map((e,s)=>a.jsx("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded",children:e},s))}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1 mr-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm mb-1",children:[a.jsx("span",{children:"完成进度"}),(0,a.jsxs)("span",{children:[r.toFixed(0),"%"]})]}),a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:a.jsx("div",{className:"bg-primary-600 h-2 rounded-full transition-all",style:{width:`${r}%`}})})]}),(0,a.jsxs)(x(),{href:`/category-quiz?category=${s.id}&difficulty=${e}`,className:"btn-primary flex items-center gap-2",children:[a.jsx(c.Z,{className:"w-4 h-4"}),0===t.completed?"开始练习":"继续练习"]})]})]})]})},s.id)})})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"card",children:[a.jsx("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"学习计划"}),a.jsx("div",{className:"space-y-3",children:[{week:1,title:"基础防诈骗认知",categories:["求职防骗","租房安全"],status:"completed",progress:100},{week:2,title:"金融安全意识",categories:["金融理财","网络通信"],status:"current",progress:60},{week:3,title:"高级防范技巧",categories:["教育培训","冒充权威"],status:"upcoming",progress:0},{week:4,title:"综合实战演练",categories:["兼职副业","综合测试"],status:"upcoming",progress:0}].map(e=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[getStatusIcon(e.status),(0,a.jsxs)("span",{className:"font-medium text-gray-900",children:["第",e.week,"周"]})]}),a.jsx("div",{className:"text-sm text-gray-900 mb-2",children:e.title}),a.jsx("div",{className:"text-xs text-gray-600 mb-2",children:e.categories.join(" • ")}),a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-1.5",children:a.jsx("div",{className:"bg-primary-600 h-1.5 rounded-full transition-all",style:{width:`${e.progress}%`}})})]},e.week))})]}),(0,a.jsxs)("div",{className:"card bg-gradient-to-r from-green-500 to-blue-600 text-white",children:[a.jsx("h3",{className:"text-lg font-bold mb-4",children:"\uD83D\uDCA1 学习建议"}),(0,a.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[a.jsx("span",{children:"\uD83D\uDCDA"}),a.jsx("span",{children:"建议从入门难度开始，循序渐进"})]}),(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[a.jsx("span",{children:"\uD83C\uDFAF"}),a.jsx("span",{children:"专注完成一个分类再进入下一个"})]}),(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[a.jsx("span",{children:"⏰"}),a.jsx("span",{children:"每天练习20-30分钟效果最佳"})]}),(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[a.jsx("span",{children:"\uD83D\uDD04"}),a.jsx("span",{children:"定期复习错题，巩固知识点"})]})]})]}),(0,a.jsxs)("div",{className:"card",children:[a.jsx("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"学习成就"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 p-2 bg-yellow-50 rounded-lg",children:[a.jsx("span",{className:"text-xl",children:"\uD83C\uDFC6"}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-gray-900",children:"求职防骗专家"}),a.jsx("div",{className:"text-xs text-gray-600",children:"完成求职防骗所有难度"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 p-2 bg-green-50 rounded-lg",children:[a.jsx("span",{className:"text-xl",children:"\uD83C\uDFAF"}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-gray-900",children:"精准射手"}),a.jsx("div",{className:"text-xs text-gray-600",children:"单次练习正确率90%+"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 p-2 bg-blue-50 rounded-lg",children:[a.jsx("span",{className:"text-xl",children:"\uD83D\uDCDA"}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-gray-900",children:"勤奋学者"}),a.jsx("div",{className:"text-xs text-gray-600",children:"连续学习7天"})]})]})]})]})]})]})]})]})}},2454:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>c,__esModule:()=>i,default:()=>d});var a=t(5153);let r=(0,a.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/category-practice/page.tsx`),{__esModule:i,$$typeof:c}=r,l=r.default,d=l}};var s=require("../../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[9678,5607,3163,2035],()=>__webpack_exec__(5953));module.exports=t})();