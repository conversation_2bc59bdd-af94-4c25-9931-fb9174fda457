<!DOCTYPE html><html lang="zh-CN"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/97115f26cb754c8c.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-69bfb055b70a1563.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-a0c76284e27c8afa.js" async="" crossorigin=""></script><script src="/_next/static/chunks/472-af003cc8e47f5b1f.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-52f18067e7ca02da.js" async="" crossorigin=""></script><script src="/_next/static/chunks/326-cf5b0283528485c7.js" async=""></script><script src="/_next/static/chunks/app/not-found-5c4df079dd57eb5a.js" async=""></script><script src="/_next/static/chunks/app/features/real-cases/page-d5bd4b06cf10854e.js" async=""></script><title>大学生防诈挑战游戏</title><meta name="description" content="通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱"/><meta name="author" content="防诈挑战游戏团队"/><meta name="keywords" content="防诈骗,大学生,安全教育,答题游戏"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body><div class="min-h-screen"><div class="min-h-screen bg-gradient-to-br from-red-50 to-orange-100"><header class="bg-white shadow-sm border-b"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4"><div class="flex items-center gap-3"><a class="p-2 hover:bg-gray-100 rounded-lg transition-colors" href="/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left w-5 h-5 text-gray-600"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg></a><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle w-8 h-8 text-red-600"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path><path d="M12 9v4"></path><path d="M12 17h.01"></path></svg><div><h1 class="text-2xl font-bold text-gray-900">真实案例</h1><p class="text-sm text-gray-600">真实经历，警示教育</p></div></div></div></header><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="card mb-8 bg-gradient-to-r from-red-500 to-orange-600 text-white"><h2 class="text-2xl font-bold mb-6">案例统计</h2><div class="grid grid-cols-2 md:grid-cols-4 gap-4"><div class="text-center"><div class="text-2xl font-bold">1247</div><div class="text-sm text-red-200">总案例数</div></div><div class="text-center"><div class="text-2xl font-bold">892</div><div class="text-sm text-red-200">已验证</div></div><div class="text-center"><div class="text-2xl font-bold">156,420</div><div class="text-sm text-red-200">总浏览量</div></div><div class="text-center"><div class="text-2xl font-bold">8934</div><div class="text-sm text-red-200">有用投票</div></div></div></div><div class="card mb-8"><div class="flex flex-wrap gap-4 items-center"><div class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-filter w-4 h-4 text-gray-600"><polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon></svg><span class="text-sm font-medium text-gray-700">筛选：</span></div><select class="px-3 py-2 border border-gray-300 rounded-lg text-sm"><option value="all" selected="">全部分类</option><option value="job">求职骗局</option><option value="rental">租房陷阱</option><option value="finance">金融诈骗</option><option value="education">教育骗局</option></select><select class="px-3 py-2 border border-gray-300 rounded-lg text-sm"><option value="all" selected="">全部风险等级</option><option value="high">高危</option><option value="medium">中危</option><option value="low">低危</option></select></div></div><div class="space-y-6"><div class="card hover:shadow-lg transition-shadow"><div class="flex items-start gap-4 mb-4"><div class="flex-shrink-0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle w-6 h-6 text-red-500"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path><path d="M12 9v4"></path><path d="M12 17h.01"></path></svg></div><div class="flex-1"><div class="flex items-center gap-3 mb-2"><h3 class="text-lg font-bold text-gray-900">求职遇到&quot;培训费&quot;陷阱，差点被骗5000元</h3><span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">已验证</span><span class="px-2 py-1 rounded-full text-xs font-medium text-red-600 bg-red-100">高危</span></div><div class="flex items-center gap-4 text-sm text-gray-600 mb-3"><span>作者: <!-- -->小心求职者</span><span>分类: <!-- -->求职骗局</span><span>涉及金额: ¥<!-- -->5,000</span><span>发布: <!-- -->2024-01-15</span></div><p class="text-gray-700 mb-4">应聘时被要求先交培训费，幸好及时识破骗局</p><div class="flex flex-wrap gap-2 mb-4"><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">#<!-- -->培训费</span><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">#<!-- -->虚假招聘</span><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">#<!-- -->求职陷阱</span></div><div class="bg-gray-50 rounded-lg p-4 mb-4"><h4 class="font-semibold text-gray-900 mb-2">案例详情</h4><div class="text-gray-700 text-sm whitespace-pre-line">我在某招聘网站上看到一个&quot;高薪客服&quot;的职位，月薪8000-12000，工作轻松。投递简历后很快收到面试通知。

面试官说我很符合要求，但需要先参加公司的&quot;专业培训&quot;，培训费5000元，培训结束后会退还并正式入职。

当时我觉得有些奇怪，但面试官说这是为了筛选真正有意向的员工。我差点就交钱了，幸好朋友提醒我这是典型的培训费诈骗。

后来我查了这家公司，发现根本不存在，网站也是假的。现在想想真是后怕，差点就被骗了5000元。</div></div><div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"><div class="bg-red-50 rounded-lg p-3"><h5 class="font-semibold text-red-900 mb-2">⚠️ 经验教训</h5><ul class="text-red-800 text-sm space-y-1"><li>• <!-- -->正规公司不会要求员工交培训费</li><li>• <!-- -->入职前要核实公司真实性</li><li>• <!-- -->遇到要求先交钱的工作要警惕</li><li>• <!-- -->可以通过企查查等平台验证公司信息</li></ul></div><div class="bg-green-50 rounded-lg p-3"><h5 class="font-semibold text-green-900 mb-2">🛡️ 防范措施</h5><ul class="text-green-800 text-sm space-y-1"><li>• <!-- -->通过官方渠道投递简历</li><li>• <!-- -->面试时注意观察公司环境</li><li>• <!-- -->不要轻易交纳任何费用</li><li>• <!-- -->与朋友家人商量再做决定</li></ul></div></div><div class="flex items-center justify-between pt-4 border-t border-gray-200"><div class="flex items-center gap-4 text-sm text-gray-600"><div class="flex items-center gap-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-eye w-4 h-4"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path><circle cx="12" cy="12" r="3"></circle></svg><span>1250</span></div><div class="flex items-center gap-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-heart w-4 h-4"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"></path></svg><span>89</span></div><div class="flex items-center gap-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-circle w-4 h-4"><path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"></path></svg><span>23</span></div></div><div class="flex gap-2"><button class="flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-red-600 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-heart w-4 h-4"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"></path></svg>有用</button><button class="flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-blue-600 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-share2 w-4 h-4"><circle cx="18" cy="5" r="3"></circle><circle cx="6" cy="12" r="3"></circle><circle cx="18" cy="19" r="3"></circle><line x1="8.59" x2="15.42" y1="13.51" y2="17.49"></line><line x1="15.41" x2="8.59" y1="6.51" y2="10.49"></line></svg>分享</button></div></div></div></div></div><div class="card hover:shadow-lg transition-shadow"><div class="flex items-start gap-4 mb-4"><div class="flex-shrink-0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle w-6 h-6 text-red-500"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path><path d="M12 9v4"></path><path d="M12 17h.01"></path></svg></div><div class="flex-1"><div class="flex items-center gap-3 mb-2"><h3 class="text-lg font-bold text-gray-900">租房遇到假房东，押金3000元打水漂</h3><span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">已验证</span><span class="px-2 py-1 rounded-full text-xs font-medium text-yellow-600 bg-yellow-100">中危</span></div><div class="flex items-center gap-4 text-sm text-gray-600 mb-3"><span>作者: <!-- -->租房小白</span><span>分类: <!-- -->租房陷阱</span><span>涉及金额: ¥<!-- -->3,000</span><span>发布: <!-- -->2024-01-14</span></div><p class="text-gray-700 mb-4">通过网络找房遇到假房东，交了押金后人就消失了</p><div class="flex flex-wrap gap-2 mb-4"><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">#<!-- -->假房东</span><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">#<!-- -->押金诈骗</span><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">#<!-- -->租房陷阱</span></div><div class="bg-gray-50 rounded-lg p-4 mb-4"><h4 class="font-semibold text-gray-900 mb-2">案例详情</h4><div class="text-gray-700 text-sm whitespace-pre-line">在某租房APP上看到一套很便宜的房子，位置好价格低，我立刻联系了&quot;房东&quot;。

&quot;房东&quot;说人在外地，不能现场看房，但可以先交押金锁定房源。他发了很多房子的照片，看起来很真实。

我担心房子被别人租走，就通过微信转账交了3000元押金。约定第二天去看房签合同。

结果第二天到了地址，发现根本联系不上&quot;房东&quot;，微信也被拉黑了。后来才知道那些照片都是从网上盗用的，我遇到了假房东。</div></div><div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"><div class="bg-red-50 rounded-lg p-3"><h5 class="font-semibold text-red-900 mb-2">⚠️ 经验教训</h5><ul class="text-red-800 text-sm space-y-1"><li>• <!-- -->租房一定要实地看房</li><li>• <!-- -->不要轻信网上的房源照片</li><li>• <!-- -->交钱前要核实房东身份</li><li>• <!-- -->通过正规中介或平台租房更安全</li></ul></div><div class="bg-green-50 rounded-lg p-3"><h5 class="font-semibold text-green-900 mb-2">🛡️ 防范措施</h5><ul class="text-green-800 text-sm space-y-1"><li>• <!-- -->要求查看房产证和身份证</li><li>• <!-- -->实地看房后再谈价格</li><li>• <!-- -->通过银行转账留下记录</li><li>• <!-- -->签订正式租房合同</li></ul></div></div><div class="flex items-center justify-between pt-4 border-t border-gray-200"><div class="flex items-center gap-4 text-sm text-gray-600"><div class="flex items-center gap-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-eye w-4 h-4"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path><circle cx="12" cy="12" r="3"></circle></svg><span>980</span></div><div class="flex items-center gap-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-heart w-4 h-4"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"></path></svg><span>67</span></div><div class="flex items-center gap-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-circle w-4 h-4"><path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"></path></svg><span>18</span></div></div><div class="flex gap-2"><button class="flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-red-600 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-heart w-4 h-4"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"></path></svg>有用</button><button class="flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-blue-600 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-share2 w-4 h-4"><circle cx="18" cy="5" r="3"></circle><circle cx="6" cy="12" r="3"></circle><circle cx="18" cy="19" r="3"></circle><line x1="8.59" x2="15.42" y1="13.51" y2="17.49"></line><line x1="15.41" x2="8.59" y1="6.51" y2="10.49"></line></svg>分享</button></div></div></div></div></div><div class="card hover:shadow-lg transition-shadow"><div class="flex items-start gap-4 mb-4"><div class="flex-shrink-0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle w-6 h-6 text-red-500"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path><path d="M12 9v4"></path><path d="M12 17h.01"></path></svg></div><div class="flex-1"><div class="flex items-center gap-3 mb-2"><h3 class="text-lg font-bold text-gray-900">网贷&quot;砍头息&quot;陷阱，实际年利率超过100%</h3><span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">已验证</span><span class="px-2 py-1 rounded-full text-xs font-medium text-red-600 bg-red-100">高危</span></div><div class="flex items-center gap-4 text-sm text-gray-600 mb-3"><span>作者: <!-- -->理财新手</span><span>分类: <!-- -->金融诈骗</span><span>涉及金额: ¥<!-- -->10,000</span><span>发布: <!-- -->2024-01-13</span></div><p class="text-gray-700 mb-4">急需用钱申请网贷，遇到砍头息陷阱，实际利率惊人</p><div class="flex flex-wrap gap-2 mb-4"><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">#<!-- -->砍头息</span><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">#<!-- -->高利贷</span><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">#<!-- -->网贷陷阱</span></div><div class="bg-gray-50 rounded-lg p-4 mb-4"><h4 class="font-semibold text-gray-900 mb-2">案例详情</h4><div class="text-gray-700 text-sm whitespace-pre-line">因为急需1万元周转，我在网上找到一个贷款平台，声称&quot;低息快贷，当天放款&quot;。

申请时平台说月利率只有1.5%，看起来很合理。但放款时发现实际到账只有8000元，平台说扣除了&quot;服务费&quot;、&quot;手续费&quot;等2000元。

还款时我才发现，虽然借了1万元，但实际只拿到8000元，却要按1万元还款。算下来实际年利率超过了100%，这就是典型的&quot;砍头息&quot;。

现在我深陷其中，每月还款压力巨大，真后悔当初没有仔细了解。</div></div><div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"><div class="bg-red-50 rounded-lg p-3"><h5 class="font-semibold text-red-900 mb-2">⚠️ 经验教训</h5><ul class="text-red-800 text-sm space-y-1"><li>• <!-- -->砍头息是违法的高利贷行为</li><li>• <!-- -->借款前要仔细计算实际利率</li><li>• <!-- -->正规金融机构不会预扣费用</li><li>• <!-- -->遇到资金困难要通过正当渠道解决</li></ul></div><div class="bg-green-50 rounded-lg p-3"><h5 class="font-semibold text-green-900 mb-2">🛡️ 防范措施</h5><ul class="text-green-800 text-sm space-y-1"><li>• <!-- -->选择银行等正规金融机构</li><li>• <!-- -->仔细阅读借款合同条款</li><li>• <!-- -->计算真实的借款成本</li><li>• <!-- -->不要被&quot;低息&quot;广告迷惑</li></ul></div></div><div class="flex items-center justify-between pt-4 border-t border-gray-200"><div class="flex items-center gap-4 text-sm text-gray-600"><div class="flex items-center gap-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-eye w-4 h-4"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path><circle cx="12" cy="12" r="3"></circle></svg><span>1580</span></div><div class="flex items-center gap-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-heart w-4 h-4"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"></path></svg><span>124</span></div><div class="flex items-center gap-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-circle w-4 h-4"><path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"></path></svg><span>35</span></div></div><div class="flex gap-2"><button class="flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-red-600 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-heart w-4 h-4"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"></path></svg>有用</button><button class="flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-blue-600 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-share2 w-4 h-4"><circle cx="18" cy="5" r="3"></circle><circle cx="6" cy="12" r="3"></circle><circle cx="18" cy="19" r="3"></circle><line x1="8.59" x2="15.42" y1="13.51" y2="17.49"></line><line x1="15.41" x2="8.59" y1="6.51" y2="10.49"></line></svg>分享</button></div></div></div></div></div></div><div class="card mt-8 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-center"><h3 class="text-xl font-bold mb-4">分享你的经历，帮助更多人</h3><p class="text-blue-100 mb-6">如果你也有防诈骗的真实经历，欢迎分享给大家，让更多人从中受益。</p><a class="btn-secondary inline-flex items-center gap-2" href="/demo-share/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-share2 w-5 h-5"><circle cx="18" cy="5" r="3"></circle><circle cx="6" cy="12" r="3"></circle><circle cx="18" cy="19" r="3"></circle><line x1="8.59" x2="15.42" y1="13.51" y2="17.49"></line><line x1="15.41" x2="8.59" y1="6.51" y2="10.49"></line></svg>分享我的经历</a></div></div></div></div><script src="/_next/static/chunks/webpack-69bfb055b70a1563.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/css/97115f26cb754c8c.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L2\"\n"])</script><script>self.__next_f.push([1,"3:I[3728,[],\"\"]\n5:I[9928,[],\"\"]\n6:I[6954,[],\"\"]\n7:I[7264,[],\"\"]\n8:I[8326,[\"326\",\"static/chunks/326-cf5b0283528485c7.js\",\"160\",\"static/chunks/app/not-found-5c4df079dd57eb5a.js\"],\"\"]\na:I[8297,[],\"\"]\nb:I[3689,[\"326\",\"static/chunks/326-cf5b0283528485c7.js\",\"729\",\"static/chunks/app/features/real-cases/page-d5bd4b06cf10854e.js\"],\"\"]\n"])</script><script>self.__next_f.push([1,"2:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/97115f26cb754c8c.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L3\",null,{\"buildId\":\"-csu5AGVUyiuSzdvekIsf\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/features/real-cases/\",\"initialTree\":[\"\",{\"children\":[\"features\",{\"children\":[\"real-cases\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],\"initialHead\":[false,\"$L4\"],\"globalErrorComponent\":\"$5\",\"children\":[null,[\"$\",\"html\",null,{\"lang\":\"zh-CN\",\"children\":[\"$\",\"body\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"min-h-screen\",\"children\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4 sm:px-6 lg:px-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-md w-full text-center\",\"children\":[\"$\",\"div\",null,{\"className\":\"card\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mb-6\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-alert-triangle w-16 h-16 text-yellow-500 mx-auto mb-4\",\"children\":[[\"$\",\"path\",\"c3ski4\",{\"d\":\"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z\"}],[\"$\",\"path\",\"juzpu7\",{\"d\":\"M12 9v4\"}],[\"$\",\"path\",\"p32p05\",{\"d\":\"M12 17h.01\"}],\"$undefined\"]}],[\"$\",\"h1\",null,{\"className\":\"text-6xl font-bold text-gray-900 mb-2\",\"children\":\"404\"}],[\"$\",\"h2\",null,{\"className\":\"text-2xl font-semibold text-gray-700 mb-4\",\"children\":\"页面未找到\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600\",\"children\":\"抱歉，你访问的页面不存在。可能是链接错误或页面已被移除。\"}]]}],[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"btn-primary w-full flex items-center justify-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-home w-5 h-5\",\"children\":[[\"$\",\"path\",\"y5dka4\",{\"d\":\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"}],[\"$\",\"polyline\",\"e2us08\",{\"points\":\"9 22 9 12 15 12 15 22\"}],\"$undefined\"]}],\"返回首页\"]}],[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"btn-secondary w-full flex items-center justify-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-left w-5 h-5\",\"children\":[[\"$\",\"path\",\"1l729n\",{\"d\":\"m12 19-7-7 7-7\"}],[\"$\",\"path\",\"x3x0zl\",{\"d\":\"M19 12H5\"}],\"$undefined\"]}],\"返回上一页\"]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\"children\":[\"$\",\"p\",null,{\"className\":\"text-blue-800 text-sm\",\"children\":[\"💡 \",[\"$\",\"strong\",null,{\"children\":\"提示：\"}],\"如果你是通过链接访问的，请检查链接是否正确。 如果问题持续存在，请联系我们。\"]}]}]]}]}]}],\"notFoundStyles\":[],\"childProp\":{\"current\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"features\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"childProp\":{\"current\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"features\",\"children\",\"real-cases\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"childProp\":{\"current\":[\"$L9\",[\"$\",\"$La\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$b\",\"isStaticGeneration\":true}],null],\"segment\":\"__PAGE__\"},\"styles\":null}],\"segment\":\"real-cases\"},\"styles\":null}],\"segment\":\"features\"},\"styles\":null}]}]}]}],null]}]]\n"])</script><script>self.__next_f.push([1,"4:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"大学生防诈挑战游戏\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱\"}],[\"$\",\"meta\",\"4\",{\"name\":\"author\",\"content\":\"防诈挑战游戏团队\"}],[\"$\",\"meta\",\"5\",{\"name\":\"keywords\",\"content\":\"防诈骗,大学生,安全教育,答题游戏\"}]]\n9:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>