/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/features/real-cases/page";
exports.ids = ["app/features/real-cases/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffeatures%2Freal-cases%2Fpage&page=%2Ffeatures%2Freal-cases%2Fpage&appPaths=%2Ffeatures%2Freal-cases%2Fpage&pagePath=private-next-app-dir%2Ffeatures%2Freal-cases%2Fpage.tsx&appDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffeatures%2Freal-cases%2Fpage&page=%2Ffeatures%2Freal-cases%2Fpage&appPaths=%2Ffeatures%2Freal-cases%2Fpage&pagePath=private-next-app-dir%2Ffeatures%2Freal-cases%2Fpage.tsx&appDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'features',\n        {\n        children: [\n        'real-cases',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/features/real-cases/page.tsx */ \"(rsc)/./app/features/real-cases/page.tsx\")), \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\")), \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/features/real-cases/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/features/real-cases/page\",\n        pathname: \"/features/real-cases\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffeatures%2Freal-cases%2Fpage&page=%2Ffeatures%2Freal-cases%2Fpage&appPaths=%2Ffeatures%2Freal-cases%2Fpage&pagePath=private-next-app-dir%2Ffeatures%2Freal-cases%2Fpage.tsx&appDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp%2Ffeatures%2Freal-cases%2Fpage.tsx&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp%2Ffeatures%2Freal-cases%2Fpage.tsx&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/features/real-cases/page.tsx */ \"(ssr)/./app/features/real-cases/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRnolMkZEZXNrdG9wJTJGdnNjb2RlJTJGJUU5JTk4JUIyJUU4JUFGJTg4JUU2JThDJTkxJUU2JTg4JTk4JUU2JUI4JUI4JUU2JTg4JThGJTJGYXBwJTJGZmVhdHVyZXMlMkZyZWFsLWNhc2VzJTJGcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJhdWQtcHJldmVudGlvbi1nYW1lLz9hZGM2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvei9EZXNrdG9wL3ZzY29kZS/pmLLor4jmjJHmiJjmuLjmiI8vYXBwL2ZlYXR1cmVzL3JlYWwtY2FzZXMvcGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp%2Ffeatures%2Freal-cases%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp%2Fglobals.css&server=true!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp%2Fglobals.css&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRnolMkZEZXNrdG9wJTJGdnNjb2RlJTJGJUU5JTk4JUIyJUU4JUFGJTg4JUU2JThDJTkxJUU2JTg4JTk4JUU2JUI4JUI4JUU2JTg4JThGJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZsaW5rLmpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2ZyYXVkLXByZXZlbnRpb24tZ2FtZS8/OTVjNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL3ovRGVza3RvcC92c2NvZGUv6Ziy6K+I5oyR5oiY5ri45oiPL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2xpbmsuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/features/real-cases/page.tsx":
/*!******************************************!*\
  !*** ./app/features/real-cases/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RealCasesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Eye_Filter_Heart_MessageCircle_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Eye,Filter,Heart,MessageCircle,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Eye_Filter_Heart_MessageCircle_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Eye,Filter,Heart,MessageCircle,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Eye_Filter_Heart_MessageCircle_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Eye,Filter,Heart,MessageCircle,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Eye_Filter_Heart_MessageCircle_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Eye,Filter,Heart,MessageCircle,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Eye_Filter_Heart_MessageCircle_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Eye,Filter,Heart,MessageCircle,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Eye_Filter_Heart_MessageCircle_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Eye,Filter,Heart,MessageCircle,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Eye_Filter_Heart_MessageCircle_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Eye,Filter,Heart,MessageCircle,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction RealCasesPage() {\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedRisk, setSelectedRisk] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const caseStats = {\n        totalCases: 1247,\n        verifiedCases: 892,\n        totalViews: 156420,\n        helpfulVotes: 8934\n    };\n    const realCases = [\n        {\n            id: \"case001\",\n            title: '求职遇到\"培训费\"陷阱，差点被骗5000元',\n            author: \"小心求职者\",\n            category: \"job\",\n            riskLevel: \"high\",\n            amount: 5000,\n            publishedAt: \"2024-01-15\",\n            views: 1250,\n            likes: 89,\n            comments: 23,\n            verified: true,\n            tags: [\n                \"培训费\",\n                \"虚假招聘\",\n                \"求职陷阱\"\n            ],\n            summary: \"应聘时被要求先交培训费，幸好及时识破骗局\",\n            content: `我在某招聘网站上看到一个\"高薪客服\"的职位，月薪8000-12000，工作轻松。投递简历后很快收到面试通知。\n\n面试官说我很符合要求，但需要先参加公司的\"专业培训\"，培训费5000元，培训结束后会退还并正式入职。\n\n当时我觉得有些奇怪，但面试官说这是为了筛选真正有意向的员工。我差点就交钱了，幸好朋友提醒我这是典型的培训费诈骗。\n\n后来我查了这家公司，发现根本不存在，网站也是假的。现在想想真是后怕，差点就被骗了5000元。`,\n            lessons: [\n                \"正规公司不会要求员工交培训费\",\n                \"入职前要核实公司真实性\",\n                \"遇到要求先交钱的工作要警惕\",\n                \"可以通过企查查等平台验证公司信息\"\n            ],\n            prevention: [\n                \"通过官方渠道投递简历\",\n                \"面试时注意观察公司环境\",\n                \"不要轻易交纳任何费用\",\n                \"与朋友家人商量再做决定\"\n            ]\n        },\n        {\n            id: \"case002\",\n            title: \"租房遇到假房东，押金3000元打水漂\",\n            author: \"租房小白\",\n            category: \"rental\",\n            riskLevel: \"medium\",\n            amount: 3000,\n            publishedAt: \"2024-01-14\",\n            views: 980,\n            likes: 67,\n            comments: 18,\n            verified: true,\n            tags: [\n                \"假房东\",\n                \"押金诈骗\",\n                \"租房陷阱\"\n            ],\n            summary: \"通过网络找房遇到假房东，交了押金后人就消失了\",\n            content: `在某租房APP上看到一套很便宜的房子，位置好价格低，我立刻联系了\"房东\"。\n\n\"房东\"说人在外地，不能现场看房，但可以先交押金锁定房源。他发了很多房子的照片，看起来很真实。\n\n我担心房子被别人租走，就通过微信转账交了3000元押金。约定第二天去看房签合同。\n\n结果第二天到了地址，发现根本联系不上\"房东\"，微信也被拉黑了。后来才知道那些照片都是从网上盗用的，我遇到了假房东。`,\n            lessons: [\n                \"租房一定要实地看房\",\n                \"不要轻信网上的房源照片\",\n                \"交钱前要核实房东身份\",\n                \"通过正规中介或平台租房更安全\"\n            ],\n            prevention: [\n                \"要求查看房产证和身份证\",\n                \"实地看房后再谈价格\",\n                \"通过银行转账留下记录\",\n                \"签订正式租房合同\"\n            ]\n        },\n        {\n            id: \"case003\",\n            title: '网贷\"砍头息\"陷阱，实际年利率超过100%',\n            author: \"理财新手\",\n            category: \"finance\",\n            riskLevel: \"high\",\n            amount: 10000,\n            publishedAt: \"2024-01-13\",\n            views: 1580,\n            likes: 124,\n            comments: 35,\n            verified: true,\n            tags: [\n                \"砍头息\",\n                \"高利贷\",\n                \"网贷陷阱\"\n            ],\n            summary: \"急需用钱申请网贷，遇到砍头息陷阱，实际利率惊人\",\n            content: `因为急需1万元周转，我在网上找到一个贷款平台，声称\"低息快贷，当天放款\"。\n\n申请时平台说月利率只有1.5%，看起来很合理。但放款时发现实际到账只有8000元，平台说扣除了\"服务费\"、\"手续费\"等2000元。\n\n还款时我才发现，虽然借了1万元，但实际只拿到8000元，却要按1万元还款。算下来实际年利率超过了100%，这就是典型的\"砍头息\"。\n\n现在我深陷其中，每月还款压力巨大，真后悔当初没有仔细了解。`,\n            lessons: [\n                \"砍头息是违法的高利贷行为\",\n                \"借款前要仔细计算实际利率\",\n                \"正规金融机构不会预扣费用\",\n                \"遇到资金困难要通过正当渠道解决\"\n            ],\n            prevention: [\n                \"选择银行等正规金融机构\",\n                \"仔细阅读借款合同条款\",\n                \"计算真实的借款成本\",\n                '不要被\"低息\"广告迷惑'\n            ]\n        }\n    ];\n    const getRiskColor = (risk)=>{\n        switch(risk){\n            case \"high\":\n                return \"text-red-600 bg-red-100\";\n            case \"medium\":\n                return \"text-yellow-600 bg-yellow-100\";\n            case \"low\":\n                return \"text-green-600 bg-green-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    const getRiskLabel = (risk)=>{\n        switch(risk){\n            case \"high\":\n                return \"高危\";\n            case \"medium\":\n                return \"中危\";\n            case \"low\":\n                return \"低危\";\n            default:\n                return \"未知\";\n        }\n    };\n    const getCategoryLabel = (category)=>{\n        switch(category){\n            case \"job\":\n                return \"求职骗局\";\n            case \"rental\":\n                return \"租房陷阱\";\n            case \"finance\":\n                return \"金融诈骗\";\n            case \"education\":\n                return \"教育骗局\";\n            default:\n                return \"其他\";\n        }\n    };\n    const filteredCases = realCases.filter((caseItem)=>{\n        const categoryMatch = selectedCategory === \"all\" || caseItem.category === selectedCategory;\n        const riskMatch = selectedRisk === \"all\" || caseItem.riskLevel === selectedRisk;\n        return categoryMatch && riskMatch;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-red-50 to-orange-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Eye_Filter_Heart_MessageCircle_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-5 h-5 text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Eye_Filter_Heart_MessageCircle_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-8 h-8 text-red-600\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"真实案例\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"真实经历，警示教育\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card mb-8 bg-gradient-to-r from-red-500 to-orange-600 text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold mb-6\",\n                                children: \"案例统计\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: caseStats.totalCases\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-red-200\",\n                                                children: \"总案例数\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: caseStats.verifiedCases\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-red-200\",\n                                                children: \"已验证\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: caseStats.totalViews.toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-red-200\",\n                                                children: \"总浏览量\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: caseStats.helpfulVotes\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-red-200\",\n                                                children: \"有用投票\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-4 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Eye_Filter_Heart_MessageCircle_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"筛选：\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: selectedCategory,\n                                    onChange: (e)=>setSelectedCategory(e.target.value),\n                                    className: \"px-3 py-2 border border-gray-300 rounded-lg text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"all\",\n                                            children: \"全部分类\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"job\",\n                                            children: \"求职骗局\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"rental\",\n                                            children: \"租房陷阱\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"finance\",\n                                            children: \"金融诈骗\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"education\",\n                                            children: \"教育骗局\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: selectedRisk,\n                                    onChange: (e)=>setSelectedRisk(e.target.value),\n                                    className: \"px-3 py-2 border border-gray-300 rounded-lg text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"all\",\n                                            children: \"全部风险等级\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"high\",\n                                            children: \"高危\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"medium\",\n                                            children: \"中危\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"low\",\n                                            children: \"低危\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: filteredCases.map((caseItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card hover:shadow-lg transition-shadow\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Eye_Filter_Heart_MessageCircle_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-6 h-6 text-red-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-bold text-gray-900\",\n                                                            children: caseItem.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        caseItem.verified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full\",\n                                                            children: \"已验证\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `px-2 py-1 rounded-full text-xs font-medium ${getRiskColor(caseItem.riskLevel)}`,\n                                                            children: getRiskLabel(caseItem.riskLevel)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4 text-sm text-gray-600 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"作者: \",\n                                                                caseItem.author\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"分类: \",\n                                                                getCategoryLabel(caseItem.category)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"涉及金额: \\xa5\",\n                                                                caseItem.amount.toLocaleString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"发布: \",\n                                                                caseItem.publishedAt\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700 mb-4\",\n                                                    children: caseItem.summary\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2 mb-4\",\n                                                    children: caseItem.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded\",\n                                                            children: [\n                                                                \"#\",\n                                                                tag\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-50 rounded-lg p-4 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-900 mb-2\",\n                                                            children: \"案例详情\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-700 text-sm whitespace-pre-line\",\n                                                            children: caseItem.content\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-red-50 rounded-lg p-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                    className: \"font-semibold text-red-900 mb-2\",\n                                                                    children: \"⚠️ 经验教训\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                                    lineNumber: 275,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"text-red-800 text-sm space-y-1\",\n                                                                    children: caseItem.lessons.map((lesson, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: [\n                                                                                \"• \",\n                                                                                lesson\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                                            lineNumber: 278,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-green-50 rounded-lg p-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                    className: \"font-semibold text-green-900 mb-2\",\n                                                                    children: \"\\uD83D\\uDEE1️ 防范措施\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"text-green-800 text-sm space-y-1\",\n                                                                    children: caseItem.prevention.map((measure, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: [\n                                                                                \"• \",\n                                                                                measure\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                                            lineNumber: 286,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                                    lineNumber: 284,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between pt-4 border-t border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 text-sm text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Eye_Filter_Heart_MessageCircle_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                                            lineNumber: 295,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: caseItem.views\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                                            lineNumber: 296,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                                    lineNumber: 294,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Eye_Filter_Heart_MessageCircle_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                                            lineNumber: 299,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: caseItem.likes\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                                            lineNumber: 300,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Eye_Filter_Heart_MessageCircle_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                                            lineNumber: 303,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: caseItem.comments\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                                            lineNumber: 304,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-red-600 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Eye_Filter_Heart_MessageCircle_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                                            lineNumber: 309,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"有用\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                                    lineNumber: 308,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-blue-600 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Eye_Filter_Heart_MessageCircle_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                                            lineNumber: 313,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"分享\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this)\n                            }, caseItem.id, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card mt-8 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold mb-4\",\n                                children: \"分享你的经历，帮助更多人\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-100 mb-6\",\n                                children: \"如果你也有防诈骗的真实经历，欢迎分享给大家，让更多人从中受益。\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/demo-share\",\n                                className: \"btn-secondary inline-flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Eye_Filter_Heart_MessageCircle_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"分享我的经历\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/features/real-cases/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"11b37adaa9c3\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mcmF1ZC1wcmV2ZW50aW9uLWdhbWUvLi9hcHAvZ2xvYmFscy5jc3M/ODJlZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjExYjM3YWRhYTljM1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/features/real-cases/page.tsx":
/*!******************************************!*\
  !*** ./app/features/real-cases/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: \"大学生防诈挑战游戏\",\n    description: \"通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱\",\n    keywords: \"防诈骗,大学生,安全教育,答题游戏\",\n    authors: [\n        {\n            name: \"防诈挑战游戏团队\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxVQUFVO0lBQ1ZDLFNBQVM7UUFBQztZQUFFQyxNQUFNO1FBQVc7S0FBRTtBQUNqQyxFQUFDO0FBRU0sTUFBTUMsV0FBVztJQUN0QkMsT0FBTztJQUNQQyxjQUFjO0FBQ2hCLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7c0JBQ0MsNEVBQUNDO2dCQUFJQyxXQUFVOzBCQUNaTDs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJhdWQtcHJldmVudGlvbi1nYW1lLy4vYXBwL2xheW91dC50c3g/OTk4OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICflpKflrabnlJ/pmLLor4jmjJHmiJjmuLjmiI8nLFxuICBkZXNjcmlwdGlvbjogJ+mAmui/h+etlOmimOaMkeaImOaPkOWNh+mYsuiviOmql+aEj+ivhu+8jOS/neaKpOiHquW3sei/nOemu+WQhOenjeiviOmql+mZt+mYsScsXG4gIGtleXdvcmRzOiAn6Ziy6K+I6aqXLOWkp+WtpueUnyzlronlhajmlZnogrIs562U6aKY5ri45oiPJyxcbiAgYXV0aG9yczogW3sgbmFtZTogJ+mYsuiviOaMkeaImOa4uOaIj+WboumYnycgfV0sXG59XG5cbmV4cG9ydCBjb25zdCB2aWV3cG9ydCA9IHtcbiAgd2lkdGg6ICdkZXZpY2Utd2lkdGgnLFxuICBpbml0aWFsU2NhbGU6IDEsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHk+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuXCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJrZXl3b3JkcyIsImF1dGhvcnMiLCJuYW1lIiwidmlld3BvcnQiLCJ3aWR0aCIsImluaXRpYWxTY2FsZSIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Home!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Home!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Home!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-16 h-16 text-yellow-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 10,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-6xl font-bold text-gray-900 mb-2\",\n                                children: \"404\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 11,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-700 mb-4\",\n                                children: \"页面未找到\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 12,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"抱歉，你访问的页面不存在。可能是链接错误或页面已被移除。\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 13,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"btn-primary w-full flex items-center justify-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"返回首页\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"btn-secondary w-full flex items-center justify-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"返回上一页\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-800 text-sm\",\n                            children: [\n                                \"\\uD83D\\uDCA1 \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"提示：\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 18\n                                }, this),\n                                \"如果你是通过链接访问的，请检查链接是否正确。 如果问题持续存在，请联系我们。\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                lineNumber: 8,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/not-found.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffeatures%2Freal-cases%2Fpage&page=%2Ffeatures%2Freal-cases%2Fpage&appPaths=%2Ffeatures%2Freal-cases%2Fpage&pagePath=private-next-app-dir%2Ffeatures%2Freal-cases%2Fpage.tsx&appDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();