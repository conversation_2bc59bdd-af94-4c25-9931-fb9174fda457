(()=>{var e={};e.id=4729,e.ids=[4729],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},5806:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>o,pages:()=>x,routeModule:()=>h,tree:()=>d});var a=t(7096),r=t(6132),l=t(7284),i=t.n(l),n=t(2564),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["features",{children:["real-cases",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,5507)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],x=["/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx"],o="/features/real-cases/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/features/real-cases/page",pathname:"/features/real-cases",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},7980:(e,s,t)=>{Promise.resolve().then(t.bind(t,652))},652:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>RealCasesPage});var a=t(784),r=t(9885),l=t(9803),i=t(4409),n=t(2029),c=t(8626),d=t(7371),x=t(169),o=t(6386),m=t(1440),h=t.n(m);function RealCasesPage(){let[e,s]=(0,r.useState)("all"),[t,m]=(0,r.useState)("all"),p={totalCases:1247,verifiedCases:892,totalViews:156420,helpfulVotes:8934},u=[{id:"case001",title:'求职遇到"培训费"陷阱，差点被骗5000元',author:"小心求职者",category:"job",riskLevel:"high",amount:5e3,publishedAt:"2024-01-15",views:1250,likes:89,comments:23,verified:!0,tags:["培训费","虚假招聘","求职陷阱"],summary:"应聘时被要求先交培训费，幸好及时识破骗局",content:`我在某招聘网站上看到一个"高薪客服"的职位，月薪8000-12000，工作轻松。投递简历后很快收到面试通知。

面试官说我很符合要求，但需要先参加公司的"专业培训"，培训费5000元，培训结束后会退还并正式入职。

当时我觉得有些奇怪，但面试官说这是为了筛选真正有意向的员工。我差点就交钱了，幸好朋友提醒我这是典型的培训费诈骗。

后来我查了这家公司，发现根本不存在，网站也是假的。现在想想真是后怕，差点就被骗了5000元。`,lessons:["正规公司不会要求员工交培训费","入职前要核实公司真实性","遇到要求先交钱的工作要警惕","可以通过企查查等平台验证公司信息"],prevention:["通过官方渠道投递简历","面试时注意观察公司环境","不要轻易交纳任何费用","与朋友家人商量再做决定"]},{id:"case002",title:"租房遇到假房东，押金3000元打水漂",author:"租房小白",category:"rental",riskLevel:"medium",amount:3e3,publishedAt:"2024-01-14",views:980,likes:67,comments:18,verified:!0,tags:["假房东","押金诈骗","租房陷阱"],summary:"通过网络找房遇到假房东，交了押金后人就消失了",content:`在某租房APP上看到一套很便宜的房子，位置好价格低，我立刻联系了"房东"。

"房东"说人在外地，不能现场看房，但可以先交押金锁定房源。他发了很多房子的照片，看起来很真实。

我担心房子被别人租走，就通过微信转账交了3000元押金。约定第二天去看房签合同。

结果第二天到了地址，发现根本联系不上"房东"，微信也被拉黑了。后来才知道那些照片都是从网上盗用的，我遇到了假房东。`,lessons:["租房一定要实地看房","不要轻信网上的房源照片","交钱前要核实房东身份","通过正规中介或平台租房更安全"],prevention:["要求查看房产证和身份证","实地看房后再谈价格","通过银行转账留下记录","签订正式租房合同"]},{id:"case003",title:'网贷"砍头息"陷阱，实际年利率超过100%',author:"理财新手",category:"finance",riskLevel:"high",amount:1e4,publishedAt:"2024-01-13",views:1580,likes:124,comments:35,verified:!0,tags:["砍头息","高利贷","网贷陷阱"],summary:"急需用钱申请网贷，遇到砍头息陷阱，实际利率惊人",content:`因为急需1万元周转，我在网上找到一个贷款平台，声称"低息快贷，当天放款"。

申请时平台说月利率只有1.5%，看起来很合理。但放款时发现实际到账只有8000元，平台说扣除了"服务费"、"手续费"等2000元。

还款时我才发现，虽然借了1万元，但实际只拿到8000元，却要按1万元还款。算下来实际年利率超过了100%，这就是典型的"砍头息"。

现在我深陷其中，每月还款压力巨大，真后悔当初没有仔细了解。`,lessons:["砍头息是违法的高利贷行为","借款前要仔细计算实际利率","正规金融机构不会预扣费用","遇到资金困难要通过正当渠道解决"],prevention:["选择银行等正规金融机构","仔细阅读借款合同条款","计算真实的借款成本",'不要被"低息"广告迷惑']}],getRiskColor=e=>{switch(e){case"high":return"text-red-600 bg-red-100";case"medium":return"text-yellow-600 bg-yellow-100";case"low":return"text-green-600 bg-green-100";default:return"text-gray-600 bg-gray-100"}},getRiskLabel=e=>{switch(e){case"high":return"高危";case"medium":return"中危";case"low":return"低危";default:return"未知"}},getCategoryLabel=e=>{switch(e){case"job":return"求职骗局";case"rental":return"租房陷阱";case"finance":return"金融诈骗";case"education":return"教育骗局";default:return"其他"}},g=u.filter(s=>{let a="all"===e||s.category===e,r="all"===t||s.riskLevel===t;return a&&r});return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 to-orange-100",children:[a.jsx("header",{className:"bg-white shadow-sm border-b",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(h(),{href:"/",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(l.Z,{className:"w-5 h-5 text-gray-600"})}),a.jsx(i.Z,{className:"w-8 h-8 text-red-600"}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"真实案例"}),a.jsx("p",{className:"text-sm text-gray-600",children:"真实经历，警示教育"})]})]})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"card mb-8 bg-gradient-to-r from-red-500 to-orange-600 text-white",children:[a.jsx("h2",{className:"text-2xl font-bold mb-6",children:"案例统计"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-2xl font-bold",children:p.totalCases}),a.jsx("div",{className:"text-sm text-red-200",children:"总案例数"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-2xl font-bold",children:p.verifiedCases}),a.jsx("div",{className:"text-sm text-red-200",children:"已验证"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-2xl font-bold",children:p.totalViews.toLocaleString()}),a.jsx("div",{className:"text-sm text-red-200",children:"总浏览量"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-2xl font-bold",children:p.helpfulVotes}),a.jsx("div",{className:"text-sm text-red-200",children:"有用投票"})]})]})]}),a.jsx("div",{className:"card mb-8",children:(0,a.jsxs)("div",{className:"flex flex-wrap gap-4 items-center",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(n.Z,{className:"w-4 h-4 text-gray-600"}),a.jsx("span",{className:"text-sm font-medium text-gray-700",children:"筛选："})]}),(0,a.jsxs)("select",{value:e,onChange:e=>s(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg text-sm",children:[a.jsx("option",{value:"all",children:"全部分类"}),a.jsx("option",{value:"job",children:"求职骗局"}),a.jsx("option",{value:"rental",children:"租房陷阱"}),a.jsx("option",{value:"finance",children:"金融诈骗"}),a.jsx("option",{value:"education",children:"教育骗局"})]}),(0,a.jsxs)("select",{value:t,onChange:e=>m(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg text-sm",children:[a.jsx("option",{value:"all",children:"全部风险等级"}),a.jsx("option",{value:"high",children:"高危"}),a.jsx("option",{value:"medium",children:"中危"}),a.jsx("option",{value:"low",children:"低危"})]})]})}),a.jsx("div",{className:"space-y-6",children:g.map(e=>a.jsx("div",{className:"card hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-start gap-4 mb-4",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx(i.Z,{className:"w-6 h-6 text-red-500"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[a.jsx("h3",{className:"text-lg font-bold text-gray-900",children:e.title}),e.verified&&a.jsx("span",{className:"px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full",children:"已验证"}),a.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${getRiskColor(e.riskLevel)}`,children:getRiskLabel(e.riskLevel)})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600 mb-3",children:[(0,a.jsxs)("span",{children:["作者: ",e.author]}),(0,a.jsxs)("span",{children:["分类: ",getCategoryLabel(e.category)]}),(0,a.jsxs)("span",{children:["涉及金额: \xa5",e.amount.toLocaleString()]}),(0,a.jsxs)("span",{children:["发布: ",e.publishedAt]})]}),a.jsx("p",{className:"text-gray-700 mb-4",children:e.summary}),a.jsx("div",{className:"flex flex-wrap gap-2 mb-4",children:e.tags.map((e,s)=>(0,a.jsxs)("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded",children:["#",e]},s))}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:[a.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"案例详情"}),a.jsx("div",{className:"text-gray-700 text-sm whitespace-pre-line",children:e.content})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"bg-red-50 rounded-lg p-3",children:[a.jsx("h5",{className:"font-semibold text-red-900 mb-2",children:"⚠️ 经验教训"}),a.jsx("ul",{className:"text-red-800 text-sm space-y-1",children:e.lessons.map((e,s)=>(0,a.jsxs)("li",{children:["• ",e]},s))})]}),(0,a.jsxs)("div",{className:"bg-green-50 rounded-lg p-3",children:[a.jsx("h5",{className:"font-semibold text-green-900 mb-2",children:"\uD83D\uDEE1️ 防范措施"}),a.jsx("ul",{className:"text-green-800 text-sm space-y-1",children:e.prevention.map((e,s)=>(0,a.jsxs)("li",{children:["• ",e]},s))})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[a.jsx(c.Z,{className:"w-4 h-4"}),a.jsx("span",{children:e.views})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[a.jsx(d.Z,{className:"w-4 h-4"}),a.jsx("span",{children:e.likes})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[a.jsx(x.Z,{className:"w-4 h-4"}),a.jsx("span",{children:e.comments})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("button",{className:"flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-red-600 transition-colors",children:[a.jsx(d.Z,{className:"w-4 h-4"}),"有用"]}),(0,a.jsxs)("button",{className:"flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-blue-600 transition-colors",children:[a.jsx(o.Z,{className:"w-4 h-4"}),"分享"]})]})]})]})]})},e.id))}),(0,a.jsxs)("div",{className:"card mt-8 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-center",children:[a.jsx("h3",{className:"text-xl font-bold mb-4",children:"分享你的经历，帮助更多人"}),a.jsx("p",{className:"text-blue-100 mb-6",children:"如果你也有防诈骗的真实经历，欢迎分享给大家，让更多人从中受益。"}),(0,a.jsxs)(h(),{href:"/demo-share",className:"btn-secondary inline-flex items-center gap-2",children:[a.jsx(o.Z,{className:"w-5 h-5"}),"分享我的经历"]})]})]})]})}},5507:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>l,default:()=>c});var a=t(5153);let r=(0,a.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/real-cases/page.tsx`),{__esModule:l,$$typeof:i}=r,n=r.default,c=n}};var s=require("../../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[9678,5607,6815,7773,2035],()=>__webpack_exec__(5806));module.exports=t})();