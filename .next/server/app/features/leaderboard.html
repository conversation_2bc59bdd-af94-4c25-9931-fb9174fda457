<!DOCTYPE html><html lang="zh-CN"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/97115f26cb754c8c.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-69bfb055b70a1563.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-a0c76284e27c8afa.js" async="" crossorigin=""></script><script src="/_next/static/chunks/472-af003cc8e47f5b1f.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-52f18067e7ca02da.js" async="" crossorigin=""></script><script src="/_next/static/chunks/326-cf5b0283528485c7.js" async=""></script><script src="/_next/static/chunks/app/not-found-5c4df079dd57eb5a.js" async=""></script><script src="/_next/static/chunks/app/features/leaderboard/page-fab0aa61f6889045.js" async=""></script><title>大学生防诈挑战游戏</title><meta name="description" content="通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱"/><meta name="author" content="防诈挑战游戏团队"/><meta name="keywords" content="防诈骗,大学生,安全教育,答题游戏"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body><div class="min-h-screen"><div class="min-h-screen bg-gradient-to-br from-yellow-50 to-orange-100"><header class="bg-white shadow-sm border-b"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4"><div class="flex items-center gap-3"><a class="p-2 hover:bg-gray-100 rounded-lg transition-colors" href="/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left w-5 h-5 text-gray-600"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg></a><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trophy w-8 h-8 text-yellow-600"><path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6"></path><path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18"></path><path d="M4 22h16"></path><path d="M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22"></path><path d="M14 14.66V17c0 .*********** 1.21C16.15 18.75 17 20.24 17 22"></path><path d="M18 2H6v7a6 6 0 0 0 12 0V2Z"></path></svg><div><h1 class="text-2xl font-bold text-gray-900">排行榜</h1><p class="text-sm text-gray-600">竞技学习，激发动力</p></div></div></div></header><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="card mb-8 bg-gradient-to-r from-yellow-500 to-orange-600 text-white"><h2 class="text-2xl font-bold mb-6">竞技统计</h2><div class="grid grid-cols-2 md:grid-cols-4 gap-4"><div class="text-center"><div class="text-2xl font-bold">15,420</div><div class="text-sm text-yellow-200">总参与人数</div></div><div class="text-center"><div class="text-2xl font-bold">892</div><div class="text-sm text-yellow-200">今日活跃</div></div><div class="text-center"><div class="text-2xl font-bold">78.5<!-- -->%</div><div class="text-sm text-yellow-200">平均分数</div></div><div class="text-center"><div class="text-2xl font-bold">98<!-- -->%</div><div class="text-sm text-yellow-200">最高分数</div></div></div></div><div class="grid grid-cols-1 lg:grid-cols-3 gap-8"><div class="lg:col-span-2"><div class="card"><div class="flex items-center justify-between mb-6"><h3 class="text-xl font-bold text-gray-900">总排行榜</h3><div class="flex gap-2"><select class="px-3 py-1 border border-gray-300 rounded-lg text-sm"><option value="daily">今日</option><option value="weekly" selected="">本周</option><option value="monthly">本月</option><option value="all">全部</option></select><select class="px-3 py-1 border border-gray-300 rounded-lg text-sm"><option value="all" selected="">全部分类</option><option value="job">求职防骗</option><option value="rental">租房安全</option><option value="finance">金融理财</option></select></div></div><div class="space-y-4"><div class="p-4 rounded-lg border-2 transition-all bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200"><div class="flex items-center gap-4"><div class="flex-shrink-0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-crown w-6 h-6 text-yellow-500"><path d="m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14"></path></svg></div><div class="text-3xl">👑</div><div class="flex-1"><div class="flex items-center gap-2 mb-1"><span class="font-bold text-gray-900">防诈大师</span><span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">专家级</span></div><div class="flex items-center gap-4 text-sm text-gray-600"><span>分数: <!-- -->98<!-- -->%</span><span>连胜: <!-- -->15</span><span>专长: <!-- -->综合</span><span>题目: <!-- -->450</span></div><div class="flex flex-wrap gap-1 mt-2"><span class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">连胜王</span><span class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">满分达人</span><span class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">知识渊博</span></div></div><div class="text-right"><div class="text-2xl font-bold text-primary-600">98<!-- -->%</div><div class="text-xs text-gray-500">#<!-- -->1</div></div></div></div><div class="p-4 rounded-lg border-2 transition-all bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200"><div class="flex items-center gap-4"><div class="flex-shrink-0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-medal w-6 h-6 text-gray-400"><path d="M7.21 15 2.66 7.14a2 2 0 0 1 .13-2.2L4.4 2.8A2 2 0 0 1 6 2h12a2 2 0 0 1 1.6.8l1.6 2.14a2 2 0 0 1 .14 2.2L16.79 15"></path><path d="M11 12 5.12 2.2"></path><path d="m13 12 5.88-9.8"></path><path d="M8 7h8"></path><circle cx="12" cy="17" r="5"></circle><path d="M12 18v-2h-.5"></path></svg></div><div class="text-3xl">🛡️</div><div class="flex-1"><div class="flex items-center gap-2 mb-1"><span class="font-bold text-gray-900">安全卫士</span><span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">高级</span></div><div class="flex items-center gap-4 text-sm text-gray-600"><span>分数: <!-- -->96<!-- -->%</span><span>连胜: <!-- -->12</span><span>专长: <!-- -->求职防骗</span><span>题目: <!-- -->380</span></div><div class="flex flex-wrap gap-1 mt-2"><span class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">求职专家</span><span class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">稳定发挥</span></div></div><div class="text-right"><div class="text-2xl font-bold text-primary-600">96<!-- -->%</div><div class="text-xs text-gray-500">#<!-- -->2</div></div></div></div><div class="p-4 rounded-lg border-2 transition-all bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200"><div class="flex items-center gap-4"><div class="flex-shrink-0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-medal w-6 h-6 text-amber-600"><path d="M7.21 15 2.66 7.14a2 2 0 0 1 .13-2.2L4.4 2.8A2 2 0 0 1 6 2h12a2 2 0 0 1 1.6.8l1.6 2.14a2 2 0 0 1 .14 2.2L16.79 15"></path><path d="M11 12 5.12 2.2"></path><path d="m13 12 5.88-9.8"></path><path d="M8 7h8"></path><circle cx="12" cy="17" r="5"></circle><path d="M12 18v-2h-.5"></path></svg></div><div class="text-3xl">📚</div><div class="flex-1"><div class="flex items-center gap-2 mb-1"><span class="font-bold text-gray-900">谨慎学者</span><span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">高级</span></div><div class="flex items-center gap-4 text-sm text-gray-600"><span>分数: <!-- -->95<!-- -->%</span><span>连胜: <!-- -->10</span><span>专长: <!-- -->金融理财</span><span>题目: <!-- -->320</span></div><div class="flex flex-wrap gap-1 mt-2"><span class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">理财达人</span><span class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">学习之星</span></div></div><div class="text-right"><div class="text-2xl font-bold text-primary-600">95<!-- -->%</div><div class="text-xs text-gray-500">#<!-- -->3</div></div></div></div><div class="p-4 rounded-lg border-2 transition-all bg-gray-50 border-gray-200"><div class="flex items-center gap-4"><div class="flex-shrink-0"><span class="w-6 h-6 flex items-center justify-center text-gray-600 font-bold">4</span></div><div class="text-3xl">🏠</div><div class="flex-1"><div class="flex items-center gap-2 mb-1"><span class="font-bold text-gray-900">防骗达人</span><span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">中级</span></div><div class="flex items-center gap-4 text-sm text-gray-600"><span>分数: <!-- -->94<!-- -->%</span><span>连胜: <!-- -->8</span><span>专长: <!-- -->租房安全</span><span>题目: <!-- -->280</span></div><div class="flex flex-wrap gap-1 mt-2"><span class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">租房专家</span></div></div><div class="text-right"><div class="text-2xl font-bold text-primary-600">94<!-- -->%</div><div class="text-xs text-gray-500">#<!-- -->4</div></div></div></div><div class="p-4 rounded-lg border-2 transition-all bg-gray-50 border-gray-200"><div class="flex items-center gap-4"><div class="flex-shrink-0"><span class="w-6 h-6 flex items-center justify-center text-gray-600 font-bold">5</span></div><div class="text-3xl">🧠</div><div class="flex-1"><div class="flex items-center gap-2 mb-1"><span class="font-bold text-gray-900">智慧守护</span><span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">中级</span></div><div class="flex items-center gap-4 text-sm text-gray-600"><span>分数: <!-- -->93<!-- -->%</span><span>连胜: <!-- -->7</span><span>专长: <!-- -->综合</span><span>题目: <!-- -->250</span></div><div class="flex flex-wrap gap-1 mt-2"><span class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">全能选手</span></div></div><div class="text-right"><div class="text-2xl font-bold text-primary-600">93<!-- -->%</div><div class="text-xs text-gray-500">#<!-- -->5</div></div></div></div></div></div></div><div class="space-y-6"><div class="card"><h3 class="text-lg font-bold text-gray-900 mb-4">分类冠军</h3><div class="space-y-3"><div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg"><span class="text-2xl">💼</span><div class="flex-1"><div class="font-medium text-gray-900">防诈小能手</div><div class="text-sm text-gray-600">97<!-- -->% 正确率</div></div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-crown w-4 h-4 text-yellow-500"><path d="m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14"></path></svg></div><div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg"><span class="text-2xl">🏠</span><div class="flex-1"><div class="font-medium text-gray-900">房产专家</div><div class="text-sm text-gray-600">95<!-- -->% 正确率</div></div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-crown w-4 h-4 text-yellow-500"><path d="m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14"></path></svg></div><div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg"><span class="text-2xl">💰</span><div class="flex-1"><div class="font-medium text-gray-900">理财高手</div><div class="text-sm text-gray-600">96<!-- -->% 正确率</div></div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-crown w-4 h-4 text-yellow-500"><path d="m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14"></path></svg></div><div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg"><span class="text-2xl">🎓</span><div class="flex-1"><div class="font-medium text-gray-900">教育达人</div><div class="text-sm text-gray-600">94<!-- -->% 正确率</div></div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-crown w-4 h-4 text-yellow-500"><path d="m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14"></path></svg></div></div></div><div class="card"><h3 class="text-lg font-bold text-gray-900 mb-4">稀有成就</h3><div class="space-y-3"><div class="flex items-center gap-3 p-3 border border-gray-200 rounded-lg"><span class="text-xl">🔥</span><div class="flex-1"><div class="font-medium text-gray-900">连胜王</div><div class="text-sm text-gray-600">连续答对15题</div></div><span class="px-2 py-1 text-xs rounded-full text-purple-600 bg-purple-100">legendary</span></div><div class="flex items-center gap-3 p-3 border border-gray-200 rounded-lg"><span class="text-xl">💯</span><div class="flex-1"><div class="font-medium text-gray-900">满分达人</div><div class="text-sm text-gray-600">获得满分10次</div></div><span class="px-2 py-1 text-xs rounded-full text-orange-600 bg-orange-100">epic</span></div><div class="flex items-center gap-3 p-3 border border-gray-200 rounded-lg"><span class="text-xl">📖</span><div class="flex-1"><div class="font-medium text-gray-900">知识渊博</div><div class="text-sm text-gray-600">完成所有分类</div></div><span class="px-2 py-1 text-xs rounded-full text-blue-600 bg-blue-100">rare</span></div><div class="flex items-center gap-3 p-3 border border-gray-200 rounded-lg"><span class="text-xl">💼</span><div class="flex-1"><div class="font-medium text-gray-900">求职专家</div><div class="text-sm text-gray-600">求职类题目90%正确率</div></div><span class="px-2 py-1 text-xs rounded-full text-green-600 bg-green-100">uncommon</span></div><div class="flex items-center gap-3 p-3 border border-gray-200 rounded-lg"><span class="text-xl">💰</span><div class="flex-1"><div class="font-medium text-gray-900">理财达人</div><div class="text-sm text-gray-600">金融类题目90%正确率</div></div><span class="px-2 py-1 text-xs rounded-full text-green-600 bg-green-100">uncommon</span></div></div></div><div class="card bg-gradient-to-r from-blue-500 to-purple-600 text-white"><h3 class="text-lg font-bold mb-4">我的排名</h3><div class="text-center"><div class="text-3xl mb-2">🎯</div><div class="text-2xl font-bold mb-1">#42</div><div class="text-sm text-blue-200 mb-3">当前排名</div><div class="grid grid-cols-2 gap-4 text-sm"><div><div class="font-bold">85%</div><div class="text-blue-200">我的分数</div></div><div><div class="font-bold">5</div><div class="text-blue-200">连胜次数</div></div></div><a class="btn-secondary mt-4 w-full" href="/demo-quiz/">继续挑战</a></div></div></div></div><div class="card mt-8"><h3 class="text-xl font-bold text-gray-900 mb-6">竞技规则</h3><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"><div class="text-center"><div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-star w-6 h-6 text-blue-600"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg></div><h4 class="font-semibold text-gray-900 mb-2">分数计算</h4><p class="text-sm text-gray-600">基于正确率和答题速度综合计算</p></div><div class="text-center"><div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trending-up w-6 h-6 text-green-600"><polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline><polyline points="16 7 22 7 22 13"></polyline></svg></div><h4 class="font-semibold text-gray-900 mb-2">连胜奖励</h4><p class="text-sm text-gray-600">连续答对可获得额外分数加成</p></div><div class="text-center"><div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar w-6 h-6 text-purple-600"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line></svg></div><h4 class="font-semibold text-gray-900 mb-2">周期更新</h4><p class="text-sm text-gray-600">排行榜每周重置，保持竞争活力</p></div><div class="text-center"><div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trophy w-6 h-6 text-yellow-600"><path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6"></path><path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18"></path><path d="M4 22h16"></path><path d="M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22"></path><path d="M14 14.66V17c0 .*********** 1.21C16.15 18.75 17 20.24 17 22"></path><path d="M18 2H6v7a6 6 0 0 0 12 0V2Z"></path></svg></div><h4 class="font-semibold text-gray-900 mb-2">奖励机制</h4><p class="text-sm text-gray-600">前三名可获得特殊称号和徽章</p></div></div></div></div></div></div><script src="/_next/static/chunks/webpack-69bfb055b70a1563.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/css/97115f26cb754c8c.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L2\"\n"])</script><script>self.__next_f.push([1,"3:I[3728,[],\"\"]\n5:I[9928,[],\"\"]\n6:I[6954,[],\"\"]\n7:I[7264,[],\"\"]\n8:I[8326,[\"326\",\"static/chunks/326-cf5b0283528485c7.js\",\"160\",\"static/chunks/app/not-found-5c4df079dd57eb5a.js\"],\"\"]\na:I[8297,[],\"\"]\nb:I[3593,[\"326\",\"static/chunks/326-cf5b0283528485c7.js\",\"514\",\"static/chunks/app/features/leaderboard/page-fab0aa61f6889045.js\"],\"\"]\n"])</script><script>self.__next_f.push([1,"2:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/97115f26cb754c8c.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L3\",null,{\"buildId\":\"-csu5AGVUyiuSzdvekIsf\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/features/leaderboard/\",\"initialTree\":[\"\",{\"children\":[\"features\",{\"children\":[\"leaderboard\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],\"initialHead\":[false,\"$L4\"],\"globalErrorComponent\":\"$5\",\"children\":[null,[\"$\",\"html\",null,{\"lang\":\"zh-CN\",\"children\":[\"$\",\"body\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"min-h-screen\",\"children\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4 sm:px-6 lg:px-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-md w-full text-center\",\"children\":[\"$\",\"div\",null,{\"className\":\"card\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mb-6\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-alert-triangle w-16 h-16 text-yellow-500 mx-auto mb-4\",\"children\":[[\"$\",\"path\",\"c3ski4\",{\"d\":\"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z\"}],[\"$\",\"path\",\"juzpu7\",{\"d\":\"M12 9v4\"}],[\"$\",\"path\",\"p32p05\",{\"d\":\"M12 17h.01\"}],\"$undefined\"]}],[\"$\",\"h1\",null,{\"className\":\"text-6xl font-bold text-gray-900 mb-2\",\"children\":\"404\"}],[\"$\",\"h2\",null,{\"className\":\"text-2xl font-semibold text-gray-700 mb-4\",\"children\":\"页面未找到\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600\",\"children\":\"抱歉，你访问的页面不存在。可能是链接错误或页面已被移除。\"}]]}],[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"btn-primary w-full flex items-center justify-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-home w-5 h-5\",\"children\":[[\"$\",\"path\",\"y5dka4\",{\"d\":\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"}],[\"$\",\"polyline\",\"e2us08\",{\"points\":\"9 22 9 12 15 12 15 22\"}],\"$undefined\"]}],\"返回首页\"]}],[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"btn-secondary w-full flex items-center justify-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-left w-5 h-5\",\"children\":[[\"$\",\"path\",\"1l729n\",{\"d\":\"m12 19-7-7 7-7\"}],[\"$\",\"path\",\"x3x0zl\",{\"d\":\"M19 12H5\"}],\"$undefined\"]}],\"返回上一页\"]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\"children\":[\"$\",\"p\",null,{\"className\":\"text-blue-800 text-sm\",\"children\":[\"💡 \",[\"$\",\"strong\",null,{\"children\":\"提示：\"}],\"如果你是通过链接访问的，请检查链接是否正确。 如果问题持续存在，请联系我们。\"]}]}]]}]}]}],\"notFoundStyles\":[],\"childProp\":{\"current\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"features\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"childProp\":{\"current\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"features\",\"children\",\"leaderboard\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"childProp\":{\"current\":[\"$L9\",[\"$\",\"$La\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$b\",\"isStaticGeneration\":true}],null],\"segment\":\"__PAGE__\"},\"styles\":null}],\"segment\":\"leaderboard\"},\"styles\":null}],\"segment\":\"features\"},\"styles\":null}]}]}]}],null]}]]\n"])</script><script>self.__next_f.push([1,"4:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"大学生防诈挑战游戏\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱\"}],[\"$\",\"meta\",\"4\",{\"name\":\"author\",\"content\":\"防诈挑战游戏团队\"}],[\"$\",\"meta\",\"5\",{\"name\":\"keywords\",\"content\":\"防诈骗,大学生,安全教育,答题游戏\"}]]\n9:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>