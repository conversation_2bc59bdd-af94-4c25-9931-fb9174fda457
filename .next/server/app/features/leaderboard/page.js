(()=>{var e={};e.id=1514,e.ids=[1514],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},3571:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>o,pages:()=>x,routeModule:()=>h,tree:()=>d});var t=a(7096),r=a(6132),l=a(7284),n=a.n(l),c=a(2564),i={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>c[e]);a.d(s,i);let d=["",{children:["features",{children:["leaderboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,4229)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/leaderboard/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],x=["/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/leaderboard/page.tsx"],o="/features/leaderboard/page",m={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/features/leaderboard/page",pathname:"/features/leaderboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3821:(e,s,a)=>{Promise.resolve().then(a.bind(a,4172))},4172:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>LeaderboardPage});var t=a(784),r=a(9885),l=a(4402),n=a(405),c=a(9803),i=a(1438),d=a(7932),x=a(7),o=a(517),m=a(1440),h=a.n(m);function LeaderboardPage(){let[e,s]=(0,r.useState)("weekly"),[a,m]=(0,r.useState)("all"),u={totalPlayers:15420,activeToday:892,averageScore:78.5,topScore:98},getRankIcon=e=>{switch(e){case 1:return t.jsx(l.Z,{className:"w-6 h-6 text-yellow-500"});case 2:return t.jsx(n.Z,{className:"w-6 h-6 text-gray-400"});case 3:return t.jsx(n.Z,{className:"w-6 h-6 text-amber-600"});default:return t.jsx("span",{className:"w-6 h-6 flex items-center justify-center text-gray-600 font-bold",children:e})}},getRarityColor=e=>{switch(e){case"legendary":return"text-purple-600 bg-purple-100";case"epic":return"text-orange-600 bg-orange-100";case"rare":return"text-blue-600 bg-blue-100";case"uncommon":return"text-green-600 bg-green-100";default:return"text-gray-600 bg-gray-100"}};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-yellow-50 to-orange-100",children:[t.jsx("header",{className:"bg-white shadow-sm border-b",children:t.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[t.jsx(h(),{href:"/",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:t.jsx(c.Z,{className:"w-5 h-5 text-gray-600"})}),t.jsx(i.Z,{className:"w-8 h-8 text-yellow-600"}),(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"排行榜"}),t.jsx("p",{className:"text-sm text-gray-600",children:"竞技学习，激发动力"})]})]})})}),(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"card mb-8 bg-gradient-to-r from-yellow-500 to-orange-600 text-white",children:[t.jsx("h2",{className:"text-2xl font-bold mb-6",children:"竞技统计"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"text-2xl font-bold",children:u.totalPlayers.toLocaleString()}),t.jsx("div",{className:"text-sm text-yellow-200",children:"总参与人数"})]}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"text-2xl font-bold",children:u.activeToday}),t.jsx("div",{className:"text-sm text-yellow-200",children:"今日活跃"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[u.averageScore,"%"]}),t.jsx("div",{className:"text-sm text-yellow-200",children:"平均分数"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[u.topScore,"%"]}),t.jsx("div",{className:"text-sm text-yellow-200",children:"最高分数"})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[t.jsx("div",{className:"lg:col-span-2",children:(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[t.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"总排行榜"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("select",{value:e,onChange:e=>s(e.target.value),className:"px-3 py-1 border border-gray-300 rounded-lg text-sm",children:[t.jsx("option",{value:"daily",children:"今日"}),t.jsx("option",{value:"weekly",children:"本周"}),t.jsx("option",{value:"monthly",children:"本月"}),t.jsx("option",{value:"all",children:"全部"})]}),(0,t.jsxs)("select",{value:a,onChange:e=>m(e.target.value),className:"px-3 py-1 border border-gray-300 rounded-lg text-sm",children:[t.jsx("option",{value:"all",children:"全部分类"}),t.jsx("option",{value:"job",children:"求职防骗"}),t.jsx("option",{value:"rental",children:"租房安全"}),t.jsx("option",{value:"finance",children:"金融理财"})]})]})]}),t.jsx("div",{className:"space-y-4",children:[{rank:1,nickname:"防诈大师",score:98,streak:15,category:"综合",avatar:"\uD83D\uDC51",level:"专家级",achievements:["连胜王","满分达人","知识渊博"],joinDate:"2024-01-01",totalQuestions:450},{rank:2,nickname:"安全卫士",score:96,streak:12,category:"求职防骗",avatar:"\uD83D\uDEE1️",level:"高级",achievements:["求职专家","稳定发挥"],joinDate:"2024-01-03",totalQuestions:380},{rank:3,nickname:"谨慎学者",score:95,streak:10,category:"金融理财",avatar:"\uD83D\uDCDA",level:"高级",achievements:["理财达人","学习之星"],joinDate:"2024-01-05",totalQuestions:320},{rank:4,nickname:"防骗达人",score:94,streak:8,category:"租房安全",avatar:"\uD83C\uDFE0",level:"中级",achievements:["租房专家"],joinDate:"2024-01-08",totalQuestions:280},{rank:5,nickname:"智慧守护",score:93,streak:7,category:"综合",avatar:"\uD83E\uDDE0",level:"中级",achievements:["全能选手"],joinDate:"2024-01-10",totalQuestions:250}].map(e=>t.jsx("div",{className:`p-4 rounded-lg border-2 transition-all ${e.rank<=3?"bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200":"bg-gray-50 border-gray-200"}`,children:(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[t.jsx("div",{className:"flex-shrink-0",children:getRankIcon(e.rank)}),t.jsx("div",{className:"text-3xl",children:e.avatar}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[t.jsx("span",{className:"font-bold text-gray-900",children:e.nickname}),t.jsx("span",{className:"px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full",children:e.level})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,t.jsxs)("span",{children:["分数: ",e.score,"%"]}),(0,t.jsxs)("span",{children:["连胜: ",e.streak]}),(0,t.jsxs)("span",{children:["专长: ",e.category]}),(0,t.jsxs)("span",{children:["题目: ",e.totalQuestions]})]}),t.jsx("div",{className:"flex flex-wrap gap-1 mt-2",children:e.achievements.map((e,s)=>t.jsx("span",{className:"px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full",children:e},s))})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("div",{className:"text-2xl font-bold text-primary-600",children:[e.score,"%"]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["#",e.rank]})]})]})},e.rank))})]})}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"card",children:[t.jsx("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"分类冠军"}),t.jsx("div",{className:"space-y-3",children:Object.entries({job:{name:"防诈小能手",score:97,avatar:"\uD83D\uDCBC"},rental:{name:"房产专家",score:95,avatar:"\uD83C\uDFE0"},finance:{name:"理财高手",score:96,avatar:"\uD83D\uDCB0"},education:{name:"教育达人",score:94,avatar:"\uD83C\uDF93"}}).map(([e,s])=>(0,t.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[t.jsx("span",{className:"text-2xl",children:s.avatar}),(0,t.jsxs)("div",{className:"flex-1",children:[t.jsx("div",{className:"font-medium text-gray-900",children:s.name}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[s.score,"% 正确率"]})]}),t.jsx(l.Z,{className:"w-4 h-4 text-yellow-500"})]},e))})]}),(0,t.jsxs)("div",{className:"card",children:[t.jsx("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"稀有成就"}),t.jsx("div",{className:"space-y-3",children:[{name:"连胜王",description:"连续答对15题",icon:"\uD83D\uDD25",rarity:"legendary"},{name:"满分达人",description:"获得满分10次",icon:"\uD83D\uDCAF",rarity:"epic"},{name:"知识渊博",description:"完成所有分类",icon:"\uD83D\uDCD6",rarity:"rare"},{name:"求职专家",description:"求职类题目90%正确率",icon:"\uD83D\uDCBC",rarity:"uncommon"},{name:"理财达人",description:"金融类题目90%正确率",icon:"\uD83D\uDCB0",rarity:"uncommon"}].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center gap-3 p-3 border border-gray-200 rounded-lg",children:[t.jsx("span",{className:"text-xl",children:e.icon}),(0,t.jsxs)("div",{className:"flex-1",children:[t.jsx("div",{className:"font-medium text-gray-900",children:e.name}),t.jsx("div",{className:"text-sm text-gray-600",children:e.description})]}),t.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${getRarityColor(e.rarity)}`,children:e.rarity})]},s))})]}),(0,t.jsxs)("div",{className:"card bg-gradient-to-r from-blue-500 to-purple-600 text-white",children:[t.jsx("h3",{className:"text-lg font-bold mb-4",children:"我的排名"}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"text-3xl mb-2",children:"\uD83C\uDFAF"}),t.jsx("div",{className:"text-2xl font-bold mb-1",children:"#42"}),t.jsx("div",{className:"text-sm text-blue-200 mb-3",children:"当前排名"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[t.jsx("div",{className:"font-bold",children:"85%"}),t.jsx("div",{className:"text-blue-200",children:"我的分数"})]}),(0,t.jsxs)("div",{children:[t.jsx("div",{className:"font-bold",children:"5"}),t.jsx("div",{className:"text-blue-200",children:"连胜次数"})]})]}),t.jsx(h(),{href:"/demo-quiz",className:"btn-secondary mt-4 w-full",children:"继续挑战"})]})]})]})]}),(0,t.jsxs)("div",{className:"card mt-8",children:[t.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-6",children:"竞技规则"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3",children:t.jsx(d.Z,{className:"w-6 h-6 text-blue-600"})}),t.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"分数计算"}),t.jsx("p",{className:"text-sm text-gray-600",children:"基于正确率和答题速度综合计算"})]}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3",children:t.jsx(x.Z,{className:"w-6 h-6 text-green-600"})}),t.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"连胜奖励"}),t.jsx("p",{className:"text-sm text-gray-600",children:"连续答对可获得额外分数加成"})]}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3",children:t.jsx(o.Z,{className:"w-6 h-6 text-purple-600"})}),t.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"周期更新"}),t.jsx("p",{className:"text-sm text-gray-600",children:"排行榜每周重置，保持竞争活力"})]}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3",children:t.jsx(i.Z,{className:"w-6 h-6 text-yellow-600"})}),t.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"奖励机制"}),t.jsx("p",{className:"text-sm text-gray-600",children:"前三名可获得特殊称号和徽章"})]})]})]})]})]})}},4229:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>n,__esModule:()=>l,default:()=>i});var t=a(5153);let r=(0,t.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/leaderboard/page.tsx`),{__esModule:l,$$typeof:n}=r,c=r.default,i=c}};var s=require("../../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),a=s.X(0,[9678,5607,1016,2677,2118,2035],()=>__webpack_exec__(3571));module.exports=a})();