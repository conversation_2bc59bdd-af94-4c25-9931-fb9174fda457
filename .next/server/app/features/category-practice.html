<!DOCTYPE html><html lang="zh-CN"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/97115f26cb754c8c.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-69bfb055b70a1563.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-a0c76284e27c8afa.js" async="" crossorigin=""></script><script src="/_next/static/chunks/472-af003cc8e47f5b1f.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-52f18067e7ca02da.js" async="" crossorigin=""></script><script src="/_next/static/chunks/326-cf5b0283528485c7.js" async=""></script><script src="/_next/static/chunks/app/not-found-5c4df079dd57eb5a.js" async=""></script><script src="/_next/static/chunks/app/features/category-practice/page-8aa028b980042304.js" async=""></script><title>大学生防诈挑战游戏</title><meta name="description" content="通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱"/><meta name="author" content="防诈挑战游戏团队"/><meta name="keywords" content="防诈骗,大学生,安全教育,答题游戏"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body><div class="min-h-screen"><div class="min-h-screen bg-gradient-to-br from-indigo-50 to-purple-100"><header class="bg-white shadow-sm border-b"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4"><div class="flex items-center gap-3"><a class="p-2 hover:bg-gray-100 rounded-lg transition-colors" href="/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left w-5 h-5 text-gray-600"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg></a><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-target w-8 h-8 text-indigo-600"><circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="6"></circle><circle cx="12" cy="12" r="2"></circle></svg><div><h1 class="text-2xl font-bold text-gray-900">分类刷题</h1><p class="text-sm text-gray-600">针对性强化，系统提升</p></div></div></div></header><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="card mb-8 bg-gradient-to-r from-indigo-500 to-purple-600 text-white"><h2 class="text-2xl font-bold mb-6">学习进度统计</h2><div class="grid grid-cols-2 md:grid-cols-4 gap-4"><div class="text-center"><div class="text-2xl font-bold">3<!-- -->/<!-- -->7</div><div class="text-sm text-indigo-200">完成分类</div></div><div class="text-center"><div class="text-2xl font-bold">85<!-- -->/<!-- -->180</div><div class="text-sm text-indigo-200">答题进度</div></div><div class="text-center"><div class="text-2xl font-bold">82.5<!-- -->%</div><div class="text-sm text-indigo-200">平均正确率</div></div><div class="text-center"><div class="text-2xl font-bold">45<!-- -->h</div><div class="text-sm text-indigo-200">学习时长</div></div></div></div><div class="grid grid-cols-1 lg:grid-cols-3 gap-8"><div class="lg:col-span-2"><div class="card mb-6"><div class="flex items-center justify-between mb-6"><h3 class="text-xl font-bold text-gray-900">练习分类</h3><div class="flex gap-2"><button class="px-3 py-1 rounded-lg text-sm transition-all text-green-600 bg-green-100">入门</button><button class="px-3 py-1 rounded-lg text-sm transition-all bg-gray-100 text-gray-600 hover:bg-gray-200">进阶</button><button class="px-3 py-1 rounded-lg text-sm transition-all bg-gray-100 text-gray-600 hover:bg-gray-200">高级</button></div></div><div class="space-y-4"><div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start gap-4"><div class="text-3xl">💼</div><div class="flex-1"><div class="flex items-center gap-3 mb-2"><h4 class="text-lg font-semibold text-gray-900">求职防骗</h4><span class="px-2 py-1 rounded-full text-xs font-medium text-green-600 bg-green-100">入门</span></div><p class="text-gray-600 mb-3">识别虚假招聘、培训费诈骗等求职陷阱</p><div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3"><div class="text-center p-2 bg-blue-50 rounded"><div class="font-bold text-blue-600">15</div><div class="text-xs text-gray-600">题目数</div></div><div class="text-center p-2 bg-green-50 rounded"><div class="font-bold text-green-600">15</div><div class="text-xs text-gray-600">已完成</div></div><div class="text-center p-2 bg-yellow-50 rounded"><div class="font-bold text-yellow-600">90<!-- -->%</div><div class="text-xs text-gray-600">正确率</div></div><div class="text-center p-2 bg-purple-50 rounded"><div class="font-bold text-purple-600">25分钟</div><div class="text-xs text-gray-600">预计时长</div></div></div><div class="flex flex-wrap gap-2 mb-3"><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">虚假招聘识别</span><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">培训费陷阱</span><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">保证金诈骗</span><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">网络兼职风险</span></div><div class="flex items-center justify-between"><div class="flex-1 mr-4"><div class="flex items-center justify-between text-sm mb-1"><span>完成进度</span><span>100<!-- -->%</span></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-primary-600 h-2 rounded-full transition-all" style="width:100%"></div></div></div><a class="btn-primary flex items-center gap-2" href="/category-quiz/?category=job-fraud&amp;difficulty=beginner"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-4 h-4"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg>继续练习</a></div></div></div></div><div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start gap-4"><div class="text-3xl">🏠</div><div class="flex-1"><div class="flex items-center gap-3 mb-2"><h4 class="text-lg font-semibold text-gray-900">租房安全</h4><span class="px-2 py-1 rounded-full text-xs font-medium text-green-600 bg-green-100">入门</span></div><p class="text-gray-600 mb-3">防范假房东、押金诈骗等租房风险</p><div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3"><div class="text-center p-2 bg-blue-50 rounded"><div class="font-bold text-blue-600">12</div><div class="text-xs text-gray-600">题目数</div></div><div class="text-center p-2 bg-green-50 rounded"><div class="font-bold text-green-600">12</div><div class="text-xs text-gray-600">已完成</div></div><div class="text-center p-2 bg-yellow-50 rounded"><div class="font-bold text-yellow-600">88<!-- -->%</div><div class="text-xs text-gray-600">正确率</div></div><div class="text-center p-2 bg-purple-50 rounded"><div class="font-bold text-purple-600">20分钟</div><div class="text-xs text-gray-600">预计时长</div></div></div><div class="flex flex-wrap gap-2 mb-3"><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">假房东识别</span><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">押金安全</span><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">租房合同</span><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">中介陷阱</span></div><div class="flex items-center justify-between"><div class="flex-1 mr-4"><div class="flex items-center justify-between text-sm mb-1"><span>完成进度</span><span>100<!-- -->%</span></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-primary-600 h-2 rounded-full transition-all" style="width:100%"></div></div></div><a class="btn-primary flex items-center gap-2" href="/category-quiz/?category=rental-scam&amp;difficulty=beginner"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-4 h-4"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg>继续练习</a></div></div></div></div><div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start gap-4"><div class="text-3xl">💰</div><div class="flex-1"><div class="flex items-center gap-3 mb-2"><h4 class="text-lg font-semibold text-gray-900">金融理财</h4><span class="px-2 py-1 rounded-full text-xs font-medium text-green-600 bg-green-100">入门</span></div><p class="text-gray-600 mb-3">识别网贷陷阱、投资诈骗等金融风险</p><div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3"><div class="text-center p-2 bg-blue-50 rounded"><div class="font-bold text-blue-600">18</div><div class="text-xs text-gray-600">题目数</div></div><div class="text-center p-2 bg-green-50 rounded"><div class="font-bold text-green-600">10</div><div class="text-xs text-gray-600">已完成</div></div><div class="text-center p-2 bg-yellow-50 rounded"><div class="font-bold text-yellow-600">75<!-- -->%</div><div class="text-xs text-gray-600">正确率</div></div><div class="text-center p-2 bg-purple-50 rounded"><div class="font-bold text-purple-600">35分钟</div><div class="text-xs text-gray-600">预计时长</div></div></div><div class="flex flex-wrap gap-2 mb-3"><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">网贷陷阱</span><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">投资诈骗</span><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">理财产品</span><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">利率计算</span></div><div class="flex items-center justify-between"><div class="flex-1 mr-4"><div class="flex items-center justify-between text-sm mb-1"><span>完成进度</span><span>56<!-- -->%</span></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-primary-600 h-2 rounded-full transition-all" style="width:55.55555555555556%"></div></div></div><a class="btn-primary flex items-center gap-2" href="/category-quiz/?category=finance-trap&amp;difficulty=beginner"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-4 h-4"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg>继续练习</a></div></div></div></div><div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"><div class="flex items-start gap-4"><div class="text-3xl">🎓</div><div class="flex-1"><div class="flex items-center gap-3 mb-2"><h4 class="text-lg font-semibold text-gray-900">教育培训</h4><span class="px-2 py-1 rounded-full text-xs font-medium text-green-600 bg-green-100">入门</span></div><p class="text-gray-600 mb-3">防范培训诈骗、学历造假等教育陷阱</p><div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3"><div class="text-center p-2 bg-blue-50 rounded"><div class="font-bold text-blue-600">10</div><div class="text-xs text-gray-600">题目数</div></div><div class="text-center p-2 bg-green-50 rounded"><div class="font-bold text-green-600">0</div><div class="text-xs text-gray-600">已完成</div></div><div class="text-center p-2 bg-yellow-50 rounded"><div class="font-bold text-yellow-600">0<!-- -->%</div><div class="text-xs text-gray-600">正确率</div></div><div class="text-center p-2 bg-purple-50 rounded"><div class="font-bold text-purple-600">30分钟</div><div class="text-xs text-gray-600">预计时长</div></div></div><div class="flex flex-wrap gap-2 mb-3"><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">培训机构</span><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">学历认证</span><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">技能证书</span><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">就业承诺</span></div><div class="flex items-center justify-between"><div class="flex-1 mr-4"><div class="flex items-center justify-between text-sm mb-1"><span>完成进度</span><span>0<!-- -->%</span></div><div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-primary-600 h-2 rounded-full transition-all" style="width:0%"></div></div></div><a class="btn-primary flex items-center gap-2" href="/category-quiz/?category=education-scam&amp;difficulty=beginner"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-4 h-4"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg>开始练习</a></div></div></div></div></div></div></div><div class="space-y-6"><div class="card"><h3 class="text-lg font-bold text-gray-900 mb-4">学习计划</h3><div class="space-y-3"><div class="border border-gray-200 rounded-lg p-3"><div class="flex items-center gap-2 mb-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-circle w-5 h-5 text-green-600"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><path d="m9 11 3 3L22 4"></path></svg><span class="font-medium text-gray-900">第<!-- -->1<!-- -->周</span></div><div class="text-sm text-gray-900 mb-2">基础防诈骗认知</div><div class="text-xs text-gray-600 mb-2">求职防骗 • 租房安全</div><div class="w-full bg-gray-200 rounded-full h-1.5"><div class="bg-primary-600 h-1.5 rounded-full transition-all" style="width:100%"></div></div></div><div class="border border-gray-200 rounded-lg p-3"><div class="flex items-center gap-2 mb-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-5 h-5 text-blue-600"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg><span class="font-medium text-gray-900">第<!-- -->2<!-- -->周</span></div><div class="text-sm text-gray-900 mb-2">金融安全意识</div><div class="text-xs text-gray-600 mb-2">金融理财 • 网络通信</div><div class="w-full bg-gray-200 rounded-full h-1.5"><div class="bg-primary-600 h-1.5 rounded-full transition-all" style="width:60%"></div></div></div><div class="border border-gray-200 rounded-lg p-3"><div class="flex items-center gap-2 mb-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-5 h-5 text-gray-400"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg><span class="font-medium text-gray-900">第<!-- -->3<!-- -->周</span></div><div class="text-sm text-gray-900 mb-2">高级防范技巧</div><div class="text-xs text-gray-600 mb-2">教育培训 • 冒充权威</div><div class="w-full bg-gray-200 rounded-full h-1.5"><div class="bg-primary-600 h-1.5 rounded-full transition-all" style="width:0%"></div></div></div><div class="border border-gray-200 rounded-lg p-3"><div class="flex items-center gap-2 mb-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-5 h-5 text-gray-400"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg><span class="font-medium text-gray-900">第<!-- -->4<!-- -->周</span></div><div class="text-sm text-gray-900 mb-2">综合实战演练</div><div class="text-xs text-gray-600 mb-2">兼职副业 • 综合测试</div><div class="w-full bg-gray-200 rounded-full h-1.5"><div class="bg-primary-600 h-1.5 rounded-full transition-all" style="width:0%"></div></div></div></div></div><div class="card bg-gradient-to-r from-green-500 to-blue-600 text-white"><h3 class="text-lg font-bold mb-4">💡 学习建议</h3><div class="space-y-3 text-sm"><div class="flex items-start gap-2"><span>📚</span><span>建议从入门难度开始，循序渐进</span></div><div class="flex items-start gap-2"><span>🎯</span><span>专注完成一个分类再进入下一个</span></div><div class="flex items-start gap-2"><span>⏰</span><span>每天练习20-30分钟效果最佳</span></div><div class="flex items-start gap-2"><span>🔄</span><span>定期复习错题，巩固知识点</span></div></div></div><div class="card"><h3 class="text-lg font-bold text-gray-900 mb-4">学习成就</h3><div class="space-y-3"><div class="flex items-center gap-3 p-2 bg-yellow-50 rounded-lg"><span class="text-xl">🏆</span><div><div class="font-medium text-gray-900">求职防骗专家</div><div class="text-xs text-gray-600">完成求职防骗所有难度</div></div></div><div class="flex items-center gap-3 p-2 bg-green-50 rounded-lg"><span class="text-xl">🎯</span><div><div class="font-medium text-gray-900">精准射手</div><div class="text-xs text-gray-600">单次练习正确率90%+</div></div></div><div class="flex items-center gap-3 p-2 bg-blue-50 rounded-lg"><span class="text-xl">📚</span><div><div class="font-medium text-gray-900">勤奋学者</div><div class="text-xs text-gray-600">连续学习7天</div></div></div></div></div></div></div></div></div></div><script src="/_next/static/chunks/webpack-69bfb055b70a1563.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/css/97115f26cb754c8c.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L2\"\n"])</script><script>self.__next_f.push([1,"3:I[3728,[],\"\"]\n5:I[9928,[],\"\"]\n6:I[6954,[],\"\"]\n7:I[7264,[],\"\"]\n8:I[8326,[\"326\",\"static/chunks/326-cf5b0283528485c7.js\",\"160\",\"static/chunks/app/not-found-5c4df079dd57eb5a.js\"],\"\"]\na:I[8297,[],\"\"]\nb:I[8611,[\"326\",\"static/chunks/326-cf5b0283528485c7.js\",\"699\",\"static/chunks/app/features/category-practice/page-8aa028b980042304.js\"],\"\"]\n"])</script><script>self.__next_f.push([1,"2:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/97115f26cb754c8c.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L3\",null,{\"buildId\":\"-csu5AGVUyiuSzdvekIsf\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/features/category-practice/\",\"initialTree\":[\"\",{\"children\":[\"features\",{\"children\":[\"category-practice\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],\"initialHead\":[false,\"$L4\"],\"globalErrorComponent\":\"$5\",\"children\":[null,[\"$\",\"html\",null,{\"lang\":\"zh-CN\",\"children\":[\"$\",\"body\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"min-h-screen\",\"children\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4 sm:px-6 lg:px-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-md w-full text-center\",\"children\":[\"$\",\"div\",null,{\"className\":\"card\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mb-6\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-alert-triangle w-16 h-16 text-yellow-500 mx-auto mb-4\",\"children\":[[\"$\",\"path\",\"c3ski4\",{\"d\":\"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z\"}],[\"$\",\"path\",\"juzpu7\",{\"d\":\"M12 9v4\"}],[\"$\",\"path\",\"p32p05\",{\"d\":\"M12 17h.01\"}],\"$undefined\"]}],[\"$\",\"h1\",null,{\"className\":\"text-6xl font-bold text-gray-900 mb-2\",\"children\":\"404\"}],[\"$\",\"h2\",null,{\"className\":\"text-2xl font-semibold text-gray-700 mb-4\",\"children\":\"页面未找到\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600\",\"children\":\"抱歉，你访问的页面不存在。可能是链接错误或页面已被移除。\"}]]}],[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"btn-primary w-full flex items-center justify-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-home w-5 h-5\",\"children\":[[\"$\",\"path\",\"y5dka4\",{\"d\":\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"}],[\"$\",\"polyline\",\"e2us08\",{\"points\":\"9 22 9 12 15 12 15 22\"}],\"$undefined\"]}],\"返回首页\"]}],[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"btn-secondary w-full flex items-center justify-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-left w-5 h-5\",\"children\":[[\"$\",\"path\",\"1l729n\",{\"d\":\"m12 19-7-7 7-7\"}],[\"$\",\"path\",\"x3x0zl\",{\"d\":\"M19 12H5\"}],\"$undefined\"]}],\"返回上一页\"]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\"children\":[\"$\",\"p\",null,{\"className\":\"text-blue-800 text-sm\",\"children\":[\"💡 \",[\"$\",\"strong\",null,{\"children\":\"提示：\"}],\"如果你是通过链接访问的，请检查链接是否正确。 如果问题持续存在，请联系我们。\"]}]}]]}]}]}],\"notFoundStyles\":[],\"childProp\":{\"current\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"features\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"childProp\":{\"current\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"features\",\"children\",\"category-practice\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"childProp\":{\"current\":[\"$L9\",[\"$\",\"$La\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$b\",\"isStaticGeneration\":true}],null],\"segment\":\"__PAGE__\"},\"styles\":null}],\"segment\":\"category-practice\"},\"styles\":null}],\"segment\":\"features\"},\"styles\":null}]}]}]}],null]}]]\n"])</script><script>self.__next_f.push([1,"4:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"大学生防诈挑战游戏\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱\"}],[\"$\",\"meta\",\"4\",{\"name\":\"author\",\"content\":\"防诈挑战游戏团队\"}],[\"$\",\"meta\",\"5\",{\"name\":\"keywords\",\"content\":\"防诈骗,大学生,安全教育,答题游戏\"}]]\n9:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>