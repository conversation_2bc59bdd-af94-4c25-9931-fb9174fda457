<!DOCTYPE html><html lang="zh-CN"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/97115f26cb754c8c.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-69bfb055b70a1563.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-a0c76284e27c8afa.js" async="" crossorigin=""></script><script src="/_next/static/chunks/472-af003cc8e47f5b1f.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-52f18067e7ca02da.js" async="" crossorigin=""></script><script src="/_next/static/chunks/326-cf5b0283528485c7.js" async=""></script><script src="/_next/static/chunks/app/not-found-5c4df079dd57eb5a.js" async=""></script><script src="/_next/static/chunks/app/features/anonymous/page-0954444d38e3f7b0.js" async=""></script><title>大学生防诈挑战游戏</title><meta name="description" content="通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱"/><meta name="author" content="防诈挑战游戏团队"/><meta name="keywords" content="防诈骗,大学生,安全教育,答题游戏"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body><div class="min-h-screen"><div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100"><header class="bg-white shadow-sm border-b"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4"><div class="flex items-center gap-3"><a class="p-2 hover:bg-gray-100 rounded-lg transition-colors" href="/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left w-5 h-5 text-gray-600"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg></a><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield w-8 h-8 text-primary-600"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path></svg><div><h1 class="text-2xl font-bold text-gray-900">匿名参与</h1><p class="text-sm text-gray-600">保护隐私，安心学习</p></div></div></div></header><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="card mb-8 bg-gradient-to-r from-primary-500 to-blue-600 text-white"><div class="flex items-center gap-4 mb-6"><div class="p-3 bg-white/20 rounded-lg"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield w-8 h-8"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path></svg></div><div><h2 class="text-2xl font-bold mb-2">隐私优先的学习体验</h2><p class="text-primary-100">无需实名注册，通过邀请码+昵称的方式匿名参与，让你在保护隐私的同时安心学习防诈骗知识。</p></div></div><div class="grid grid-cols-2 md:grid-cols-4 gap-4"><div class="text-center"><div class="text-2xl font-bold">15,420</div><div class="text-sm text-primary-200">匿名用户</div></div><div class="text-center"><div class="text-2xl font-bold">892</div><div class="text-sm text-primary-200">今日活跃</div></div><div class="text-center"><div class="text-2xl font-bold">78.5<!-- -->%</div><div class="text-sm text-primary-200">平均成绩</div></div><div class="text-center"><div class="text-2xl font-bold">9.8<!-- -->/10</div><div class="text-sm text-primary-200">隐私评分</div></div></div></div><div class="mb-8"><h3 class="text-2xl font-bold text-gray-900 mb-6">隐私保护特性</h3><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div class="card hover:shadow-lg transition-shadow"><div class="flex items-start gap-4"><div class="text-3xl">🔑</div><div class="flex-1"><h4 class="text-lg font-semibold text-gray-900 mb-2">邀请码机制</h4><p class="text-gray-600 mb-3">通过邀请码参与，无需提供真实身份信息</p><p class="text-sm text-gray-500">每个邀请码由系统随机生成，不与任何个人信息关联，确保参与者身份完全匿名。</p></div></div></div><div class="card hover:shadow-lg transition-shadow"><div class="flex items-start gap-4"><div class="text-3xl">👤</div><div class="flex-1"><h4 class="text-lg font-semibold text-gray-900 mb-2">昵称系统</h4><p class="text-gray-600 mb-3">自定义昵称显示，保护真实姓名隐私</p><p class="text-sm text-gray-500">用户可以使用任意昵称参与游戏，系统不会要求验证真实姓名，充分保护个人隐私。</p></div></div></div><div class="card hover:shadow-lg transition-shadow"><div class="flex items-start gap-4"><div class="text-3xl">💾</div><div class="flex-1"><h4 class="text-lg font-semibold text-gray-900 mb-2">本地存储</h4><p class="text-gray-600 mb-3">数据仅保存在本地浏览器，不上传服务器</p><p class="text-sm text-gray-500">所有游戏数据、学习记录都保存在用户本地，随时可以清除，完全掌控自己的数据。</p></div></div></div><div class="card hover:shadow-lg transition-shadow"><div class="flex items-start gap-4"><div class="text-3xl">🚫</div><div class="flex-1"><h4 class="text-lg font-semibold text-gray-900 mb-2">无追踪设计</h4><p class="text-gray-600 mb-3">不收集设备信息，不进行用户行为追踪</p><p class="text-sm text-gray-500">系统设计遵循隐私优先原则，不收集IP地址、设备指纹等可识别信息。</p></div></div></div></div></div><div class="card mb-8"><h3 class="text-xl font-bold text-gray-900 mb-6">匿名参与流程</h3><div class="grid grid-cols-1 md:grid-cols-4 gap-6"><div class="text-center"><div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3"><span class="text-primary-600 font-bold">1</span></div><h4 class="font-semibold text-gray-900 mb-2">获取邀请码</h4><p class="text-sm text-gray-600">通过官方渠道获取邀请码</p></div><div class="text-center"><div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3"><span class="text-primary-600 font-bold">2</span></div><h4 class="font-semibold text-gray-900 mb-2">设置昵称</h4><p class="text-sm text-gray-600">选择一个喜欢的昵称</p></div><div class="text-center"><div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3"><span class="text-primary-600 font-bold">3</span></div><h4 class="font-semibold text-gray-900 mb-2">开始学习</h4><p class="text-sm text-gray-600">立即开始防诈骗学习</p></div><div class="text-center"><div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3"><span class="text-primary-600 font-bold">4</span></div><h4 class="font-semibold text-gray-900 mb-2">数据掌控</h4><p class="text-sm text-gray-600">随时导出或清除数据</p></div></div></div><div class="card mb-8"><div class="flex items-center justify-between mb-6"><h3 class="text-xl font-bold text-gray-900">用户反馈</h3><button class="flex items-center gap-2 text-primary-600 hover:text-primary-700"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-eye w-4 h-4"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path><circle cx="12" cy="12" r="3"></circle></svg>显示详情</button></div><div class="space-y-4"><div class="border border-gray-200 rounded-lg p-4"><div class="flex items-center justify-between mb-3"><div class="flex items-center gap-3"><div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-4 h-4 text-primary-600"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg></div><span class="font-medium text-gray-900">防诈小卫士</span></div><div class="flex items-center gap-2"><span class="text-sm text-gray-500">2小时前</span><span class="px-2 py-1 bg-success-100 text-success-700 text-xs rounded-full">95<!-- -->分</span></div></div><p class="text-gray-700">很喜欢这种匿名参与的方式，可以放心学习防诈骗知识，不用担心个人信息泄露。</p></div><div class="border border-gray-200 rounded-lg p-4"><div class="flex items-center justify-between mb-3"><div class="flex items-center gap-3"><div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-4 h-4 text-primary-600"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg></div><span class="font-medium text-gray-900">谨慎学习者</span></div><div class="flex items-center gap-2"><span class="text-sm text-gray-500">5小时前</span><span class="px-2 py-1 bg-success-100 text-success-700 text-xs rounded-full">88<!-- -->分</span></div></div><p class="text-gray-700">邀请码+昵称的设计很贴心，既能参与学习又保护隐私，这样的设计很人性化。</p></div><div class="border border-gray-200 rounded-lg p-4"><div class="flex items-center justify-between mb-3"><div class="flex items-center gap-3"><div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-4 h-4 text-primary-600"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg></div><span class="font-medium text-gray-900">安全意识强</span></div><div class="flex items-center gap-2"><span class="text-sm text-gray-500">1天前</span><span class="px-2 py-1 bg-success-100 text-success-700 text-xs rounded-full">92<!-- -->分</span></div></div><p class="text-gray-700">数据本地存储让我很放心，随时可以清除，完全掌控自己的学习数据。</p></div></div></div><div class="card bg-green-50 border-green-200"><div class="flex items-start gap-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-circle w-6 h-6 text-green-600 mt-1"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><path d="m9 11 3 3L22 4"></path></svg><div><h3 class="text-lg font-bold text-green-900 mb-2">我们的隐私承诺</h3><ul class="text-green-800 space-y-2"><li>• 绝不收集用户真实身份信息</li><li>• 所有数据仅保存在用户本地设备</li><li>• 不进行任何形式的用户行为追踪</li><li>• 用户可随时完全删除所有数据</li><li>• 开源透明，接受社区监督</li></ul></div></div></div><div class="text-center mt-8"><div class="flex flex-col sm:flex-row gap-4 justify-center"><a class="btn-primary" href="/demo-quiz/">立即体验匿名学习</a><a class="btn-secondary" href="/login/">注册匿名账户</a></div></div></div></div></div><script src="/_next/static/chunks/webpack-69bfb055b70a1563.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/css/97115f26cb754c8c.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L2\"\n"])</script><script>self.__next_f.push([1,"3:I[3728,[],\"\"]\n5:I[9928,[],\"\"]\n6:I[6954,[],\"\"]\n7:I[7264,[],\"\"]\n8:I[8326,[\"326\",\"static/chunks/326-cf5b0283528485c7.js\",\"160\",\"static/chunks/app/not-found-5c4df079dd57eb5a.js\"],\"\"]\na:I[8297,[],\"\"]\nb:I[1116,[\"326\",\"static/chunks/326-cf5b0283528485c7.js\",\"129\",\"static/chunks/app/features/anonymous/page-0954444d38e3f7b0.js\"],\"\"]\n"])</script><script>self.__next_f.push([1,"2:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/97115f26cb754c8c.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L3\",null,{\"buildId\":\"-csu5AGVUyiuSzdvekIsf\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/features/anonymous/\",\"initialTree\":[\"\",{\"children\":[\"features\",{\"children\":[\"anonymous\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],\"initialHead\":[false,\"$L4\"],\"globalErrorComponent\":\"$5\",\"children\":[null,[\"$\",\"html\",null,{\"lang\":\"zh-CN\",\"children\":[\"$\",\"body\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"min-h-screen\",\"children\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4 sm:px-6 lg:px-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-md w-full text-center\",\"children\":[\"$\",\"div\",null,{\"className\":\"card\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mb-6\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-alert-triangle w-16 h-16 text-yellow-500 mx-auto mb-4\",\"children\":[[\"$\",\"path\",\"c3ski4\",{\"d\":\"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z\"}],[\"$\",\"path\",\"juzpu7\",{\"d\":\"M12 9v4\"}],[\"$\",\"path\",\"p32p05\",{\"d\":\"M12 17h.01\"}],\"$undefined\"]}],[\"$\",\"h1\",null,{\"className\":\"text-6xl font-bold text-gray-900 mb-2\",\"children\":\"404\"}],[\"$\",\"h2\",null,{\"className\":\"text-2xl font-semibold text-gray-700 mb-4\",\"children\":\"页面未找到\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600\",\"children\":\"抱歉，你访问的页面不存在。可能是链接错误或页面已被移除。\"}]]}],[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"btn-primary w-full flex items-center justify-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-home w-5 h-5\",\"children\":[[\"$\",\"path\",\"y5dka4\",{\"d\":\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"}],[\"$\",\"polyline\",\"e2us08\",{\"points\":\"9 22 9 12 15 12 15 22\"}],\"$undefined\"]}],\"返回首页\"]}],[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"btn-secondary w-full flex items-center justify-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-left w-5 h-5\",\"children\":[[\"$\",\"path\",\"1l729n\",{\"d\":\"m12 19-7-7 7-7\"}],[\"$\",\"path\",\"x3x0zl\",{\"d\":\"M19 12H5\"}],\"$undefined\"]}],\"返回上一页\"]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\"children\":[\"$\",\"p\",null,{\"className\":\"text-blue-800 text-sm\",\"children\":[\"💡 \",[\"$\",\"strong\",null,{\"children\":\"提示：\"}],\"如果你是通过链接访问的，请检查链接是否正确。 如果问题持续存在，请联系我们。\"]}]}]]}]}]}],\"notFoundStyles\":[],\"childProp\":{\"current\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"features\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"childProp\":{\"current\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"features\",\"children\",\"anonymous\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"childProp\":{\"current\":[\"$L9\",[\"$\",\"$La\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$b\",\"isStaticGeneration\":true}],null],\"segment\":\"__PAGE__\"},\"styles\":null}],\"segment\":\"anonymous\"},\"styles\":null}],\"segment\":\"features\"},\"styles\":null}]}]}]}],null]}]]\n"])</script><script>self.__next_f.push([1,"4:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"大学生防诈挑战游戏\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱\"}],[\"$\",\"meta\",\"4\",{\"name\":\"author\",\"content\":\"防诈挑战游戏团队\"}],[\"$\",\"meta\",\"5\",{\"name\":\"keywords\",\"content\":\"防诈骗,大学生,安全教育,答题游戏\"}]]\n9:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>