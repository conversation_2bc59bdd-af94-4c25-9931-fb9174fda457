(()=>{var e={};e.id=7129,e.ids=[7129],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},6786:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>o,originalPathname:()=>m,pages:()=>x,routeModule:()=>h,tree:()=>d});var a=t(7096),r=t(6132),l=t(7284),i=t.n(l),c=t(2564),n={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);t.d(s,n);let d=["",{children:["features",{children:["anonymous",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,7058)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/anonymous/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],x=["/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/anonymous/page.tsx"],m="/features/anonymous/page",o={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/features/anonymous/page",pathname:"/features/anonymous",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3209:(e,s,t)=>{Promise.resolve().then(t.bind(t,9224))},9224:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>AnonymousFeaturePage});var a=t(784),r=t(9885),l=t(9803),i=t(4901),c=t(3250),n=t(8626),d=t(1672),x=t(2032),m=t(1440),o=t.n(m);function AnonymousFeaturePage(){let[e,s]=(0,r.useState)(!1),t={totalUsers:15420,activeToday:892,averageScore:78.5,privacyRating:9.8};return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[a.jsx("header",{className:"bg-white shadow-sm border-b",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(o(),{href:"/",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(l.Z,{className:"w-5 h-5 text-gray-600"})}),a.jsx(i.Z,{className:"w-8 h-8 text-primary-600"}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"匿名参与"}),a.jsx("p",{className:"text-sm text-gray-600",children:"保护隐私，安心学习"})]})]})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"card mb-8 bg-gradient-to-r from-primary-500 to-blue-600 text-white",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[a.jsx("div",{className:"p-3 bg-white/20 rounded-lg",children:a.jsx(i.Z,{className:"w-8 h-8"})}),(0,a.jsxs)("div",{children:[a.jsx("h2",{className:"text-2xl font-bold mb-2",children:"隐私优先的学习体验"}),a.jsx("p",{className:"text-primary-100",children:"无需实名注册，通过邀请码+昵称的方式匿名参与，让你在保护隐私的同时安心学习防诈骗知识。"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-2xl font-bold",children:t.totalUsers.toLocaleString()}),a.jsx("div",{className:"text-sm text-primary-200",children:"匿名用户"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-2xl font-bold",children:t.activeToday}),a.jsx("div",{className:"text-sm text-primary-200",children:"今日活跃"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[t.averageScore,"%"]}),a.jsx("div",{className:"text-sm text-primary-200",children:"平均成绩"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[t.privacyRating,"/10"]}),a.jsx("div",{className:"text-sm text-primary-200",children:"隐私评分"})]})]})]}),(0,a.jsxs)("div",{className:"mb-8",children:[a.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"隐私保护特性"}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[{title:"邀请码机制",description:"通过邀请码参与，无需提供真实身份信息",icon:"\uD83D\uDD11",details:"每个邀请码由系统随机生成，不与任何个人信息关联，确保参与者身份完全匿名。"},{title:"昵称系统",description:"自定义昵称显示，保护真实姓名隐私",icon:"\uD83D\uDC64",details:"用户可以使用任意昵称参与游戏，系统不会要求验证真实姓名，充分保护个人隐私。"},{title:"本地存储",description:"数据仅保存在本地浏览器，不上传服务器",icon:"\uD83D\uDCBE",details:"所有游戏数据、学习记录都保存在用户本地，随时可以清除，完全掌控自己的数据。"},{title:"无追踪设计",description:"不收集设备信息，不进行用户行为追踪",icon:"\uD83D\uDEAB",details:"系统设计遵循隐私优先原则，不收集IP地址、设备指纹等可识别信息。"}].map((e,s)=>a.jsx("div",{className:"card hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[a.jsx("div",{className:"text-3xl",children:e.icon}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.title}),a.jsx("p",{className:"text-gray-600 mb-3",children:e.description}),a.jsx("p",{className:"text-sm text-gray-500",children:e.details})]})]})},s))})]}),(0,a.jsxs)("div",{className:"card mb-8",children:[a.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-6",children:"匿名参与流程"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3",children:a.jsx("span",{className:"text-primary-600 font-bold",children:"1"})}),a.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"获取邀请码"}),a.jsx("p",{className:"text-sm text-gray-600",children:"通过官方渠道获取邀请码"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3",children:a.jsx("span",{className:"text-primary-600 font-bold",children:"2"})}),a.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"设置昵称"}),a.jsx("p",{className:"text-sm text-gray-600",children:"选择一个喜欢的昵称"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3",children:a.jsx("span",{className:"text-primary-600 font-bold",children:"3"})}),a.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"开始学习"}),a.jsx("p",{className:"text-sm text-gray-600",children:"立即开始防诈骗学习"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3",children:a.jsx("span",{className:"text-primary-600 font-bold",children:"4"})}),a.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"数据掌控"}),a.jsx("p",{className:"text-sm text-gray-600",children:"随时导出或清除数据"})]})]})]}),(0,a.jsxs)("div",{className:"card mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[a.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"用户反馈"}),(0,a.jsxs)("button",{onClick:()=>s(!e),className:"flex items-center gap-2 text-primary-600 hover:text-primary-700",children:[e?a.jsx(c.Z,{className:"w-4 h-4"}):a.jsx(n.Z,{className:"w-4 h-4"}),e?"隐藏详情":"显示详情"]})]}),a.jsx("div",{className:"space-y-4",children:[{nickname:"防诈小卫士",content:"很喜欢这种匿名参与的方式，可以放心学习防诈骗知识，不用担心个人信息泄露。",score:95,timeAgo:"2小时前"},{nickname:"谨慎学习者",content:"邀请码+昵称的设计很贴心，既能参与学习又保护隐私，这样的设计很人性化。",score:88,timeAgo:"5小时前"},{nickname:"安全意识强",content:"数据本地存储让我很放心，随时可以清除，完全掌控自己的学习数据。",score:92,timeAgo:"1天前"}].map((s,t)=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx("div",{className:"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center",children:a.jsx(d.Z,{className:"w-4 h-4 text-primary-600"})}),a.jsx("span",{className:"font-medium text-gray-900",children:s.nickname})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx("span",{className:"text-sm text-gray-500",children:s.timeAgo}),(0,a.jsxs)("span",{className:"px-2 py-1 bg-success-100 text-success-700 text-xs rounded-full",children:[s.score,"分"]})]})]}),a.jsx("p",{className:"text-gray-700",children:s.content}),e&&a.jsx("div",{className:"mt-3 pt-3 border-t border-gray-100",children:(0,a.jsxs)("div",{className:"flex items-center gap-4 text-xs text-gray-500",children:[a.jsx("span",{children:"✓ 已验证匿名用户"}),a.jsx("span",{children:"✓ 数据本地存储"}),a.jsx("span",{children:"✓ 隐私完全保护"})]})})]},t))})]}),a.jsx("div",{className:"card bg-green-50 border-green-200",children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[a.jsx(x.Z,{className:"w-6 h-6 text-green-600 mt-1"}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-bold text-green-900 mb-2",children:"我们的隐私承诺"}),(0,a.jsxs)("ul",{className:"text-green-800 space-y-2",children:[a.jsx("li",{children:"• 绝不收集用户真实身份信息"}),a.jsx("li",{children:"• 所有数据仅保存在用户本地设备"}),a.jsx("li",{children:"• 不进行任何形式的用户行为追踪"}),a.jsx("li",{children:"• 用户可随时完全删除所有数据"}),a.jsx("li",{children:"• 开源透明，接受社区监督"})]})]})]})}),a.jsx("div",{className:"text-center mt-8",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[a.jsx(o(),{href:"/demo-quiz",className:"btn-primary",children:"立即体验匿名学习"}),a.jsx(o(),{href:"/login",className:"btn-secondary",children:"注册匿名账户"})]})})]})]})}},7058:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>l,default:()=>n});var a=t(5153);let r=(0,a.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/anonymous/page.tsx`),{__esModule:l,$$typeof:i}=r,c=r.default,n=c}};var s=require("../../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[9678,5607,9863,2035],()=>__webpack_exec__(6786));module.exports=t})();