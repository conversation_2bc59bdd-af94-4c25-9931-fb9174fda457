(()=>{var e={};e.id=718,e.ids=[718],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},8131:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>o,pages:()=>x,routeModule:()=>u,tree:()=>d});var r=t(7096),a=t(6132),l=t(7284),i=t.n(l),n=t(2564),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["features",{children:["community-questions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2479)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/community-questions/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],x=["/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/community-questions/page.tsx"],o="/features/community-questions/page",m={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/features/community-questions/page",pathname:"/features/community-questions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6988:(e,s,t)=>{Promise.resolve().then(t.bind(t,7808))},7808:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>CommunityQuestionsPage});var r=t(784),a=t(9885),l=t(9803),i=t(1672),n=t(7),c=t(2385),d=t(8973),x=t(7932),o=t(1440),m=t.n(o);function CommunityQuestionsPage(){let[e,s]=(0,a.useState)("overview"),t={totalSubmissions:1247,approvedQuestions:892,activeContributors:156,averageQuality:8.7},getStatusColor=e=>{switch(e){case"approved":return"text-green-600 bg-green-100";case"reviewing":return"text-yellow-600 bg-yellow-100";default:return"text-gray-600 bg-gray-100"}},getStatusLabel=e=>{switch(e){case"approved":return"已通过";case"reviewing":return"审核中";default:return"未知"}};return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100",children:[r.jsx("header",{className:"bg-white shadow-sm border-b",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[r.jsx(m(),{href:"/",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:r.jsx(l.Z,{className:"w-5 h-5 text-gray-600"})}),r.jsx(i.Z,{className:"w-8 h-8 text-purple-600"}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"共建题库"}),r.jsx("p",{className:"text-sm text-gray-600",children:"用户共同建设，知识共享"})]})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"card mb-8 bg-gradient-to-r from-purple-500 to-indigo-600 text-white",children:[r.jsx("h2",{className:"text-2xl font-bold mb-6",children:"社区贡献统计"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"text-2xl font-bold",children:t.totalSubmissions}),r.jsx("div",{className:"text-sm text-purple-200",children:"总提交数"})]}),(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"text-2xl font-bold",children:t.approvedQuestions}),r.jsx("div",{className:"text-sm text-purple-200",children:"已通过题目"})]}),(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"text-2xl font-bold",children:t.activeContributors}),r.jsx("div",{className:"text-sm text-purple-200",children:"活跃贡献者"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[t.averageQuality,"/10"]}),r.jsx("div",{className:"text-sm text-purple-200",children:"平均质量分"})]})]})]}),r.jsx("div",{className:"card mb-8",children:r.jsx("div",{className:"flex flex-wrap gap-2",children:[{key:"overview",label:"概览",icon:n.Z},{key:"submit",label:"提交指南",icon:c.Z},{key:"contributors",label:"贡献者",icon:d.Z}].map(({key:t,label:a,icon:l})=>(0,r.jsxs)("button",{onClick:()=>s(t),className:`flex items-center gap-2 px-4 py-2 rounded-lg transition-all ${e===t?"bg-purple-100 text-purple-700 border-purple-300 border-2":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:[r.jsx(l,{className:"w-4 h-4"}),a]},t))})}),"overview"===e&&(0,r.jsxs)("div",{className:"card",children:[r.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-6",children:"最近提交的题目"}),r.jsx("div",{className:"space-y-4",children:[{id:"Q001",title:'虚假招聘要求交"体检费"的识别方法',author:"防诈小能手",category:"求职骗局",status:"approved",submittedAt:"2024-01-15",votes:23,quality:9.2},{id:"Q002",title:"租房遇到假房东的真实案例分析",author:"谨慎租客",category:"租房陷阱",status:"reviewing",submittedAt:"2024-01-14",votes:18,quality:8.8}].map(e=>(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("h4",{className:"font-semibold text-gray-900 mb-1",children:e.title}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,r.jsxs)("span",{children:["作者: ",e.author]}),(0,r.jsxs)("span",{children:["分类: ",e.category]}),(0,r.jsxs)("span",{children:["提交: ",e.submittedAt]})]})]}),r.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(e.status)}`,children:getStatusLabel(e.status)})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[r.jsx(x.Z,{className:"w-4 h-4 text-yellow-500"}),(0,r.jsxs)("span",{children:[e.quality,"/10"]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[r.jsx(n.Z,{className:"w-4 h-4 text-blue-500"}),(0,r.jsxs)("span",{children:[e.votes," 票"]})]})]})]},e.id))})]}),"submit"===e&&(0,r.jsxs)("div",{className:"card",children:[r.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-6",children:"题目提交指南"}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[r.jsx("h4",{className:"font-semibold text-gray-900 mb-3",children:"\uD83D\uDCCB 基于真实案例"}),r.jsx("p",{className:"text-gray-600 mb-3",children:"题目应该基于真实的诈骗案例"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[r.jsx("li",{children:"• 亲身经历的诈骗尝试"}),r.jsx("li",{children:"• 新闻报道的真实案例"}),r.jsx("li",{children:"• 朋友遭遇的诈骗事件"})]})]}),(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[r.jsx("h4",{className:"font-semibold text-gray-900 mb-3",children:"\uD83C\uDFAF 教育意义明确"}),r.jsx("p",{className:"text-gray-600 mb-3",children:"题目应该具有明确的教育价值"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[r.jsx("li",{children:"• 识别诈骗特征"}),r.jsx("li",{children:"• 防范措施说明"}),r.jsx("li",{children:"• 正确应对方法"})]})]})]}),r.jsx("div",{className:"text-center",children:(0,r.jsxs)(m(),{href:"/submit-question",className:"btn-primary text-lg px-8 py-4 inline-flex items-center gap-2",children:[r.jsx(c.Z,{className:"w-5 h-5"}),"立即提交题目"]})})]})]}),"contributors"===e&&(0,r.jsxs)("div",{className:"card",children:[r.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-6",children:"优秀贡献者"}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[{nickname:"防诈专家",contributions:45,approvalRate:96,totalVotes:1250,badge:"\uD83C\uDFC6",level:"专家级"},{nickname:"安全卫士",contributions:38,approvalRate:92,totalVotes:980,badge:"\uD83E\uDD47",level:"高级"}].map((e,s)=>(0,r.jsxs)("div",{className:"text-center p-6 border border-gray-200 rounded-lg",children:[r.jsx("div",{className:"text-4xl mb-3",children:e.badge}),r.jsx("div",{className:"font-bold text-lg text-gray-900 mb-1",children:e.nickname}),r.jsx("div",{className:"text-sm text-gray-600 mb-4",children:e.level}),(0,r.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-3",children:[r.jsx("div",{className:"text-lg font-bold text-blue-600",children:e.contributions}),r.jsx("div",{className:"text-blue-700",children:"贡献题目"})]}),(0,r.jsxs)("div",{className:"bg-green-50 rounded-lg p-3",children:[(0,r.jsxs)("div",{className:"text-lg font-bold text-green-600",children:[e.approvalRate,"%"]}),r.jsx("div",{className:"text-green-700",children:"通过率"})]})]})]},s))})]}),(0,r.jsxs)("div",{className:"card mt-8 bg-gradient-to-r from-green-500 to-blue-600 text-white",children:[r.jsx("h3",{className:"text-xl font-bold mb-4",children:"\uD83C\uDF81 贡献激励机制"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-semibold mb-2",children:"贡献值奖励"}),(0,r.jsxs)("ul",{className:"text-green-100 text-sm space-y-1",children:[r.jsx("li",{children:"• 提交题目: +1 贡献值"}),r.jsx("li",{children:"• 题目通过: +5 贡献值"}),r.jsx("li",{children:"• 获得点赞: +0.1 贡献值"})]})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-semibold mb-2",children:"等级系统"}),(0,r.jsxs)("ul",{className:"text-green-100 text-sm space-y-1",children:[r.jsx("li",{children:"• 新手: 0-10 贡献值"}),r.jsx("li",{children:"• 中级: 11-50 贡献值"}),r.jsx("li",{children:"• 高级: 51-100 贡献值"}),r.jsx("li",{children:"• 专家: 100+ 贡献值"})]})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-semibold mb-2",children:"特殊权益"}),(0,r.jsxs)("ul",{className:"text-green-100 text-sm space-y-1",children:[r.jsx("li",{children:"• 优先审核通道"}),r.jsx("li",{children:"• 专属贡献者标识"}),r.jsx("li",{children:"• 参与题库管理"}),r.jsx("li",{children:"• 获得纪念证书"})]})]})]})]})]})]})}},2479:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>l,default:()=>c});var r=t(5153);let a=(0,r.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/community-questions/page.tsx`),{__esModule:l,$$typeof:i}=a,n=a.default,c=n}};var s=require("../../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[9678,5607,8972,4440,2035],()=>__webpack_exec__(8131));module.exports=t})();