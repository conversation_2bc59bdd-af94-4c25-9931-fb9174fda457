(()=>{var e={};e.id=3315,e.ids=[3315],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},6964:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>g,tree:()=>d});var a=t(7096),r=t(6132),l=t(7284),i=t.n(l),c=t(2564),n={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);t.d(s,n);let d=["",{children:["features",{children:["category-learning",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,6487)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/category-learning/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],o=["/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/category-learning/page.tsx"],x="/features/category-learning/page",m={require:t,loadChunk:()=>Promise.resolve()},g=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/features/category-learning/page",pathname:"/features/category-learning",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9251:(e,s,t)=>{Promise.resolve().then(t.bind(t,4039))},4039:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>CategoryLearningPage});var a=t(784),r=t(9885),l=t(9803),i=t(8937),c=t(696),n=t(7),d=t(8973),o=t(1440),x=t.n(o);function CategoryLearningPage(){let[e,s]=(0,r.useState)("job-fraud"),t={"job-fraud":{title:"求职防骗",icon:"\uD83D\uDCBC",color:"blue",description:"识别虚假招聘、培训费诈骗等求职陷阱",questionCount:25,completionRate:78,averageScore:85,difficulty:"入门",learningPath:["基础概念：什么是求职诈骗","常见套路：培训费、保证金陷阱","识别技巧：如何辨别虚假招聘","防范措施：正确的求职方式","实战演练：真实案例分析"],recentUpdates:['新增"AI面试诈骗"专题',"更新最新求职诈骗案例","优化题目难度梯度"]},"rental-scam":{title:"租房安全",icon:"\uD83C\uDFE0",color:"green",description:"防范假房东、押金诈骗等租房风险",questionCount:18,completionRate:65,averageScore:79,difficulty:"入门",learningPath:["租房基础：正规租房流程","风险识别：假房东特征","合同要点：租房合同注意事项","资金安全：押金支付技巧","维权方法：遇到问题如何处理"],recentUpdates:['新增"长租公寓陷阱"内容',"更新租房合同模板","增加地区性案例分析"]},"loan-trap":{title:"金融理财",icon:"\uD83D\uDCB0",color:"yellow",description:"识别网贷陷阱、投资诈骗等金融风险",questionCount:32,completionRate:52,averageScore:72,difficulty:"进阶",learningPath:["金融基础：正规金融机构识别","网贷风险：套路贷识别技巧","投资陷阱：虚假理财产品","利率计算：真实成本分析","维权途径：金融纠纷处理"],recentUpdates:['新增"虚拟货币诈骗"专题',"更新最新金融监管政策","增加计算器工具"]},"training-scam":{title:"教育培训",icon:"\uD83C\uDF93",color:"purple",description:"防范培训诈骗、学历造假等教育陷阱",questionCount:15,completionRate:43,averageScore:68,difficulty:"进阶",learningPath:["培训机构：正规资质识别",'承诺陷阱："包过包就业"风险',"费用合理性：培训成本分析","合同条款：培训协议要点","维权指南：培训纠纷处理"],recentUpdates:['新增"在线教育诈骗"内容',"更新培训机构查询方法","增加退费维权指南"]}},o=t[e],m={totalLearners:12580,completedToday:456,averageImprovement:23.5,satisfactionRate:94.2};return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-blue-100",children:[a.jsx("header",{className:"bg-white shadow-sm border-b",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(x(),{href:"/",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(l.Z,{className:"w-5 h-5 text-gray-600"})}),a.jsx(i.Z,{className:"w-8 h-8 text-success-600"}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"分类学习"}),a.jsx("p",{className:"text-sm text-gray-600",children:"针对性学习，高效提升"})]})]})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"card mb-8 bg-gradient-to-r from-success-500 to-green-600 text-white",children:[a.jsx("h2",{className:"text-2xl font-bold mb-6",children:"分类学习数据概览"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-2xl font-bold",children:m.totalLearners.toLocaleString()}),a.jsx("div",{className:"text-sm text-green-200",children:"总学习人数"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-2xl font-bold",children:m.completedToday}),a.jsx("div",{className:"text-sm text-green-200",children:"今日完成"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[m.averageImprovement,"%"]}),a.jsx("div",{className:"text-sm text-green-200",children:"平均提升"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[m.satisfactionRate,"%"]}),a.jsx("div",{className:"text-sm text-green-200",children:"满意度"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[a.jsx("div",{className:"lg:col-span-1",children:(0,a.jsxs)("div",{className:"card sticky top-8",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"学习分类"}),a.jsx("div",{className:"space-y-3",children:Object.entries(t).map(([t,r])=>a.jsx("button",{onClick:()=>s(t),className:`w-full p-3 rounded-lg text-left transition-all ${e===t?"bg-primary-100 border-primary-300 border-2":"bg-gray-50 hover:bg-gray-100 border-2 border-transparent"}`,children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx("span",{className:"text-2xl",children:r.icon}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-gray-900",children:r.title}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[r.questionCount," 道题目"]})]})]})},t))})]})}),(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)("div",{className:"card",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[a.jsx("div",{className:"text-4xl",children:o.icon}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-2xl font-bold text-gray-900",children:o.title}),a.jsx("p",{className:"text-gray-600",children:o.description})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"text-center p-3 bg-blue-50 rounded-lg",children:[a.jsx("div",{className:"text-lg font-bold text-blue-600",children:o.questionCount}),a.jsx("div",{className:"text-sm text-gray-600",children:"题目数量"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-green-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-lg font-bold text-green-600",children:[o.completionRate,"%"]}),a.jsx("div",{className:"text-sm text-gray-600",children:"完成率"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-yellow-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-lg font-bold text-yellow-600",children:[o.averageScore,"%"]}),a.jsx("div",{className:"text-sm text-gray-600",children:"平均分"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-purple-50 rounded-lg",children:[a.jsx("div",{className:"text-lg font-bold text-purple-600",children:o.difficulty}),a.jsx("div",{className:"text-sm text-gray-600",children:"难度等级"})]})]}),(0,a.jsxs)(x(),{href:`/category-practice?category=${e}`,className:"btn-primary w-full flex items-center justify-center gap-2",children:[a.jsx(c.Z,{className:"w-5 h-5"}),"开始学习 ",o.title]})]}),(0,a.jsxs)("div",{className:"card",children:[a.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:"学习路径"}),a.jsx("div",{className:"space-y-3",children:o.learningPath.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[a.jsx("div",{className:"flex-shrink-0 w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center text-primary-600 text-sm font-bold",children:s+1}),a.jsx("div",{className:"text-gray-700",children:e})]},s))})]}),(0,a.jsxs)("div",{className:"card",children:[a.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:"最新更新"}),a.jsx("div",{className:"space-y-3",children:o.recentUpdates.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-blue-50 rounded-lg",children:[a.jsx(n.Z,{className:"w-5 h-5 text-blue-600"}),a.jsx("span",{className:"text-blue-800",children:e})]},s))})]})]})]}),(0,a.jsxs)("div",{className:"card mt-8",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-6",children:[a.jsx(d.Z,{className:"w-6 h-6 text-yellow-500"}),a.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"分类学习排行榜"})]}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[{nickname:"学习达人",score:98,category:"求职防骗",badge:"\uD83C\uDFC6"},{nickname:"防诈专家",score:96,category:"金融理财",badge:"\uD83E\uDD47"},{nickname:"安全卫士",score:95,category:"租房安全",badge:"\uD83E\uDD48"},{nickname:"谨慎学者",score:94,category:"教育培训",badge:"\uD83E\uDD49"}].map((e,s)=>(0,a.jsxs)("div",{className:"p-4 border border-gray-200 rounded-lg text-center",children:[a.jsx("div",{className:"text-2xl mb-2",children:e.badge}),a.jsx("div",{className:"font-semibold text-gray-900",children:e.nickname}),a.jsx("div",{className:"text-sm text-gray-600 mb-2",children:e.category}),(0,a.jsxs)("div",{className:"text-lg font-bold text-primary-600",children:[e.score,"分"]})]},s))})]}),(0,a.jsxs)("div",{className:"card mt-8 bg-gradient-to-r from-purple-500 to-indigo-600 text-white",children:[a.jsx("h3",{className:"text-xl font-bold mb-4",children:"\uD83D\uDCA1 个性化学习建议"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-semibold mb-2",children:"新手推荐"}),(0,a.jsxs)("ul",{className:"text-purple-100 text-sm space-y-1",children:[a.jsx("li",{children:'• 从"求职防骗"开始，建立基础认知'}),a.jsx("li",{children:"• 每天学习1-2个分类，循序渐进"}),a.jsx("li",{children:"• 重点关注与自己相关的场景"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-semibold mb-2",children:"进阶技巧"}),(0,a.jsxs)("ul",{className:"text-purple-100 text-sm space-y-1",children:[a.jsx("li",{children:"• 交叉学习多个分类，形成知识网络"}),a.jsx("li",{children:"• 定期复习错题，巩固薄弱环节"}),a.jsx("li",{children:"• 结合实际案例，提升实战能力"})]})]})]})]})]})]})}},6487:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>l,default:()=>n});var a=t(5153);let r=(0,a.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/features/category-learning/page.tsx`),{__esModule:l,$$typeof:i}=r,c=r.default,n=c}};var s=require("../../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[9678,5607,8972,2737,2035],()=>__webpack_exec__(6964));module.exports=t})();