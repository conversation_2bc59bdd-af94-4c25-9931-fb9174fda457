"use strict";(()=>{var e={};e.id=9165,e.ids=[9165],e.modules={2934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{e.exports=require("url")},5242:(e,t,o)=>{o.r(t),o.d(t,{GlobalError:()=>a.a,__next_app__:()=>c,originalPathname:()=>u,pages:()=>l,routeModule:()=>x,tree:()=>d});var r=o(7096),n=o(6132),s=o(7284),a=o.n(s),i=o(2564),p={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>i[e]);o.d(t,p);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(o.bind(o,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}]},{layout:[()=>Promise.resolve().then(o.bind(o,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(o.bind(o,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],l=[],u="/_not-found",c={require:o,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/_not-found",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var t=require("../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),o=t.X(0,[9678,2035],()=>__webpack_exec__(5242));module.exports=o})();