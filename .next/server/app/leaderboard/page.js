(()=>{var e={};e.id=173,e.ids=[173],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},9111:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d});var s=a(7096),r=a(6132),l=a(7284),n=a.n(l),i=a(2564),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);a.d(t,c);let d=["",{children:["leaderboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,7635)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/leaderboard/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],o=["/home/<USER>/Desktop/vscode/防诈挑战游戏/app/leaderboard/page.tsx"],x="/leaderboard/page",m={require:a,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/leaderboard/page",pathname:"/leaderboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6295:(e,t,a)=>{Promise.resolve().then(a.bind(a,1732))},1732:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>LeaderboardPage});var s=a(784),r=a(9885),l=a(4402),n=a(405),i=a(9803),c=a(1438),d=a(2029),o=a(5441),x=a(696),m=a(1440),h=a.n(m),g=a(3165);let p=[{rank:1,nickname:"防诈达人",score:25,timeUsed:180,category:"job-fraud",date:"2024-01-15"},{rank:2,nickname:"安全小卫士",score:23,timeUsed:165,category:"telecom-fraud",date:"2024-01-15"},{rank:3,nickname:"智慧学子",score:22,timeUsed:190,category:"loan-trap",date:"2024-01-14"},{rank:4,nickname:"警觉青年",score:20,timeUsed:145,category:"rental-scam",date:"2024-01-14"},{rank:5,nickname:"反诈先锋",score:19,timeUsed:200,category:"training-scam",date:"2024-01-13"},{rank:6,nickname:"谨慎同学",score:18,timeUsed:175,category:"part-time-scam",date:"2024-01-13"},{rank:7,nickname:"明智选择",score:17,timeUsed:160,category:"fake-authority",date:"2024-01-12"},{rank:8,nickname:"理性思考",score:16,timeUsed:185,category:"job-fraud",date:"2024-01-12"},{rank:9,nickname:"细心观察",score:15,timeUsed:170,category:"telecom-fraud",date:"2024-01-11"},{rank:10,nickname:"冷静分析",score:14,timeUsed:195,category:"loan-trap",date:"2024-01-11"}];function LeaderboardPage(){let[e,t]=(0,r.useState)("all"),[a,m]=(0,r.useState)("all"),[u,j]=(0,r.useState)(p),b=u.filter(t=>{if("all"!==e&&t.category!==e)return!1;let s=new Date(t.date),r=new Date;switch(a){case"today":return s.toDateString()===r.toDateString();case"week":let l=new Date(r.getTime()-6048e5);return s>=l;case"month":let n=new Date(r.getTime()-2592e6);return s>=n;default:return!0}}),formatTime=e=>`${Math.floor(e/60)}:${(e%60).toString().padStart(2,"0")}`,getRankIcon=e=>{switch(e){case 1:return s.jsx(l.Z,{className:"w-6 h-6 text-yellow-500"});case 2:return s.jsx(n.Z,{className:"w-6 h-6 text-gray-400"});case 3:return s.jsx(n.Z,{className:"w-6 h-6 text-amber-600"});default:return s.jsx("div",{className:"w-6 h-6 flex items-center justify-center text-gray-500 font-bold",children:e})}};return(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[s.jsx("header",{className:"bg-white shadow-sm border-b",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:s.jsx("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[s.jsx(h(),{href:"/dashboard",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:s.jsx(i.Z,{className:"w-5 h-5 text-gray-600"})}),s.jsx(c.Z,{className:"w-8 h-8 text-yellow-500"}),(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"排行榜"}),s.jsx("p",{className:"text-sm text-gray-600",children:"查看挑战高手排名"})]})]})})})}),(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[s.jsx("div",{className:"card mb-8",children:(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[s.jsx(d.Z,{className:"w-4 h-4 inline mr-1"}),"诈骗类型"]}),(0,s.jsxs)("select",{value:e,onChange:e=>t(e.target.value),className:"input-field",children:[s.jsx("option",{value:"all",children:"全部类型"}),Object.entries(g.H).map(([e,t])=>s.jsx("option",{value:e,children:t},e))]})]}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[s.jsx(o.Z,{className:"w-4 h-4 inline mr-1"}),"时间范围"]}),(0,s.jsxs)("select",{value:a,onChange:e=>m(e.target.value),className:"input-field",children:[s.jsx("option",{value:"all",children:"全部时间"}),s.jsx("option",{value:"today",children:"今日"}),s.jsx("option",{value:"week",children:"本周"}),s.jsx("option",{value:"month",children:"本月"})]})]})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,s.jsxs)("div",{className:"card text-center",children:[s.jsx("div",{className:"text-3xl font-bold text-primary-600 mb-2",children:b.length}),s.jsx("div",{className:"text-gray-600",children:"参与用户"})]}),(0,s.jsxs)("div",{className:"card text-center",children:[s.jsx("div",{className:"text-3xl font-bold text-success-600 mb-2",children:b.length>0?Math.max(...b.map(e=>e.score)):0}),s.jsx("div",{className:"text-gray-600",children:"最高分数"})]}),(0,s.jsxs)("div",{className:"card text-center",children:[s.jsx("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:b.length>0?formatTime(Math.min(...b.map(e=>e.timeUsed))):"0:00"}),s.jsx("div",{className:"text-gray-600",children:"最快用时"})]})]}),(0,s.jsxs)("div",{className:"card",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-6",children:[s.jsx(c.Z,{className:"w-6 h-6 text-yellow-500"}),(0,s.jsxs)("h2",{className:"text-xl font-bold text-gray-900",children:["all"===e?"全部":g.H[e]," 排行榜"]})]}),0===b.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[s.jsx(x.Z,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),s.jsx("p",{className:"text-gray-500",children:"暂无符合条件的排行数据"})]}):s.jsx("div",{className:"space-y-3",children:b.map((e,t)=>(0,s.jsxs)("div",{className:`flex items-center justify-between p-4 rounded-lg border-2 transition-all ${e.rank<=3?"border-yellow-200 bg-yellow-50":"border-gray-200 bg-gray-50"}`,children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[s.jsx("div",{className:"flex-shrink-0",children:getRankIcon(e.rank)}),(0,s.jsxs)("div",{children:[s.jsx("div",{className:"font-semibold text-gray-900",children:e.nickname}),(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[e.category&&g.H[e.category]," • ",e.date]})]})]}),s.jsx("div",{className:"text-right",children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"text-lg font-bold text-primary-600",children:e.score}),s.jsx("div",{className:"text-xs text-gray-500",children:"题数"})]}),(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"text-lg font-bold text-blue-600",children:formatTime(e.timeUsed)}),s.jsx("div",{className:"text-xs text-gray-500",children:"用时"})]})]})})]},t))})]}),s.jsx("div",{className:"text-center mt-8",children:(0,s.jsxs)("div",{className:"card bg-gradient-to-r from-primary-500 to-purple-600 text-white",children:[s.jsx("h3",{className:"text-xl font-bold mb-2",children:"想要上榜吗？"}),s.jsx("p",{className:"text-primary-100 mb-4",children:"参与每日挑战，提升你的防诈骗能力，争取更好的排名！"}),s.jsx(h(),{href:"/challenge",className:"btn-primary bg-white text-primary-600 hover:bg-gray-100",children:"开始挑战"})]})})]})]})}},3165:(e,t,a)=>{"use strict";a.d(t,{H:()=>s});let s={"job-fraud":"求职骗局","rental-scam":"租房陷阱","loan-trap":"网贷陷阱","training-scam":"培训诈骗","telecom-fraud":"电信诈骗","fake-authority":"冒充公检法","part-time-scam":"兼职诈骗"}},7635:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>n,__esModule:()=>l,default:()=>c});var s=a(5153);let r=(0,s.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/leaderboard/page.tsx`),{__esModule:l,$$typeof:n}=r,i=r.default,c=i}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),a=t.X(0,[9678,5607,2677,3740,2035],()=>__webpack_exec__(9111));module.exports=a})();