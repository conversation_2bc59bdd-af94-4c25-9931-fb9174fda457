(()=>{var e={};e.id=2463,e.ids=[2463],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},3640:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>u,tree:()=>c});var i=t(7096),r=t(6132),a=t(7284),l=t.n(a),n=t(2564),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c=["",{children:["multimedia-content",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,9634)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/multimedia-content/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],o=["/home/<USER>/Desktop/vscode/防诈挑战游戏/app/multimedia-content/page.tsx"],x="/multimedia-content/page",m={require:t,loadChunk:()=>Promise.resolve()},u=new i.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/multimedia-content/page",pathname:"/multimedia-content",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9108:(e,s,t)=>{Promise.resolve().then(t.bind(t,1195))},1195:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>MultimediaContentPage});var i=t(784),r=t(9885),a=t(3104),l=t(1516),n=t(976),d=t(132),c=t(9803),o=t(8937),x=t(7552),m=t(1440),u=t.n(m);function MultimediaContentPage(){let[e,s]=(0,r.useState)("video"),t={video:{title:"视频教学",icon:i.jsx(a.Z,{className:"w-6 h-6"}),description:"生动的视频案例和专家讲解",content:[{title:"真实案例重现：大学生求职诈骗",duration:"8:32",views:"12.4K",description:"通过情景再现的方式，展示求职诈骗的完整过程和识别要点",thumbnail:"\uD83C\uDFAD",tags:["求职骗局","案例重现","专家解析"]},{title:"防诈骗专家访谈：如何识别网贷陷阱",duration:"15:20",views:"8.7K",description:"邀请反诈专家深度解析网贷诈骗的常见套路和防范方法",thumbnail:"\uD83D\uDC68‍\uD83D\uDCBC",tags:["专家访谈","网贷陷阱","深度解析"]},{title:"动画科普：电信诈骗的演变历程",duration:"6:45",views:"15.2K",description:"用动画形式展示电信诈骗从传统到现代的发展变化",thumbnail:"\uD83C\uDFA8",tags:["动画科普","电信诈骗","历史演变"]}]},audio:{title:"音频内容",icon:i.jsx(l.Z,{className:"w-6 h-6"}),description:"便于随时收听的音频课程和播客",content:[{title:"防诈骗每日一听：租房安全指南",duration:"12:15",views:"6.8K",description:"每日更新的防诈骗小贴士，今日主题：租房安全",thumbnail:"\uD83C\uDFE0",tags:["每日更新","租房安全","实用指南"]},{title:"真实受害者访谈：我是如何被培训诈骗的",duration:"25:30",views:"9.3K",description:"受害者亲述被骗经历，分享血泪教训",thumbnail:"\uD83C\uDF99️",tags:["真实案例","受害者访谈","培训诈骗"]},{title:"法律专家解读：诈骗案件的法律后果",duration:"18:45",views:"4.2K",description:"法律专家详解诈骗相关法律条文和判决案例",thumbnail:"⚖️",tags:["法律解读","专家观点","案例分析"]}]},interactive:{title:"互动体验",icon:i.jsx(n.Z,{className:"w-6 h-6"}),description:"沉浸式的互动学习体验",content:[{title:"VR 虚拟现实：身临其境体验诈骗现场",duration:"体验时长 10-15分钟",views:"3.1K",description:"通过 VR 技术让用户身临其境地体验各种诈骗场景",thumbnail:"\uD83E\uDD7D",tags:["VR体验","沉浸式","场景模拟"]},{title:"角色扮演：我是反诈警察",duration:"互动游戏",views:"7.5K",description:"扮演反诈警察，处理各种诈骗案件，学习专业识别技巧",thumbnail:"\uD83D\uDC6E‍♂️",tags:["角色扮演","游戏化","专业技能"]},{title:"模拟对话：与诈骗分子的智斗",duration:"对话练习",views:"5.9K",description:"模拟与诈骗分子的对话，练习应对技巧和话术",thumbnail:"\uD83D\uDCAC",tags:["对话模拟","应对技巧","实战练习"]}]},live:{title:"直播讲座",icon:i.jsx(d.Z,{className:"w-6 h-6"}),description:"实时互动的专家讲座和答疑",content:[{title:"本周直播：大学生防诈骗专题讲座",duration:"每周三 19:00",views:"预约 2.3K",description:"邀请反诈专家和高校老师，针对大学生群体的防诈骗教育",thumbnail:"\uD83D\uDCFA",tags:["定期直播","专家讲座","互动答疑"]},{title:"案例分析直播：最新诈骗手段解析",duration:"每月第二个周五",views:"预约 1.8K",description:"分析当月最新出现的诈骗手段和防范方法",thumbnail:"\uD83D\uDD0D",tags:["案例分析","最新动态","实时更新"]},{title:"互动问答：你问我答防诈骗",duration:"不定期",views:"关注 4.7K",description:"用户提问，专家实时解答各种防诈骗相关问题",thumbnail:"❓",tags:["互动问答","实时解答","用户参与"]}]}},m=t[e];return(0,i.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-blue-100",children:[i.jsx("header",{className:"bg-white shadow-sm border-b",children:i.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[i.jsx(u(),{href:"/dashboard",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:i.jsx(c.Z,{className:"w-5 h-5 text-gray-600"})}),i.jsx(o.Z,{className:"w-8 h-8 text-green-600"}),(0,i.jsxs)("div",{children:[i.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"多媒体内容中心"}),i.jsx("p",{className:"text-sm text-gray-600",children:"丰富的视听学习资源"})]})]})})}),(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,i.jsxs)("div",{className:"card mb-8",children:[i.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"内容分类"}),i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:Object.entries(t).map(([t,r])=>(0,i.jsxs)("button",{onClick:()=>s(t),className:`p-4 rounded-lg border-2 text-left transition-all ${e===t?"border-green-500 bg-green-50":"border-gray-200 hover:border-green-300"}`,children:[(0,i.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[i.jsx("div",{className:`p-2 rounded-lg ${e===t?"bg-green-100":"bg-gray-100"}`,children:r.icon}),i.jsx("h3",{className:"font-semibold text-gray-900",children:r.title})]}),i.jsx("p",{className:"text-sm text-gray-600",children:r.description})]},t))})]}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[i.jsx("div",{className:"p-3 bg-green-100 rounded-lg",children:m.icon}),(0,i.jsxs)("div",{children:[i.jsx("h3",{className:"text-2xl font-bold text-gray-900",children:m.title}),i.jsx("p",{className:"text-gray-600",children:m.description})]})]}),i.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:m.content.map((e,s)=>(0,i.jsxs)("div",{className:"card hover:shadow-lg transition-shadow",children:[(0,i.jsxs)("div",{className:"relative bg-gray-100 rounded-lg mb-4 h-48 flex items-center justify-center",children:[i.jsx("div",{className:"text-6xl",children:e.thumbnail}),i.jsx("div",{className:"absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity",children:i.jsx("button",{className:"bg-white/90 rounded-full p-3 hover:bg-white transition-colors",children:i.jsx(x.Z,{className:"w-6 h-6 text-gray-800"})})}),i.jsx("div",{className:"absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded",children:e.duration})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[i.jsx("h4",{className:"font-semibold text-gray-900 line-clamp-2",children:e.title}),i.jsx("p",{className:"text-sm text-gray-600 line-clamp-3",children:e.description}),i.jsx("div",{className:"flex flex-wrap gap-2",children:e.tags.map((e,s)=>i.jsx("span",{className:"px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full",children:e},s))}),(0,i.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500",children:[(0,i.jsxs)("span",{children:[e.views," 观看"]}),i.jsx("button",{className:"text-green-600 hover:text-green-700 font-medium",children:"立即观看"})]})]})]},s))})]}),(0,i.jsxs)("div",{className:"card mt-8 bg-gradient-to-r from-green-500 to-blue-600 text-white",children:[i.jsx("h3",{className:"text-xl font-bold mb-4",children:"\uD83C\uDFAC 多媒体学习的优势"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[i.jsx("h4",{className:"font-semibold mb-2",children:"视听结合，印象深刻"}),i.jsx("p",{className:"text-green-100 text-sm",children:"通过视频、音频等多种形式，让防诈骗知识更加生动有趣，提高学习效果。"})]}),(0,i.jsxs)("div",{children:[i.jsx("h4",{className:"font-semibold mb-2",children:"真实案例，警示教育"}),i.jsx("p",{className:"text-green-100 text-sm",children:"基于真实案例制作内容，让用户深刻理解诈骗的危害和防范的重要性。"})]}),(0,i.jsxs)("div",{children:[i.jsx("h4",{className:"font-semibold mb-2",children:"专家指导，权威可信"}),i.jsx("p",{className:"text-green-100 text-sm",children:"邀请反诈专家、法律专家等权威人士，确保内容的专业性和准确性。"})]}),(0,i.jsxs)("div",{children:[i.jsx("h4",{className:"font-semibold mb-2",children:"互动体验，身临其境"}),i.jsx("p",{className:"text-green-100 text-sm",children:"通过 VR、角色扮演等互动方式，让用户在实践中掌握防诈骗技能。"})]})]})]}),(0,i.jsxs)("div",{className:"card mt-6 text-center",children:[i.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"\uD83D\uDCCB 开发计划"}),i.jsx("p",{className:"text-gray-600 mb-6",children:"多媒体内容正在制作中，我们正在与专家团队合作，打造高质量的教育内容。"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,i.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[i.jsx("div",{className:"text-2xl font-bold text-green-600",children:"15+"}),i.jsx("div",{className:"text-sm text-gray-600",children:"视频制作中"})]}),(0,i.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[i.jsx("div",{className:"text-2xl font-bold text-blue-600",children:"8+"}),i.jsx("div",{className:"text-sm text-gray-600",children:"音频录制中"})]}),(0,i.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[i.jsx("div",{className:"text-2xl font-bold text-purple-600",children:"5+"}),i.jsx("div",{className:"text-sm text-gray-600",children:"互动体验开发中"})]}),(0,i.jsxs)("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4",children:[i.jsx("div",{className:"text-2xl font-bold text-orange-600",children:"2+"}),i.jsx("div",{className:"text-sm text-gray-600",children:"直播栏目筹备中"})]})]}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[i.jsx("button",{className:"btn-primary",disabled:!0,children:"订阅更新通知 (开发中)"}),i.jsx(u(),{href:"/dashboard",className:"btn-secondary",children:"返回主页"})]})]})]})]})}},9634:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>a,default:()=>d});var i=t(5153);let r=(0,i.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/multimedia-content/page.tsx`),{__esModule:a,$$typeof:l}=r,n=r.default,d=n}};var s=require("../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[9678,5607,9304,2035],()=>__webpack_exec__(3640));module.exports=t})();