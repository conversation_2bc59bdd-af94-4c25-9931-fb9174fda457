(()=>{var e={};e.id=8470,e.ids=[8470],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},749:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>n});var r=s(7096),l=s(6132),a=s(7284),i=s.n(a),c=s(2564),o={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>c[e]);s.d(t,o);let n=["",{children:["category-practice",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,3292)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/category-practice/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],d=["/home/<USER>/Desktop/vscode/防诈挑战游戏/app/category-practice/page.tsx"],x="/category-practice/page",m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/category-practice/page",pathname:"/category-practice",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},96:(e,t,s)=>{Promise.resolve().then(s.bind(s,4233))},4233:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>CategoryPracticePage});var r=s(784),l=s(9885),a=s(9803),i=s(8937),c=s(1438),o=s(7552),n=s(5441),d=s(1440),x=s.n(d),m=s(7114);let u=[{category:"job-fraud",title:"求职防骗",description:"识别虚假招聘、培训费诈骗等求职陷阱",icon:"\uD83D\uDCBC",difficulty:"beginner",questionCount:15,completedCount:8,bestScore:87,color:"blue"},{category:"rental-scam",title:"租房安全",description:"防范假房东、押金诈骗等租房风险",icon:"\uD83C\uDFE0",difficulty:"beginner",questionCount:12,completedCount:5,bestScore:75,color:"green"},{category:"loan-trap",title:"金融理财",description:"识别网贷陷阱、投资诈骗等金融风险",icon:"\uD83D\uDCB0",difficulty:"intermediate",questionCount:18,completedCount:3,bestScore:65,color:"yellow"},{category:"training-scam",title:"教育培训",description:"防范培训诈骗、学历造假等教育陷阱",icon:"\uD83C\uDF93",difficulty:"intermediate",questionCount:10,completedCount:0,bestScore:0,color:"purple"},{category:"telecom-fraud",title:"网络通信",description:"识别电信诈骗、钓鱼网站等网络风险",icon:"\uD83D\uDCF1",difficulty:"advanced",questionCount:20,completedCount:2,bestScore:45,color:"indigo"},{category:"fake-authority",title:"冒充权威",description:"识别冒充公检法、政府机构等权威诈骗",icon:"⚖️",difficulty:"advanced",questionCount:8,completedCount:0,bestScore:0,color:"red"},{category:"part-time-scam",title:"兼职副业",description:"防范刷单诈骗、兼职陷阱等副业风险",icon:"\uD83D\uDCBB",difficulty:"beginner",questionCount:14,completedCount:6,bestScore:92,color:"pink"}];function CategoryPracticePage(){let e=(0,m.useRouter)(),[t,s]=(0,l.useState)("all"),[d,p]=(0,l.useState)(null);(0,l.useEffect)(()=>{let e=localStorage.getItem("userData");e&&p(JSON.parse(e))},[]);let g="all"===t?u:u.filter(e=>e.difficulty===t),getDifficultyLabel=e=>({beginner:"入门",intermediate:"进阶",advanced:"高级"})[e]||e,getColorClasses=e=>{let t={blue:"from-blue-500 to-blue-600 border-blue-200 bg-blue-50",green:"from-green-500 to-green-600 border-green-200 bg-green-50",yellow:"from-yellow-500 to-yellow-600 border-yellow-200 bg-yellow-50",purple:"from-purple-500 to-purple-600 border-purple-200 bg-purple-50",indigo:"from-indigo-500 to-indigo-600 border-indigo-200 bg-indigo-50",red:"from-red-500 to-red-600 border-red-200 bg-red-50",pink:"from-pink-500 to-pink-600 border-pink-200 bg-pink-50"};return t[e]||t.blue},handleStartPractice=t=>{localStorage.setItem("selectedCategory",t),d?e.push(`/category-challenge?category=${t}`):e.push(`/demo-quiz?category=${t}`)};return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[r.jsx("header",{className:"bg-white shadow-sm border-b",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[r.jsx(x(),{href:d?"/dashboard":"/",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:r.jsx(a.Z,{className:"w-5 h-5 text-gray-600"})}),r.jsx(i.Z,{className:"w-8 h-8 text-primary-600"}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"分类刷题"}),r.jsx("p",{className:"text-sm text-gray-600",children:"选择感兴趣的领域，针对性学习防诈骗知识"})]})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"card mb-8",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"难度筛选"}),r.jsx("div",{className:"flex flex-wrap gap-3",children:["all","beginner","intermediate","advanced"].map(e=>r.jsx("button",{onClick:()=>s(e),className:`px-4 py-2 rounded-full text-sm font-medium transition-all ${t===e?"bg-primary-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:"all"===e?"全部":getDifficultyLabel(e)},e))})]}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:g.map(e=>(0,r.jsxs)("div",{className:`card hover:shadow-lg transition-all duration-300 border-2 ${getColorClasses(e.color)}`,children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[r.jsx("div",{className:"text-4xl",children:e.icon}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("h3",{className:"text-xl font-bold text-gray-900",children:e.title}),r.jsx("p",{className:"text-sm text-gray-600",children:e.description})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[r.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${"beginner"===e.difficulty?"bg-green-100 text-green-700":"intermediate"===e.difficulty?"bg-yellow-100 text-yellow-700":"bg-red-100 text-red-700"}`,children:getDifficultyLabel(e.difficulty)}),(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:[e.questionCount," 道题目"]})]}),(0,r.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[r.jsx("span",{className:"text-gray-600",children:"完成进度"}),(0,r.jsxs)("span",{className:"font-medium",children:[e.completedCount,"/",e.questionCount]})]}),r.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:r.jsx("div",{className:`h-2 rounded-full bg-gradient-to-r ${getColorClasses(e.color).split(" ")[0]} ${getColorClasses(e.color).split(" ")[1]}`,style:{width:`${e.completedCount/e.questionCount*100}%`}})}),e.bestScore>0&&(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[r.jsx(c.Z,{className:"w-4 h-4 text-yellow-500"}),(0,r.jsxs)("span",{className:"text-gray-600",children:["最佳成绩: ",e.bestScore,"%"]})]})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsxs)("button",{onClick:()=>handleStartPractice(e.category),className:"flex-1 btn-primary flex items-center justify-center gap-2",children:[r.jsx(o.Z,{className:"w-4 h-4"}),"开始练习"]}),e.completedCount>0&&(0,r.jsxs)("button",{className:"btn-secondary flex items-center gap-2",children:[r.jsx(n.Z,{className:"w-4 h-4"}),"复习"]})]})]},e.category))}),(0,r.jsxs)("div",{className:"card mt-8 bg-gradient-to-r from-primary-500 to-purple-600 text-white",children:[r.jsx("h3",{className:"text-xl font-bold mb-4",children:"\uD83D\uDCA1 学习建议"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-semibold mb-2",children:"新手推荐路径"}),(0,r.jsxs)("ol",{className:"text-primary-100 text-sm space-y-1",children:[r.jsx("li",{children:"1. 求职防骗 → 基础必学"}),r.jsx("li",{children:"2. 租房安全 → 生活必备"}),r.jsx("li",{children:"3. 兼职副业 → 收入相关"}),r.jsx("li",{children:"4. 金融理财 → 进阶学习"})]})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-semibold mb-2",children:"学习小贴士"}),(0,r.jsxs)("ul",{className:"text-primary-100 text-sm space-y-1",children:[r.jsx("li",{children:"• 建议每天练习1-2个分类"}),r.jsx("li",{children:"• 错题要及时复习巩固"}),r.jsx("li",{children:"• 结合实际生活场景思考"}),r.jsx("li",{children:"• 分享经验帮助他人学习"})]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mt-8",children:[(0,r.jsxs)("div",{className:"card text-center",children:[r.jsx("div",{className:"text-2xl font-bold text-primary-600",children:u.reduce((e,t)=>e+t.questionCount,0)}),r.jsx("div",{className:"text-sm text-gray-600",children:"总题目数"})]}),(0,r.jsxs)("div",{className:"card text-center",children:[r.jsx("div",{className:"text-2xl font-bold text-success-600",children:u.reduce((e,t)=>e+t.completedCount,0)}),r.jsx("div",{className:"text-sm text-gray-600",children:"已完成"})]}),(0,r.jsxs)("div",{className:"card text-center",children:[r.jsx("div",{className:"text-2xl font-bold text-yellow-600",children:u.filter(e=>e.bestScore>0).length}),r.jsx("div",{className:"text-sm text-gray-600",children:"已掌握分类"})]}),(0,r.jsxs)("div",{className:"card text-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:[Math.round(u.reduce((e,t)=>e+t.bestScore,0)/u.length),"%"]}),r.jsx("div",{className:"text-sm text-gray-600",children:"平均成绩"})]})]})]})]})}},3292:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>o});var r=s(5153);let l=(0,r.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/category-practice/page.tsx`),{__esModule:a,$$typeof:i}=l,c=l.default,o=c}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[9678,5607,5937,2035],()=>__webpack_exec__(749));module.exports=s})();