(()=>{var e={};e.id=4969,e.ids=[4969],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},1790:(e,t,l)=>{"use strict";l.r(t),l.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>f,tree:()=>c});var a=l(7096),r=l(6132),s=l(7284),i=l.n(s),n=l(2564),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);l.d(t,o);let c=["",{children:["share-score",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(l.bind(l,5079)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/share-score/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(l.bind(l,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(l.bind(l,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],d=["/home/<USER>/Desktop/vscode/防诈挑战游戏/app/share-score/page.tsx"],x="/share-score/page",m={require:l,loadChunk:()=>Promise.resolve()},f=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/share-score/page",pathname:"/share-score",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},7627:(e,t,l)=>{Promise.resolve().then(l.bind(l,577))},577:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>ShareScorePage});var a=l(784),r=l(9885),s=l(9803),i=l(6386),n=l(7439),o=l(5574),c=l(1440),d=l.n(c),x=l(7114),m=l(3165);function ShareScorePage(){let e=(0,x.useRouter)(),t=(0,r.useRef)(null),[l,c]=(0,r.useState)(null),[f,p]=(0,r.useState)(null),[h,u]=(0,r.useState)("classic"),[g,y]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let t=localStorage.getItem("userData");if(!t){e.push("/login");return}p(JSON.parse(t));let l=localStorage.getItem("lastChallengeResult");if(!l){alert("没有找到挑战结果"),e.push("/dashboard");return}c(JSON.parse(l))},[e]);let formatTime=e=>`${Math.floor(e/60)}:${(e%60).toString().padStart(2,"0")}`,generatePoster=async()=>{if(!t.current||!l||!f)return;y(!0);let e=t.current,a=e.getContext("2d");if(a){switch(e.width=600,e.height=800,h){case"classic":drawClassicTemplate(a,e.width,e.height);break;case"modern":drawModernTemplate(a,e.width,e.height);break;case"minimal":drawMinimalTemplate(a,e.width,e.height)}y(!1)}},drawClassicTemplate=(e,t,a)=>{let r=e.createLinearGradient(0,0,0,a);r.addColorStop(0,"#3B82F6"),r.addColorStop(1,"#1E40AF"),e.fillStyle=r,e.fillRect(0,0,t,a),e.fillStyle="#FFFFFF",e.font="bold 36px Arial",e.textAlign="center",e.fillText("防诈挑战游戏",t/2,80),e.font="bold 28px Arial",e.fillText(f.nickname,t/2,140),e.fillStyle="rgba(255, 255, 255, 0.95)",e.roundRect(50,200,t-100,400,20),e.fill(),e.fillStyle="#1F2937",e.font="bold 48px Arial",e.fillText(`${l.score} 题`,t/2,300),e.font="24px Arial",e.fillText("正确答题数",t/2,330),e.font="bold 32px Arial",e.fillText(formatTime(l.timeUsed),t/2,400),e.font="20px Arial",e.fillText("用时",t/2,430),l.category&&(e.font="bold 24px Arial",e.fillText(m.H[l.category],t/2,480),e.font="18px Arial",e.fillText("挑战类型",t/2,505)),e.font="18px Arial",e.fillStyle="#6B7280",e.fillText(new Date(l.date).toLocaleDateString(),t/2,560),e.fillStyle="#FFFFFF",e.font="20px Arial",e.fillText("提升防诈骗意识，保护自己远离陷阱",t/2,700)},drawModernTemplate=(e,t,a)=>{e.fillStyle="#F8FAFC",e.fillRect(0,0,t,a);let r=e.createLinearGradient(0,0,t,0);r.addColorStop(0,"#8B5CF6"),r.addColorStop(1,"#EC4899"),e.fillStyle=r,e.fillRect(0,0,t,150),e.fillStyle="#FFFFFF",e.font="bold 32px Arial",e.textAlign="center",e.fillText("\uD83D\uDEE1️ 防诈挑战成绩",t/2,60),e.fillText(f.nickname,t/2,110),e.fillStyle="#FFFFFF",e.shadowColor="rgba(0, 0, 0, 0.1)",e.shadowBlur=20,e.roundRect(40,200,t-80,350,15),e.fill(),e.shadowBlur=0,e.fillStyle="#1F2937",e.font="bold 56px Arial",e.fillText(l.score.toString(),t/2,320),e.font="24px Arial",e.fillStyle="#6B7280",e.fillText("正确题数",t/2,350),e.fillStyle="#3B82F6",e.font="bold 28px Arial",e.fillText(formatTime(l.timeUsed),t/2,420),e.fillStyle="#6B7280",e.font="20px Arial",e.fillText("挑战用时",t/2,445),l.category&&(e.fillStyle="#8B5CF6",e.font="bold 22px Arial",e.fillText(m.H[l.category],t/2,500))},drawMinimalTemplate=(e,t,a)=>{e.fillStyle="#FFFFFF",e.fillRect(0,0,t,a),e.strokeStyle="#E5E7EB",e.lineWidth=2,e.strokeRect(20,20,t-40,a-40),e.fillStyle="#1F2937",e.font="bold 28px Arial",e.textAlign="center",e.fillText("防诈挑战成绩单",t/2,100),e.strokeStyle="#D1D5DB",e.lineWidth=1,e.beginPath(),e.moveTo(80,130),e.lineTo(t-80,130),e.stroke(),e.fillStyle="#374151",e.font="24px Arial",e.fillText(`参与者：${f.nickname}`,t/2,180),e.font="bold 48px Arial",e.fillStyle="#059669",e.fillText(l.score.toString(),t/2,280),e.font="20px Arial",e.fillStyle="#6B7280",e.fillText("正确答题数",t/2,310),e.font="bold 32px Arial",e.fillStyle="#3B82F6",e.fillText(formatTime(l.timeUsed),t/2,380),e.font="18px Arial",e.fillStyle="#6B7280",e.fillText("用时",t/2,405),e.font="16px Arial",e.fillText(new Date(l.date).toLocaleDateString(),t/2,500),e.font="18px Arial",e.fillStyle="#1F2937",e.fillText("继续学习，提升防诈骗能力",t/2,600)},sharePoster=async()=>{if(t.current)try{let e=await new Promise(e=>{t.current.toBlob(t=>{e(t)})});if(navigator.share){let t=new File([e],"score.png",{type:"image/png"});await navigator.share({title:"我的防诈挑战成绩",text:`我在防诈挑战中答对了${l.score}题！一起来挑战吧！`,files:[t]})}else await navigator.clipboard.writeText(window.location.origin),alert("链接已复制到剪贴板")}catch(e){console.error("分享失败:",e),alert("分享失败，请尝试下载图片手动分享")}};return((0,r.useEffect)(()=>{l&&f&&generatePoster()},[l,f,h]),l&&f)?(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[a.jsx("header",{className:"bg-white shadow-sm border-b",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(d(),{href:"/dashboard",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(s.Z,{className:"w-5 h-5 text-gray-600"})}),a.jsx(i.Z,{className:"w-8 h-8 text-primary-600"}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"分享成绩"}),a.jsx("p",{className:"text-sm text-gray-600",children:"生成精美的成绩海报"})]})]})})}),a.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"card",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[a.jsx(n.Z,{className:"w-5 h-5 text-primary-600"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"选择模板"})]}),a.jsx("div",{className:"grid grid-cols-3 gap-3",children:[{id:"classic",name:"经典",desc:"蓝色渐变"},{id:"modern",name:"现代",desc:"彩色卡片"},{id:"minimal",name:"简约",desc:"黑白风格"}].map(e=>(0,a.jsxs)("button",{onClick:()=>u(e.id),className:`p-3 border-2 rounded-lg text-center transition-all ${h===e.id?"border-primary-500 bg-primary-50":"border-gray-200 hover:border-primary-300"}`,children:[a.jsx("div",{className:"font-medium text-gray-900",children:e.name}),a.jsx("div",{className:"text-xs text-gray-600",children:e.desc})]},e.id))})]}),(0,a.jsxs)("div",{className:"card",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"成绩详情"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"正确题数"}),a.jsx("span",{className:"font-semibold text-primary-600",children:l.score})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"用时"}),a.jsx("span",{className:"font-semibold text-blue-600",children:formatTime(l.timeUsed)})]}),l.category&&(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"挑战类型"}),a.jsx("span",{className:"font-semibold text-purple-600",children:m.H[l.category]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"挑战日期"}),a.jsx("span",{className:"font-semibold text-gray-900",children:new Date(l.date).toLocaleDateString()})]})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{onClick:()=>{if(!t.current)return;let e=document.createElement("a");e.download=`防诈挑战成绩-${f.nickname}-${new Date().toLocaleDateString()}.png`,e.href=t.current.toDataURL(),e.click()},disabled:g,className:"w-full btn-primary flex items-center justify-center gap-2 disabled:opacity-50",children:[a.jsx(o.Z,{className:"w-5 h-5"}),"下载海报"]}),(0,a.jsxs)("button",{onClick:sharePoster,disabled:g,className:"w-full btn-secondary flex items-center justify-center gap-2 disabled:opacity-50",children:[a.jsx(i.Z,{className:"w-5 h-5"}),"分享海报"]})]})]}),(0,a.jsxs)("div",{className:"card",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"海报预览"}),a.jsx("div",{className:"flex justify-center",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx("canvas",{ref:t,className:"max-w-full h-auto border border-gray-200 rounded-lg shadow-lg",style:{maxHeight:"600px"}}),g&&a.jsx("div",{className:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"})})]})})]})]})})]}):a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"})})}},3165:(e,t,l)=>{"use strict";l.d(t,{H:()=>a});let a={"job-fraud":"求职骗局","rental-scam":"租房陷阱","loan-trap":"网贷陷阱","training-scam":"培训诈骗","telecom-fraud":"电信诈骗","fake-authority":"冒充公检法","part-time-scam":"兼职诈骗"}},5079:(e,t,l)=>{"use strict";l.r(t),l.d(t,{$$typeof:()=>i,__esModule:()=>s,default:()=>o});var a=l(5153);let r=(0,a.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/share-score/page.tsx`),{__esModule:s,$$typeof:i}=r,n=r.default,o=n}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),l=t.X(0,[9678,5607,5276,5883,2035],()=>__webpack_exec__(1790));module.exports=l})();