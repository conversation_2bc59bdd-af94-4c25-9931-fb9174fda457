(()=>{var e={};e.id=8810,e.ids=[8810],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},3429:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d});var a=t(7096),l=t(6132),r=t(7284),i=t.n(r),n=t(2564),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["challenge",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4517)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/challenge/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],o=["/home/<USER>/Desktop/vscode/防诈挑战游戏/app/challenge/page.tsx"],x="/challenge/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/challenge/page",pathname:"/challenge",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9538:(e,s,t)=>{Promise.resolve().then(t.bind(t,5284))},5284:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>ChallengePage});var a=t(784),l=t(9885),r=t(2032),i=t(1057),n=t(9803),c=t(696),d=t(6494),o=t(4409),x=t(1440),m=t.n(x),h=t(7114),u=t(3165),g=t(2995);function ChallengePage(){let e=(0,h.useRouter)(),[s,t]=(0,l.useState)(null),[x,p]=(0,l.useState)(!1),[b,j]=(0,l.useState)(0),[N,f]=(0,l.useState)(0),[v,y]=(0,l.useState)(0),[w,S]=(0,l.useState)(null),[_,k]=(0,l.useState)(!1),[C,D]=(0,l.useState)(null),[P,q]=(0,l.useState)(!1),A=g.F.filter(e=>!s||e.category===s),I=A[b];(0,l.useEffect)(()=>{let s;let t=localStorage.getItem("userData");if(!t){e.push("/login");return}let a=localStorage.getItem("lastChallengeDate"),l=new Date().toDateString();if(a===l){alert("今日挑战已完成，请明天再来！"),e.push("/dashboard");return}return x&&!_&&(s=setInterval(()=>{w&&y(Math.floor((Date.now()-w)/1e3))},1e3)),()=>{s&&clearInterval(s)}},[x,_,w,e]);let handleAnswerSelect=e=>{D(e)},finishChallenge=()=>{k(!0);let e={score:N,timeUsed:v,category:s,date:new Date().toISOString(),questionsAnswered:b+1};localStorage.setItem("lastChallengeDate",new Date().toDateString()),localStorage.setItem("lastChallengeResult",JSON.stringify(e));let t=JSON.parse(localStorage.getItem("userData")||"{}");t.contributionScore=(t.contributionScore||0)+N,localStorage.setItem("userData",JSON.stringify(t))},formatTime=e=>`${Math.floor(e/60)}:${(e%60).toString().padStart(2,"0")}`;return _?a.jsx("div",{className:"min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8",children:a.jsx("div",{className:"max-w-2xl w-full",children:(0,a.jsxs)("div",{className:"card text-center",children:[(0,a.jsxs)("div",{className:"mb-6",children:[N>0?a.jsx(r.Z,{className:"w-16 h-16 text-success-500 mx-auto mb-4"}):a.jsx(i.Z,{className:"w-16 h-16 text-danger-500 mx-auto mb-4"}),a.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"挑战完成！"}),a.jsx("p",{className:"text-gray-600",children:N>0?`恭喜你答对了 ${N} 道题！`:"很遗憾，第一题就答错了，继续加油！"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"bg-primary-50 rounded-lg p-4",children:[a.jsx("div",{className:"text-2xl font-bold text-primary-600",children:N}),a.jsx("div",{className:"text-sm text-gray-600",children:"正确题数"})]}),(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[a.jsx("div",{className:"text-2xl font-bold text-blue-600",children:formatTime(v)}),a.jsx("div",{className:"text-sm text-gray-600",children:"用时"})]}),(0,a.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4",children:[a.jsx("div",{className:"text-2xl font-bold text-purple-600",children:s?u.H[s]:"全部"}),a.jsx("div",{className:"text-sm text-gray-600",children:"挑战类型"})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[a.jsx(m(),{href:"/dashboard",className:"btn-primary",children:"返回主页"}),a.jsx(m(),{href:"/leaderboard",className:"btn-secondary",children:"查看排行榜"}),a.jsx(m(),{href:"/share-score",className:"btn-success",children:"分享成绩"})]})]})})}):x?a.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[a.jsx("div",{className:"card mb-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(c.Z,{className:"w-8 h-8 text-primary-600"}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"每日挑战进行中"}),a.jsx("p",{className:"text-sm text-gray-600",children:s?u.H[s]:"全部类型"})]})]}),a.jsx("div",{className:"text-right",children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-lg font-bold text-primary-600",children:N}),a.jsx("div",{className:"text-xs text-gray-500",children:"正确"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-lg font-bold text-blue-600",children:formatTime(v)}),a.jsx("div",{className:"text-xs text-gray-500",children:"用时"})]})]})})]})}),(0,a.jsxs)("div",{className:"card mb-8",children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600 mb-2",children:["第 ",b+1," 题 • ",u.H[I.category]]}),a.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:I.title})]}),a.jsx("div",{className:"space-y-3",children:I.options.map((e,s)=>a.jsx("button",{onClick:()=>handleAnswerSelect(s),disabled:P,className:`quiz-option ${C===s?"selected":""} ${P?s===I.correctAnswer?"correct":C===s?"incorrect":"":""}`,children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[a.jsx("div",{className:"flex-shrink-0 w-6 h-6 rounded-full border-2 border-current flex items-center justify-center text-sm font-medium",children:String.fromCharCode(65+s)}),(0,a.jsxs)("div",{className:"text-left",children:[a.jsx("div",{className:"font-medium",children:e.text}),P&&a.jsx("div",{className:"mt-2 text-sm opacity-80",children:e.explanation})]})]})},s))}),P&&(0,a.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[a.jsx("h4",{className:"font-semibold text-blue-900 mb-2",children:"详细解析："}),a.jsx("p",{className:"text-blue-800 text-sm",children:I.explanation})]})]}),!P&&(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("button",{onClick:()=>{confirm("确定要退出挑战吗？今日将无法再次参与。")&&e.push("/dashboard")},className:"btn-secondary",children:"退出挑战"}),a.jsx("button",{onClick:()=>{if(null===C){alert("请选择一个答案");return}let e=C===I.correctAnswer;q(!0),e?(f(N+1),setTimeout(()=>{b<A.length-1?(j(b+1),D(null),q(!1)):finishChallenge()},2e3)):setTimeout(()=>{finishChallenge()},3e3)},disabled:null===C,className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:"提交答案"})]})]})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[a.jsx("header",{className:"bg-white shadow-sm border-b",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(m(),{href:"/dashboard",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(n.Z,{className:"w-5 h-5 text-gray-600"})}),a.jsx(c.Z,{className:"w-8 h-8 text-primary-600"}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"每日挑战"}),a.jsx("p",{className:"text-sm text-gray-600",children:"选择类型开始挑战"})]})]})})}),a.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsxs)("div",{className:"card mb-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[a.jsx(d.Z,{className:"w-16 h-16 text-yellow-500 mx-auto mb-4"}),a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"准备开始挑战"}),a.jsx("p",{className:"text-gray-600",children:"选择一个诈骗类型，持续答题直到答错。每天只能挑战一次，加油！"})]}),(0,a.jsxs)("div",{className:"mb-8",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"选择挑战类型："}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,a.jsxs)("button",{onClick:()=>t(null),className:`p-4 border-2 rounded-lg text-left transition-all ${null===s?"border-primary-500 bg-primary-50":"border-gray-200 hover:border-primary-300"}`,children:[a.jsx("div",{className:"font-semibold text-gray-900",children:"全部类型"}),a.jsx("div",{className:"text-sm text-gray-600",children:"混合挑战所有诈骗类型"})]}),Object.entries(u.H).map(([e,l])=>(0,a.jsxs)("button",{onClick:()=>t(e),className:`p-4 border-2 rounded-lg text-left transition-all ${s===e?"border-primary-500 bg-primary-50":"border-gray-200 hover:border-primary-300"}`,children:[a.jsx("div",{className:"font-semibold text-gray-900",children:l}),a.jsx("div",{className:"text-sm text-gray-600",children:"专项挑战"})]},e))]})]}),a.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[a.jsx(o.Z,{className:"w-5 h-5 text-yellow-600 mt-0.5"}),(0,a.jsxs)("div",{className:"text-sm text-yellow-800",children:[a.jsx("p",{className:"font-medium mb-1",children:"挑战规则："}),(0,a.jsxs)("ul",{className:"space-y-1 text-yellow-700",children:[a.jsx("li",{children:"• 持续答题直到答错或完成所有题目"}),a.jsx("li",{children:"• 每天只能参与一次挑战"}),a.jsx("li",{children:"• 答题数量和用时将计入排行榜"}),a.jsx("li",{children:"• 完成挑战可获得贡献值奖励"})]})]})]})}),a.jsx("button",{onClick:()=>{if(!s){alert("请选择一个挑战类型");return}p(!0),S(Date.now())},disabled:void 0===s,className:"w-full btn-primary text-lg py-4 disabled:opacity-50 disabled:cursor-not-allowed",children:"开始挑战"})]})})]})}},3165:(e,s,t)=>{"use strict";t.d(s,{H:()=>a});let a={"job-fraud":"求职骗局","rental-scam":"租房陷阱","loan-trap":"网贷陷阱","training-scam":"培训诈骗","telecom-fraud":"电信诈骗","fake-authority":"冒充公检法","part-time-scam":"兼职诈骗"}},4517:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>r,default:()=>c});var a=t(5153);let l=(0,a.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/challenge/page.tsx`),{__esModule:r,$$typeof:i}=l,n=l.default,c=n}};var s=require("../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[9678,5607,8998,3367,2035,2995],()=>__webpack_exec__(3429));module.exports=t})();