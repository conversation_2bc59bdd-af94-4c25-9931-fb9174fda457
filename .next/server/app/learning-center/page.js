(()=>{var e={};e.id=1206,e.ids=[1206],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},8018:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>d});var a=t(7096),r=t(6132),i=t(7284),l=t.n(i),n=t(2564),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["learning-center",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3132)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/learning-center/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],o=["/home/<USER>/Desktop/vscode/防诈挑战游戏/app/learning-center/page.tsx"],x="/learning-center/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/learning-center/page",pathname:"/learning-center",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5063:(e,s,t)=>{Promise.resolve().then(t.bind(t,4326))},4326:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>LearningCenterPage});var a=t(784),r=t(9885),i=t(987),l=t(1210),n=t(8260),c=t(6920),d=t(4571),o=t(4901),x=t(1672),m=t(9803),p=t(8937),g=t(4409),h=t(1440),u=t.n(h),j=t(3165);let N=[{category:"job-fraud",icon:a.jsx(i.Z,{className:"w-6 h-6"}),title:"求职骗局",description:"利用求职者急于找工作的心理，通过虚假招聘信息进行诈骗。",tips:["正规公司不会要求求职者预先交费",'警惕"高薪轻松"、"日赚千元"等诱人条件',"通过官方渠道核实公司信息","面试地点应在正规办公场所"],cases:['虚假招聘要求交"培训费"、"保证金"',"冒充知名企业进行电话面试诈骗",'以"内推"名义收取"介绍费"',"传销组织伪装成正规公司招聘"],prevention:["通过企业官网或正规招聘平台投递简历","面试前核实公司营业执照和办公地址","不向任何招聘方预付费用","保持警惕，相信常识判断"]},{category:"rental-scam",icon:a.jsx(l.Z,{className:"w-6 h-6"}),title:"租房陷阱",description:"利用租房需求，通过虚假房源信息或不合理条款进行诈骗。",tips:["实地看房，不要仅凭照片决定","必须签署正式租房合同","核实房东身份和房屋产权","警惕价格明显低于市场价的房源"],cases:["假房东收取定金后消失","一房多租，同时收取多人定金","虚假房源照片，实际房屋条件极差","不签合同，口头承诺后反悔"],prevention:["通过正规中介或房屋租赁平台找房","要求查看房产证和房东身份证","签署正式合同并保留所有凭证","不要一次性支付大额租金"]},{category:"loan-trap",icon:a.jsx(n.Z,{className:"w-6 h-6"}),title:"网贷陷阱",description:'以"无抵押、快速放款"为诱饵，实际收取高额费用的贷款诈骗。',tips:["正规贷款不需要预交任何费用",'警惕"无条件放款"的虚假承诺',"了解正常贷款流程和利率水平","通过银行等正规金融机构贷款"],cases:['要求预交"手续费"、"保证金"','承诺"无条件放款"后收费不放款',"利率远超法律规定的高利贷","套取个人信息后进行其他诈骗"],prevention:["选择银行或持牌金融机构","仔细阅读贷款合同条款","不向任何贷款机构预付费用","保护个人身份和银行信息"]},{category:"training-scam",icon:a.jsx(c.Z,{className:"w-6 h-6"}),title:"培训诈骗",description:"以技能培训、学历提升为名，收取高额费用但不提供承诺服务。",tips:["核实培训机构的资质和口碑",'警惕"包过"、"包就业"等承诺',"了解正常的培训费用水平",'不要被"限时优惠"冲昏头脑'],cases:['承诺"包过包就业"收取高额培训费',"虚假宣传培训效果和就业率",'以"内部渠道"名义收取额外费用',"培训内容与宣传严重不符"],prevention:["选择有资质的正规培训机构","实地考察培训环境和师资","签署详细的培训协议","保留所有付费和培训凭证"]},{category:"telecom-fraud",icon:a.jsx(d.Z,{className:"w-6 h-6"}),title:"电信诈骗",description:"通过电话、短信、网络等方式实施的远程诈骗活动。",tips:["不轻信陌生电话和短信","不点击可疑链接","不向陌生人透露个人信息","遇到可疑情况及时报警"],cases:["冒充银行客服套取银行卡信息","虚假中奖信息要求交税费","冒充快递公司要求点击链接","虚假购物退款要求提供验证码"],prevention:["通过官方渠道核实信息真伪","不在陌生网站输入敏感信息","设置复杂密码并定期更换","开启银行卡短信提醒功能"]},{category:"fake-authority",icon:a.jsx(o.Z,{className:"w-6 h-6"}),title:"冒充公检法",description:"冒充公安、检察院、法院等执法部门进行的诈骗活动。",tips:["执法部门不会电话要求转账",'不存在所谓的"安全账户"',"正规执法程序不会保密进行","遇到此类电话立即挂断"],cases:["声称涉嫌洗钱要求转账配合调查","冒充法院要求交纳保证金","声称快递涉毒要求证明清白","冒充警察要求提供银行信息"],prevention:["立即挂断可疑电话","到就近派出所核实情况","不向任何人转账汇款","保持冷静，理性判断"]},{category:"part-time-scam",icon:a.jsx(x.Z,{className:"w-6 h-6"}),title:"兼职诈骗",description:"以兼职工作为诱饵，要求预付费用或套取个人信息的诈骗。",tips:["正规兼职不需要预付费用",'警惕"轻松赚钱"的虚假承诺',"通过正规平台寻找兼职","保护个人身份信息安全"],cases:["刷单兼职要求预付保证金","打字兼职要求购买软件","代理加盟要求交纳代理费","网络兼职套取银行卡信息"],prevention:["选择知名的兼职招聘平台","不向任何兼职方预付费用","核实兼职公司的真实性","保留所有沟通和交易记录"]}];function LearningCenterPage(){let[e,s]=(0,r.useState)("job-fraud"),t=N.find(s=>s.category===e);return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[a.jsx("header",{className:"bg-white shadow-sm border-b",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(u(),{href:"/dashboard",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(m.Z,{className:"w-5 h-5 text-gray-600"})}),a.jsx(p.Z,{className:"w-8 h-8 text-primary-600"}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"学习中心"}),a.jsx("p",{className:"text-sm text-gray-600",children:"防诈骗知识库"})]})]})})}),a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[a.jsx("div",{className:"lg:col-span-1",children:(0,a.jsxs)("div",{className:"card sticky top-8",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"诈骗类型"}),a.jsx("nav",{className:"space-y-2",children:N.map(t=>(0,a.jsxs)("button",{onClick:()=>s(t.category),className:`w-full flex items-center gap-3 p-3 rounded-lg text-left transition-all ${e===t.category?"bg-primary-100 text-primary-700 border-primary-200":"hover:bg-gray-50 text-gray-700"}`,children:[t.icon,a.jsx("span",{className:"font-medium",children:j.H[t.category]})]},t.category))})]})}),(0,a.jsxs)("div",{className:"lg:col-span-3 space-y-8",children:[(0,a.jsxs)("div",{className:"card",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[t.icon,a.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:t.title})]}),a.jsx("p",{className:"text-gray-600 text-lg",children:t.description})]}),(0,a.jsxs)("div",{className:"card",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[a.jsx(g.Z,{className:"w-6 h-6 text-yellow-500"}),a.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:"识别要点"})]}),a.jsx("ul",{className:"space-y-3",children:t.tips.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-start gap-3",children:[a.jsx("div",{className:"flex-shrink-0 w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center mt-0.5",children:a.jsx("span",{className:"text-yellow-600 text-sm font-bold",children:s+1})}),a.jsx("span",{className:"text-gray-700",children:e})]},s))})]}),(0,a.jsxs)("div",{className:"card",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"常见案例"}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:t.cases.map((e,s)=>a.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[a.jsx("div",{className:"flex-shrink-0 w-6 h-6 bg-red-100 rounded-full flex items-center justify-center",children:a.jsx(g.Z,{className:"w-4 h-4 text-red-600"})}),a.jsx("span",{className:"text-red-800 text-sm",children:e})]})},s))})]}),(0,a.jsxs)("div",{className:"card",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[a.jsx(o.Z,{className:"w-6 h-6 text-green-500"}),a.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:"防范措施"})]}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:t.prevention.map((e,s)=>a.jsx("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[a.jsx("div",{className:"flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center",children:a.jsx(o.Z,{className:"w-4 h-4 text-green-600"})}),a.jsx("span",{className:"text-green-800 text-sm",children:e})]})},s))})]}),(0,a.jsxs)("div",{className:"card bg-gradient-to-r from-primary-500 to-purple-600 text-white",children:[a.jsx("h3",{className:"text-xl font-bold mb-4",children:"立即行动"}),(0,a.jsxs)("p",{className:"text-primary-100 mb-6",children:["学习了",t.title,"的防范知识，现在就去挑战相关题目，检验你的学习成果吧！"]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[a.jsx(u(),{href:"/challenge",className:"btn-primary bg-white text-primary-600 hover:bg-gray-100",children:"开始挑战"}),a.jsx(u(),{href:"/starter-quiz",className:"btn-secondary bg-white/10 text-white border-white/20 hover:bg-white/20",children:"重做入门题"})]})]})]})]})})]})}},3165:(e,s,t)=>{"use strict";t.d(s,{H:()=>a});let a={"job-fraud":"求职骗局","rental-scam":"租房陷阱","loan-trap":"网贷陷阱","training-scam":"培训诈骗","telecom-fraud":"电信诈骗","fake-authority":"冒充公检法","part-time-scam":"兼职诈骗"}},3132:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>i,default:()=>c});var a=t(5153);let r=(0,a.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/learning-center/page.tsx`),{__esModule:i,$$typeof:l}=r,n=r.default,c=n}};var s=require("../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[9678,5607,4162,2035],()=>__webpack_exec__(8018));module.exports=t})();