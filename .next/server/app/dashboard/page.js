(()=>{var e={};e.id=7702,e.ids=[7702],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},468:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>o,pages:()=>x,routeModule:()=>h,tree:()=>n});var t=a(7096),r=a(6132),l=a(7284),i=a.n(l),d=a(2564),c={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>d[e]);a.d(s,c);let n=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,1618)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/dashboard/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],x=["/home/<USER>/Desktop/vscode/防诈挑战游戏/app/dashboard/page.tsx"],o="/dashboard/page",m={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},6574:(e,s,a)=>{Promise.resolve().then(a.bind(a,8661))},8661:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>DashboardPage});var t=a(784),r=a(9885),l=a(4901),i=a(5441),d=a(517),c=a(7932),n=a(696),x=a(7631),o=a(6494),m=a(2385),h=a(1438),g=a(8937),p=a(1672),u=a(1440),j=a.n(u),N=a(7114),b=a(3165);function DashboardPage(){let e=(0,N.useRouter)(),[s,a]=(0,r.useState)(null),[u,v]=(0,r.useState)(null),[f,y]=(0,r.useState)(null),[w,D]=(0,r.useState)(!0);return((0,r.useEffect)(()=>{let s=localStorage.getItem("userData");if(!s){e.push("/login");return}let t=JSON.parse(s);a(t);let r=localStorage.getItem("starterCompleted");if(!r){e.push("/starter-quiz");return}let l=localStorage.getItem("starterResult");l&&v(JSON.parse(l));let i=localStorage.getItem("lastChallengeDate"),d=new Date().toDateString();i===d&&D(!1),y(i)},[e]),s)?(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[t.jsx("header",{className:"bg-white shadow-sm border-b",children:t.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[t.jsx(l.Z,{className:"w-8 h-8 text-primary-600"}),(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"防诈挑战游戏"}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["欢迎回来，",s.nickname]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)("div",{className:"text-right",children:[t.jsx("div",{className:"text-sm text-gray-600",children:"贡献值"}),t.jsx("div",{className:"font-semibold text-primary-600",children:s.contributionScore||0})]}),t.jsx(j(),{href:"/profile",className:"btn-secondary",children:"个人中心"})]})]})})}),(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[u&&t.jsx("div",{className:"card mb-8 bg-gradient-to-r from-primary-500 to-purple-600 text-white",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("h2",{className:"text-2xl font-bold mb-2",children:"入门挑战已完成！"}),(0,t.jsxs)("p",{className:"text-primary-100 mb-4",children:["你在入门测试中答对了 ",u.score,"/",u.total," 题， 正确率 ",Math.round(u.score/u.total*100),"%"]}),(0,t.jsxs)("div",{className:"flex items-center gap-4 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[t.jsx(i.Z,{className:"w-4 h-4"}),"用时 ",Math.floor(u.timeUsed/60),":",(u.timeUsed%60).toString().padStart(2,"0")]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[t.jsx(d.Z,{className:"w-4 h-4"}),new Date(u.completedAt).toLocaleDateString()]})]})]}),t.jsx("div",{className:"text-right",children:t.jsx(c.Z,{className:"w-16 h-16 text-yellow-300"})})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8",children:[(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[t.jsx("div",{className:"p-2 bg-primary-100 rounded-lg",children:t.jsx(n.Z,{className:"w-6 h-6 text-primary-600"})}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"每日挑战"}),t.jsx("p",{className:"text-sm text-gray-600",children:"持续答题直到答错"})]})]}),w?(0,t.jsxs)("div",{className:"space-y-3",children:[t.jsx("div",{className:"bg-success-50 border border-success-200 rounded-lg p-3",children:t.jsx("p",{className:"text-success-800 text-xs",children:"✨ 今日挑战已准备就绪！"})}),(0,t.jsxs)(j(),{href:"/challenge",className:"btn-primary w-full flex items-center justify-center gap-2 text-sm",children:["开始挑战",t.jsx(x.Z,{className:"w-4 h-4"})]})]}):(0,t.jsxs)("div",{className:"space-y-3",children:[t.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:t.jsx("p",{className:"text-yellow-800 text-xs",children:"⏰ 今日挑战已完成"})}),t.jsx("button",{disabled:!0,className:"btn-primary w-full opacity-50 cursor-not-allowed text-sm",children:"已完成"})]})]}),(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[t.jsx("div",{className:"p-2 bg-orange-100 rounded-lg",children:t.jsx(o.Z,{className:"w-6 h-6 text-orange-600"})}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"分类刷题"}),t.jsx("p",{className:"text-sm text-gray-600",children:"针对性强化学习"})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[t.jsx("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-3",children:t.jsx("p",{className:"text-orange-800 text-xs",children:"\uD83C\uDFAF 选择感兴趣的领域深度学习"})}),(0,t.jsxs)(j(),{href:"/category-practice",className:"btn-primary bg-orange-600 hover:bg-orange-700 w-full flex items-center justify-center gap-2 text-sm",children:["开始刷题",t.jsx(n.Z,{className:"w-4 h-4"})]})]})]}),(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[t.jsx("div",{className:"p-2 bg-success-100 rounded-lg",children:t.jsx(m.Z,{className:"w-6 h-6 text-success-600"})}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"贡献题目"}),t.jsx("p",{className:"text-sm text-gray-600",children:"分享真实案例"})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[t.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:t.jsx("p",{className:"text-blue-800 text-xs",children:"\uD83D\uDCA1 帮助其他人避免诈骗"})}),(0,t.jsxs)(j(),{href:"/submit-question",className:"btn-success w-full flex items-center justify-center gap-2 text-sm",children:["提交题目",t.jsx(m.Z,{className:"w-4 h-4"})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[t.jsx(j(),{href:"/leaderboard",className:"card hover:shadow-lg transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[t.jsx("div",{className:"p-2 bg-yellow-100 rounded-lg",children:t.jsx(h.Z,{className:"w-6 h-6 text-yellow-600"})}),(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-semibold text-gray-900",children:"排行榜"}),t.jsx("p",{className:"text-sm text-gray-600",children:"查看挑战排名"})]})]})}),t.jsx(j(),{href:"/learning-center",className:"card hover:shadow-lg transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[t.jsx("div",{className:"p-2 bg-purple-100 rounded-lg",children:t.jsx(g.Z,{className:"w-6 h-6 text-purple-600"})}),(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-semibold text-gray-900",children:"学习中心"}),t.jsx("p",{className:"text-sm text-gray-600",children:"防诈骗知识库"})]})]})}),t.jsx(j(),{href:"/community",className:"card hover:shadow-lg transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[t.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:t.jsx(p.Z,{className:"w-6 h-6 text-green-600"})}),(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-semibold text-gray-900",children:"社区"}),t.jsx("p",{className:"text-sm text-gray-600",children:"用户交流分享"})]})]})})]}),(0,t.jsxs)("div",{className:"mt-12",children:[t.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"\uD83D\uDE80 即将推出"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)(j(),{href:"/ai-features",className:"card hover:shadow-lg transition-shadow bg-gradient-to-r from-purple-50 to-indigo-50 border-purple-200",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[t.jsx("div",{className:"p-2 bg-purple-100 rounded-lg",children:t.jsx("span",{className:"text-2xl",children:"\uD83E\uDD16"})}),(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-semibold text-gray-900",children:"AI 智能功能"}),t.jsx("p",{className:"text-sm text-gray-600",children:"智能出题、个性化推荐、数据分析"})]})]}),t.jsx("div",{className:"bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full inline-block",children:"功能演示"})]}),(0,t.jsxs)(j(),{href:"/multimedia-content",className:"card hover:shadow-lg transition-shadow bg-gradient-to-r from-green-50 to-blue-50 border-green-200",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[t.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:t.jsx("span",{className:"text-2xl",children:"\uD83C\uDFAC"})}),(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-semibold text-gray-900",children:"多媒体内容"}),t.jsx("p",{className:"text-sm text-gray-600",children:"视频教学、音频课程、互动体验"})]})]}),t.jsx("div",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full inline-block",children:"内容预览"})]})]})]}),(0,t.jsxs)("div",{className:"mt-12",children:[t.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"诈骗类型分类"}),t.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4",children:Object.entries(b.H).map(([e,s])=>(0,t.jsxs)("div",{className:"card text-center p-4 hover:shadow-lg transition-shadow",children:[t.jsx("div",{className:"text-2xl mb-2",children:"\uD83D\uDEE1️"}),t.jsx("div",{className:"text-sm font-medium text-gray-900",children:s})]},e))})]})]})]}):t.jsx("div",{className:"min-h-screen flex items-center justify-center",children:t.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"})})}},3165:(e,s,a)=>{"use strict";a.d(s,{H:()=>t});let t={"job-fraud":"求职骗局","rental-scam":"租房陷阱","loan-trap":"网贷陷阱","training-scam":"培训诈骗","telecom-fraud":"电信诈骗","fake-authority":"冒充公检法","part-time-scam":"兼职诈骗"}},1618:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>i,__esModule:()=>l,default:()=>c});var t=a(5153);let r=(0,t.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/dashboard/page.tsx`),{__esModule:l,$$typeof:i}=r,d=r.default,c=d}};var s=require("../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),a=s.X(0,[9678,5607,1016,1400,2035],()=>__webpack_exec__(468));module.exports=a})();