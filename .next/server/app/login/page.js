(()=>{var e={};e.id=2626,e.ids=[2626],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},464:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>o});var a=s(7096),n=s(6132),r=s(7284),i=s.n(r),l=s(2564),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o=["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5004)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/login/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],c=["/home/<USER>/Desktop/vscode/防诈挑战游戏/app/login/page.tsx"],m="/login/page",x={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},7858:(e,t,s)=>{Promise.resolve().then(s.bind(s,9402))},9402:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>LoginPage});var a=s(784),n=s(9885),r=s(4901),i=s(1831),l=s(7800),d=s(3680),o=s(7631),c=s(1440),m=s.n(c),x=s(7114);function LoginPage(){let e=(0,x.useRouter)(),[t,s]=(0,n.useState)({inviteCode:"",nickname:""}),[c,p]=(0,n.useState)({}),[u,h]=(0,n.useState)(!1),handleInputChange=e=>{let{name:t,value:a}=e.target;s(e=>({...e,[t]:a})),c[t]&&p(e=>({...e,[t]:""}))},validateForm=()=>{let e={};return t.inviteCode.trim()?t.inviteCode.length<4&&(e.inviteCode="邀请码至少需要4位"):e.inviteCode="请输入邀请码",t.nickname.trim()?t.nickname.length<2?e.nickname="昵称至少需要2个字符":t.nickname.length>20&&(e.nickname="昵称不能超过20个字符"):e.nickname="请输入昵称",p(e),0===Object.keys(e).length},handleSubmit=async s=>{if(s.preventDefault(),validateForm()){h(!0);try{await new Promise(e=>setTimeout(e,1e3));let s={id:`user_${Date.now()}`,inviteCode:t.inviteCode,nickname:t.nickname,createdAt:new Date().toISOString(),contributionScore:0};localStorage.setItem("userData",JSON.stringify(s));let a=localStorage.getItem("starterCompleted");a?e.push("/dashboard"):e.push("/starter-quiz")}catch(e){p({general:"登录失败，请重试"})}finally{h(!1)}}};return a.jsx("div",{className:"min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"flex justify-center mb-6",children:a.jsx("div",{className:"p-4 bg-primary-100 rounded-full",children:a.jsx(r.Z,{className:"w-12 h-12 text-primary-600"})})}),a.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"欢迎参与防诈挑战"}),a.jsx("p",{className:"text-gray-600",children:"使用邀请码和昵称匿名登录，开始你的防诈骗学习之旅"})]}),a.jsx("div",{className:"card",children:(0,a.jsxs)("form",{onSubmit:handleSubmit,className:"space-y-6",children:[c.general&&(0,a.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-danger-50 border border-danger-200 rounded-lg text-danger-700",children:[a.jsx(i.Z,{className:"w-5 h-5"}),a.jsx("span",{className:"text-sm",children:c.general})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"inviteCode",className:"block text-sm font-medium text-gray-700 mb-2",children:"邀请码 (A)"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(l.Z,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{id:"inviteCode",name:"inviteCode",type:"text",value:t.inviteCode,onChange:handleInputChange,className:`input-field pl-10 ${c.inviteCode?"border-danger-300 focus:ring-danger-500":""}`,placeholder:"请输入邀请码"})]}),c.inviteCode&&a.jsx("p",{className:"mt-1 text-sm text-danger-600",children:c.inviteCode})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"nickname",className:"block text-sm font-medium text-gray-700 mb-2",children:"昵称 (B)"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(d.Z,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{id:"nickname",name:"nickname",type:"text",value:t.nickname,onChange:handleInputChange,className:`input-field pl-10 ${c.nickname?"border-danger-300 focus:ring-danger-500":""}`,placeholder:"请输入你的昵称"})]}),c.nickname&&a.jsx("p",{className:"mt-1 text-sm text-danger-600",children:c.nickname})]}),a.jsx("button",{type:"submit",disabled:u,className:"w-full btn-primary flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed",children:u?(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),"登录中..."]}):(0,a.jsxs)(a.Fragment,{children:["开始挑战",a.jsx(o.Z,{className:"w-5 h-5"})]})})]})}),a.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[a.jsx(i.Z,{className:"w-5 h-5 text-blue-600 mt-0.5"}),(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[a.jsx("p",{className:"font-medium mb-1",children:"匿名参与说明："}),(0,a.jsxs)("ul",{className:"space-y-1 text-blue-700",children:[a.jsx("li",{children:"• 邀请码：由主项目分配的参与凭证"}),a.jsx("li",{children:"• 昵称：你在游戏中的显示名称"}),a.jsx("li",{children:"• 所有数据仅保存在本地，保护你的隐私"})]})]})]})}),a.jsx("div",{className:"text-center",children:a.jsx(m(),{href:"/",className:"text-primary-600 hover:text-primary-700 text-sm font-medium",children:"← 返回首页"})})]})})}},5004:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>r,default:()=>d});var a=s(5153);let n=(0,a.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/login/page.tsx`),{__esModule:r,$$typeof:i}=n,l=n.default,d=l}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[9678,5607,2222,2035],()=>__webpack_exec__(464));module.exports=s})();