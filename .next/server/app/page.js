(()=>{var e={};e.id=1931,e.ids=[1931],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},5875:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>o,originalPathname:()=>m,pages:()=>d,routeModule:()=>h,tree:()=>x});var a=t(7096),r=t(6132),i=t(7284),l=t.n(i),c=t(2564),n={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);t.d(s,n);let x=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3982)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],d=["/home/<USER>/Desktop/vscode/防诈挑战游戏/app/page.tsx"],m="/page",o={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:x}})},5705:(e,s,t)=>{Promise.resolve().then(t.bind(t,8062))},8062:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>HomePage});var a=t(784),r=t(9885),i=t(4901),l=t(7631),c=t(696),n=t(2385),x=t(8937),d=t(1672),m=t(1438),o=t(4409),h=t(1440),g=t.n(h),u=t(5183),p=t(169),j=t(8626);let b=[{id:"1",title:'差点被"高薪兼职"骗了5000元',content:"在QQ群里看到日赚300的兼职广告，要求先交2000元保证金。幸好室友提醒我这是诈骗，差点就转账了...",category:"兼职诈骗",author:"小李同学",timeAgo:"2小时前",likes:23,comments:8,views:156,isVerified:!0,severity:"high"},{id:"2",title:"租房遇到假房东，损失1500元",content:"在网上看到便宜房源，房东说要先交定金才能看房。结果交了钱就联系不上了，房子根本不存在...",category:"租房陷阱",author:"张小明",timeAgo:"5小时前",likes:31,comments:12,views:203,isVerified:!1,severity:"medium"},{id:"3",title:'网贷"砍头息"套路深，借3000还6000',content:"急需用钱申请了网贷，说是无息但要交各种手续费。最后发现实际利率超高，还不起就威胁恐吓...",category:"网贷陷阱",author:"王小华",timeAgo:"1天前",likes:45,comments:18,views:312,isVerified:!0,severity:"high"},{id:"4",title:'培训机构承诺"包就业"，结果是骗局',content:"交了8000元培训费，说包分配工作。培训结束后推荐的都是销售岗位，和承诺的技术岗完全不符...",category:"培训诈骗",author:"李小红",timeAgo:"2天前",likes:28,comments:15,views:189,isVerified:!1,severity:"medium"},{id:"5",title:"冒充银行客服，差点泄露银行卡信息",content:'接到"银行客服"电话说卡被盗刷，要求提供验证码。还好想起防诈骗知识，直接挂断并报警...',category:"电信诈骗",author:"陈小军",timeAgo:"3天前",likes:52,comments:22,views:278,isVerified:!0,severity:"high"},{id:"6",title:'求职被要求交"体检费"，及时识破',content:"面试通过后HR说要交500元体检费才能入职。感觉不对劲上网查了查，发现是常见的求职诈骗套路...",category:"求职骗局",author:"刘小芳",timeAgo:"4天前",likes:19,comments:7,views:134,isVerified:!1,severity:"low"},{id:"7",title:'投资理财群里的"专家"都是托',content:"被拉进投资群，看到很多人晒收益截图。跟着投了2万元，结果平台跑路了，钱全没了...",category:"投资诈骗",author:"赵小强",timeAgo:"5天前",likes:67,comments:25,views:445,isVerified:!0,severity:"high"},{id:"8",title:"网购退款诈骗，差点被套取验证码",content:'收到"客服"电话说商品有质量问题要退款，要求提供支付宝验证码。幸好多了个心眼没有提供...',category:"网购诈骗",author:"孙小美",timeAgo:"1周前",likes:34,comments:11,views:198,isVerified:!1,severity:"medium"}];function UserStories(){let[e,s]=(0,r.useState)(0),[t,i]=(0,r.useState)(!0);(0,r.useEffect)(()=>{if(!t)return;let e=setInterval(()=>{s(e=>(e+1)%b.length)},5e3);return()=>clearInterval(e)},[t]);let l=b[e];return(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-lg border border-gray-200 p-6 h-full",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(o.Z,{className:"w-5 h-5 text-orange-500"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"用户防骗故事"})]}),a.jsx("button",{onClick:()=>i(!t),className:`px-3 py-1 rounded-full text-xs font-medium transition-colors ${t?"bg-green-100 text-green-700 hover:bg-green-200":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:t?"暂停":"播放"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("h4",{className:"font-semibold text-gray-900 mb-2 line-clamp-2",children:l.title}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[a.jsx("span",{className:"text-sm text-gray-600",children:l.author}),l.isVerified&&a.jsx("span",{className:"w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center",children:a.jsx("span",{className:"text-white text-xs",children:"✓"})}),a.jsx("span",{className:"text-xs text-gray-500",children:"•"}),a.jsx("span",{className:"text-xs text-gray-500",children:l.timeAgo})]})]}),a.jsx("div",{className:`px-2 py-1 rounded-full text-xs font-medium ${(e=>{switch(e){case"high":return"text-red-600 bg-red-100";case"medium":return"text-yellow-600 bg-yellow-100";case"low":return"text-green-600 bg-green-100";default:return"text-gray-600 bg-gray-100"}})(l.severity)}`,children:(e=>{switch(e){case"high":return"高危";case"medium":return"中危";case"low":return"低危";default:return"未知"}})(l.severity)})]}),a.jsx("div",{className:"flex items-center gap-2",children:a.jsx("span",{className:"px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full",children:l.category})}),a.jsx("p",{className:"text-gray-700 text-sm leading-relaxed line-clamp-4",children:l.content}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-xs text-gray-500 pt-3 border-t border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[a.jsx(u.Z,{className:"w-3 h-3"}),a.jsx("span",{children:l.likes})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[a.jsx(p.Z,{className:"w-3 h-3"}),a.jsx("span",{children:l.comments})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[a.jsx(j.Z,{className:"w-3 h-3"}),a.jsx("span",{children:l.views})]})]})]}),a.jsx("div",{className:"flex items-center gap-1 mt-6",children:b.map((t,r)=>a.jsx("button",{onClick:()=>s(r),className:`h-1 rounded-full transition-all duration-300 ${r===e?"bg-primary-600 w-8":"bg-gray-300 w-2 hover:bg-gray-400"}`},r))}),a.jsx("div",{className:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("p",{className:"text-blue-800 text-xs",children:["\uD83D\uDCA1 ",a.jsx("strong",{children:"提醒："}),"这些都是真实用户分享的被骗经历，希望能帮助大家提高警惕，避免类似损失。"]})})]})}function HomePage(){let[e,s]=(0,r.useState)({totalUsers:1234,questionsAnswered:8765,successRate:87});return(0,a.jsxs)("div",{className:"min-h-screen",children:[(0,a.jsxs)("section",{className:"relative overflow-hidden bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800",children:[a.jsx("div",{className:"absolute inset-0 bg-black/20"}),a.jsx("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"flex justify-center mb-8",children:a.jsx("div",{className:"p-4 bg-white/10 rounded-full backdrop-blur-sm",children:a.jsx(i.Z,{className:"w-16 h-16 text-white"})})}),a.jsx("h1",{className:"text-3xl md:text-4xl font-bold text-white mb-4",children:"大学生防诈挑战游戏"}),a.jsx("p",{className:"text-lg md:text-xl text-blue-100 mb-6 max-w-3xl mx-auto",children:"通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱"}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,a.jsxs)(g(),{href:"/demo-quiz",className:"btn-primary text-lg px-6 py-3 inline-flex items-center gap-2",children:["免费答题 (10题)",a.jsx(l.Z,{className:"w-5 h-5"})]}),(0,a.jsxs)(g(),{href:"/category-practice",className:"btn-secondary bg-white/10 text-white border-white/20 hover:bg-white/20 text-lg px-6 py-3 inline-flex items-center gap-2",children:["分类刷题",a.jsx(c.Z,{className:"w-5 h-5"})]}),(0,a.jsxs)(g(),{href:"/submit-question",className:"btn-secondary bg-white/10 text-white border-white/20 hover:bg-white/20 text-lg px-6 py-3 inline-flex items-center gap-2",children:["体验出题",a.jsx(n.Z,{className:"w-5 h-5"})]}),(0,a.jsxs)(g(),{href:"/login",className:"btn-secondary bg-white/10 text-white border-white/20 hover:bg-white/20 text-lg px-6 py-3 inline-flex items-center gap-2",children:["完整体验",a.jsx(l.Z,{className:"w-5 h-5"})]})]})]})})]}),a.jsx("section",{className:"py-8 bg-white",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-4xl font-bold text-primary-600 mb-2",children:e.totalUsers.toLocaleString()}),a.jsx("div",{className:"text-gray-600",children:"参与用户"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-4xl font-bold text-primary-600 mb-2",children:e.questionsAnswered.toLocaleString()}),a.jsx("div",{className:"text-gray-600",children:"题目完成数"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-4xl font-bold text-primary-600 mb-2",children:[e.successRate,"%"]}),a.jsx("div",{className:"text-gray-600",children:"平均正确率"})]})]})})}),a.jsx("section",{className:"py-12 bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[a.jsx("h2",{className:"text-2xl md:text-3xl font-bold text-gray-900 mb-4",children:"游戏特色"}),a.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"通过科学的游戏机制，让防诈骗学习变得有趣且有效"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-3 grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)(g(),{href:"/features/anonymous",className:"card text-center hover:shadow-lg transition-shadow",children:[a.jsx("div",{className:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:a.jsx(i.Z,{className:"w-6 h-6 text-primary-600"})}),a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"匿名参与"}),a.jsx("p",{className:"text-gray-600",children:"无需实名注册，通过邀请码+昵称的方式匿名参与，保护隐私"})]}),(0,a.jsxs)(g(),{href:"/features/category-learning",className:"card text-center hover:shadow-lg transition-shadow",children:[a.jsx("div",{className:"w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:a.jsx(x.Z,{className:"w-6 h-6 text-success-600"})}),a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"分类学习"}),a.jsx("p",{className:"text-gray-600",children:"涵盖求职、租房、网贷、培训等多种诈骗类型，针对性学习"})]}),(0,a.jsxs)(g(),{href:"/features/community-questions",className:"card text-center hover:shadow-lg transition-shadow",children:[a.jsx("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:a.jsx(d.Z,{className:"w-6 h-6 text-purple-600"})}),a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"共建题库"}),a.jsx("p",{className:"text-gray-600",children:"用户可以提交真实案例，经审核后加入题库，共同建设"})]}),(0,a.jsxs)(g(),{href:"/features/leaderboard",className:"card text-center hover:shadow-lg transition-shadow",children:[a.jsx("div",{className:"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:a.jsx(m.Z,{className:"w-6 h-6 text-orange-600"})}),a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"排行榜"}),a.jsx("p",{className:"text-gray-600",children:"每日挑战排行榜，激发学习动力，分享成绩海报"})]}),(0,a.jsxs)(g(),{href:"/features/category-practice",className:"card text-center hover:shadow-lg transition-shadow",children:[a.jsx("div",{className:"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:a.jsx(c.Z,{className:"w-6 h-6 text-red-600"})}),a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"分类刷题"}),a.jsx("p",{className:"text-gray-600",children:"根据个人需求选择特定领域，针对性强化学习"})]}),(0,a.jsxs)(g(),{href:"/features/real-cases",className:"card text-center hover:shadow-lg transition-shadow",children:[a.jsx("div",{className:"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:a.jsx(o.Z,{className:"w-6 h-6 text-indigo-600"})}),a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"真实案例"}),a.jsx("p",{className:"text-gray-600",children:"基于用户真实被骗经历，警示教育效果更佳"})]})]}),a.jsx("div",{className:"lg:col-span-1",children:a.jsx(UserStories,{})})]})]})}),a.jsx("section",{className:"py-20 bg-primary-600",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8",children:[a.jsx("div",{className:"flex justify-center mb-6",children:a.jsx(o.Z,{className:"w-16 h-16 text-yellow-300"})}),a.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-6",children:"提高警惕，远离诈骗"}),a.jsx("p",{className:"text-xl text-blue-100 mb-8",children:"每天只需几分钟，通过答题挑战提升防诈骗能力，保护自己和身边的人"}),(0,a.jsxs)(g(),{href:"/login",className:"btn-primary bg-white text-primary-600 hover:bg-gray-100 text-lg px-8 py-4 inline-flex items-center gap-2",children:["立即开始学习",a.jsx(l.Z,{className:"w-5 h-5"})]})]})}),a.jsx("footer",{className:"bg-gray-900 text-white py-12",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"flex justify-center mb-4",children:a.jsx(i.Z,{className:"w-8 h-8 text-primary-400"})}),a.jsx("h3",{className:"text-xl font-semibold mb-2",children:"大学生防诈挑战游戏"}),a.jsx("p",{className:"text-gray-400 mb-6",children:"让防诈骗学习变得有趣且有效"}),a.jsx("div",{className:"text-sm text-gray-500",children:"\xa9 2024 防诈挑战游戏. 保护大学生远离诈骗陷阱."})]})})})]})}},3982:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>i,default:()=>n});var a=t(5153);let r=(0,a.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/page.tsx`),{__esModule:i,$$typeof:l}=r,c=r.default,n=c}};var s=require("../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[9678,5607,6815,327,943,2035],()=>__webpack_exec__(5875));module.exports=t})();