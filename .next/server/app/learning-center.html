<!DOCTYPE html><html lang="zh-CN"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/97115f26cb754c8c.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-69bfb055b70a1563.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-a0c76284e27c8afa.js" async="" crossorigin=""></script><script src="/_next/static/chunks/472-af003cc8e47f5b1f.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-52f18067e7ca02da.js" async="" crossorigin=""></script><script src="/_next/static/chunks/326-cf5b0283528485c7.js" async=""></script><script src="/_next/static/chunks/app/not-found-5c4df079dd57eb5a.js" async=""></script><script src="/_next/static/chunks/app/learning-center/page-e6aca22e22e77c7c.js" async=""></script><title>大学生防诈挑战游戏</title><meta name="description" content="通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱"/><meta name="author" content="防诈挑战游戏团队"/><meta name="keywords" content="防诈骗,大学生,安全教育,答题游戏"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body><div class="min-h-screen"><div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100"><header class="bg-white shadow-sm border-b"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4"><div class="flex items-center gap-3"><a class="p-2 hover:bg-gray-100 rounded-lg transition-colors" href="/dashboard/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left w-5 h-5 text-gray-600"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg></a><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-book-open w-8 h-8 text-primary-600"><path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path><path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path></svg><div><h1 class="text-2xl font-bold text-gray-900">学习中心</h1><p class="text-sm text-gray-600">防诈骗知识库</p></div></div></div></header><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="grid grid-cols-1 lg:grid-cols-4 gap-8"><div class="lg:col-span-1"><div class="card sticky top-8"><h3 class="text-lg font-semibold text-gray-900 mb-4">诈骗类型</h3><nav class="space-y-2"><button class="w-full flex items-center gap-3 p-3 rounded-lg text-left transition-all bg-primary-100 text-primary-700 border-primary-200"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-briefcase w-6 h-6"><rect width="20" height="14" x="2" y="7" rx="2" ry="2"></rect><path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path></svg><span class="font-medium">求职骗局</span></button><button class="w-full flex items-center gap-3 p-3 rounded-lg text-left transition-all hover:bg-gray-50 text-gray-700"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-home w-6 h-6"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg><span class="font-medium">租房陷阱</span></button><button class="w-full flex items-center gap-3 p-3 rounded-lg text-left transition-all hover:bg-gray-50 text-gray-700"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-credit-card w-6 h-6"><rect width="20" height="14" x="2" y="5" rx="2"></rect><line x1="2" x2="22" y1="10" y2="10"></line></svg><span class="font-medium">网贷陷阱</span></button><button class="w-full flex items-center gap-3 p-3 rounded-lg text-left transition-all hover:bg-gray-50 text-gray-700"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-graduation-cap w-6 h-6"><path d="M22 10v6M2 10l10-5 10 5-10 5z"></path><path d="M6 12v5c3 3 9 3 12 0v-5"></path></svg><span class="font-medium">培训诈骗</span></button><button class="w-full flex items-center gap-3 p-3 rounded-lg text-left transition-all hover:bg-gray-50 text-gray-700"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-phone w-6 h-6"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg><span class="font-medium">电信诈骗</span></button><button class="w-full flex items-center gap-3 p-3 rounded-lg text-left transition-all hover:bg-gray-50 text-gray-700"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield w-6 h-6"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path></svg><span class="font-medium">冒充公检法</span></button><button class="w-full flex items-center gap-3 p-3 rounded-lg text-left transition-all hover:bg-gray-50 text-gray-700"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-6 h-6"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg><span class="font-medium">兼职诈骗</span></button></nav></div></div><div class="lg:col-span-3 space-y-8"><div class="card"><div class="flex items-center gap-3 mb-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-briefcase w-6 h-6"><rect width="20" height="14" x="2" y="7" rx="2" ry="2"></rect><path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path></svg><h2 class="text-2xl font-bold text-gray-900">求职骗局</h2></div><p class="text-gray-600 text-lg">利用求职者急于找工作的心理，通过虚假招聘信息进行诈骗。</p></div><div class="card"><div class="flex items-center gap-2 mb-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle w-6 h-6 text-yellow-500"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path><path d="M12 9v4"></path><path d="M12 17h.01"></path></svg><h3 class="text-xl font-semibold text-gray-900">识别要点</h3></div><ul class="space-y-3"><li class="flex items-start gap-3"><div class="flex-shrink-0 w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center mt-0.5"><span class="text-yellow-600 text-sm font-bold">1</span></div><span class="text-gray-700">正规公司不会要求求职者预先交费</span></li><li class="flex items-start gap-3"><div class="flex-shrink-0 w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center mt-0.5"><span class="text-yellow-600 text-sm font-bold">2</span></div><span class="text-gray-700">警惕&quot;高薪轻松&quot;、&quot;日赚千元&quot;等诱人条件</span></li><li class="flex items-start gap-3"><div class="flex-shrink-0 w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center mt-0.5"><span class="text-yellow-600 text-sm font-bold">3</span></div><span class="text-gray-700">通过官方渠道核实公司信息</span></li><li class="flex items-start gap-3"><div class="flex-shrink-0 w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center mt-0.5"><span class="text-yellow-600 text-sm font-bold">4</span></div><span class="text-gray-700">面试地点应在正规办公场所</span></li></ul></div><div class="card"><h3 class="text-xl font-semibold text-gray-900 mb-4">常见案例</h3><div class="grid grid-cols-1 md:grid-cols-2 gap-4"><div class="bg-red-50 border border-red-200 rounded-lg p-4"><div class="flex items-start gap-3"><div class="flex-shrink-0 w-6 h-6 bg-red-100 rounded-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle w-4 h-4 text-red-600"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path><path d="M12 9v4"></path><path d="M12 17h.01"></path></svg></div><span class="text-red-800 text-sm">虚假招聘要求交&quot;培训费&quot;、&quot;保证金&quot;</span></div></div><div class="bg-red-50 border border-red-200 rounded-lg p-4"><div class="flex items-start gap-3"><div class="flex-shrink-0 w-6 h-6 bg-red-100 rounded-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle w-4 h-4 text-red-600"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path><path d="M12 9v4"></path><path d="M12 17h.01"></path></svg></div><span class="text-red-800 text-sm">冒充知名企业进行电话面试诈骗</span></div></div><div class="bg-red-50 border border-red-200 rounded-lg p-4"><div class="flex items-start gap-3"><div class="flex-shrink-0 w-6 h-6 bg-red-100 rounded-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle w-4 h-4 text-red-600"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path><path d="M12 9v4"></path><path d="M12 17h.01"></path></svg></div><span class="text-red-800 text-sm">以&quot;内推&quot;名义收取&quot;介绍费&quot;</span></div></div><div class="bg-red-50 border border-red-200 rounded-lg p-4"><div class="flex items-start gap-3"><div class="flex-shrink-0 w-6 h-6 bg-red-100 rounded-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle w-4 h-4 text-red-600"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path><path d="M12 9v4"></path><path d="M12 17h.01"></path></svg></div><span class="text-red-800 text-sm">传销组织伪装成正规公司招聘</span></div></div></div></div><div class="card"><div class="flex items-center gap-2 mb-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield w-6 h-6 text-green-500"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path></svg><h3 class="text-xl font-semibold text-gray-900">防范措施</h3></div><div class="grid grid-cols-1 md:grid-cols-2 gap-4"><div class="bg-green-50 border border-green-200 rounded-lg p-4"><div class="flex items-start gap-3"><div class="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield w-4 h-4 text-green-600"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path></svg></div><span class="text-green-800 text-sm">通过企业官网或正规招聘平台投递简历</span></div></div><div class="bg-green-50 border border-green-200 rounded-lg p-4"><div class="flex items-start gap-3"><div class="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield w-4 h-4 text-green-600"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path></svg></div><span class="text-green-800 text-sm">面试前核实公司营业执照和办公地址</span></div></div><div class="bg-green-50 border border-green-200 rounded-lg p-4"><div class="flex items-start gap-3"><div class="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield w-4 h-4 text-green-600"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path></svg></div><span class="text-green-800 text-sm">不向任何招聘方预付费用</span></div></div><div class="bg-green-50 border border-green-200 rounded-lg p-4"><div class="flex items-start gap-3"><div class="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield w-4 h-4 text-green-600"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path></svg></div><span class="text-green-800 text-sm">保持警惕，相信常识判断</span></div></div></div></div><div class="card bg-gradient-to-r from-primary-500 to-purple-600 text-white"><h3 class="text-xl font-bold mb-4">立即行动</h3><p class="text-primary-100 mb-6">学习了<!-- -->求职骗局<!-- -->的防范知识，现在就去挑战相关题目，检验你的学习成果吧！</p><div class="flex flex-col sm:flex-row gap-4"><a class="btn-primary bg-white text-primary-600 hover:bg-gray-100" href="/challenge/">开始挑战</a><a class="btn-secondary bg-white/10 text-white border-white/20 hover:bg-white/20" href="/starter-quiz/">重做入门题</a></div></div></div></div></div></div></div><script src="/_next/static/chunks/webpack-69bfb055b70a1563.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/css/97115f26cb754c8c.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L2\"\n"])</script><script>self.__next_f.push([1,"3:I[3728,[],\"\"]\n5:I[9928,[],\"\"]\n6:I[6954,[],\"\"]\n7:I[7264,[],\"\"]\n8:I[8326,[\"326\",\"static/chunks/326-cf5b0283528485c7.js\",\"160\",\"static/chunks/app/not-found-5c4df079dd57eb5a.js\"],\"\"]\na:I[8297,[],\"\"]\nb:I[6869,[\"326\",\"static/chunks/326-cf5b0283528485c7.js\",\"206\",\"static/chunks/app/learning-center/page-e6aca22e22e77c7c.js\"],\"\"]\n"])</script><script>self.__next_f.push([1,"2:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/97115f26cb754c8c.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L3\",null,{\"buildId\":\"-csu5AGVUyiuSzdvekIsf\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/learning-center/\",\"initialTree\":[\"\",{\"children\":[\"learning-center\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],\"initialHead\":[false,\"$L4\"],\"globalErrorComponent\":\"$5\",\"children\":[null,[\"$\",\"html\",null,{\"lang\":\"zh-CN\",\"children\":[\"$\",\"body\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"min-h-screen\",\"children\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4 sm:px-6 lg:px-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-md w-full text-center\",\"children\":[\"$\",\"div\",null,{\"className\":\"card\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mb-6\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-alert-triangle w-16 h-16 text-yellow-500 mx-auto mb-4\",\"children\":[[\"$\",\"path\",\"c3ski4\",{\"d\":\"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z\"}],[\"$\",\"path\",\"juzpu7\",{\"d\":\"M12 9v4\"}],[\"$\",\"path\",\"p32p05\",{\"d\":\"M12 17h.01\"}],\"$undefined\"]}],[\"$\",\"h1\",null,{\"className\":\"text-6xl font-bold text-gray-900 mb-2\",\"children\":\"404\"}],[\"$\",\"h2\",null,{\"className\":\"text-2xl font-semibold text-gray-700 mb-4\",\"children\":\"页面未找到\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600\",\"children\":\"抱歉，你访问的页面不存在。可能是链接错误或页面已被移除。\"}]]}],[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"btn-primary w-full flex items-center justify-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-home w-5 h-5\",\"children\":[[\"$\",\"path\",\"y5dka4\",{\"d\":\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"}],[\"$\",\"polyline\",\"e2us08\",{\"points\":\"9 22 9 12 15 12 15 22\"}],\"$undefined\"]}],\"返回首页\"]}],[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"btn-secondary w-full flex items-center justify-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-left w-5 h-5\",\"children\":[[\"$\",\"path\",\"1l729n\",{\"d\":\"m12 19-7-7 7-7\"}],[\"$\",\"path\",\"x3x0zl\",{\"d\":\"M19 12H5\"}],\"$undefined\"]}],\"返回上一页\"]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\"children\":[\"$\",\"p\",null,{\"className\":\"text-blue-800 text-sm\",\"children\":[\"💡 \",[\"$\",\"strong\",null,{\"children\":\"提示：\"}],\"如果你是通过链接访问的，请检查链接是否正确。 如果问题持续存在，请联系我们。\"]}]}]]}]}]}],\"notFoundStyles\":[],\"childProp\":{\"current\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"learning-center\",\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"childProp\":{\"current\":[\"$L9\",[\"$\",\"$La\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$b\",\"isStaticGeneration\":true}],null],\"segment\":\"__PAGE__\"},\"styles\":null}],\"segment\":\"learning-center\"},\"styles\":null}]}]}]}],null]}]]\n"])</script><script>self.__next_f.push([1,"4:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"大学生防诈挑战游戏\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱\"}],[\"$\",\"meta\",\"4\",{\"name\":\"author\",\"content\":\"防诈挑战游戏团队\"}],[\"$\",\"meta\",\"5\",{\"name\":\"keywords\",\"content\":\"防诈骗,大学生,安全教育,答题游戏\"}]]\n9:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>