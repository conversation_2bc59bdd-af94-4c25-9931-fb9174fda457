/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/submit-question/page";
exports.ids = ["app/submit-question/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsubmit-question%2Fpage&page=%2Fsubmit-question%2Fpage&appPaths=%2Fsubmit-question%2Fpage&pagePath=private-next-app-dir%2Fsubmit-question%2Fpage.tsx&appDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsubmit-question%2Fpage&page=%2Fsubmit-question%2Fpage&appPaths=%2Fsubmit-question%2Fpage&pagePath=private-next-app-dir%2Fsubmit-question%2Fpage.tsx&appDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'submit-question',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/submit-question/page.tsx */ \"(rsc)/./app/submit-question/page.tsx\")), \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\")), \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/submit-question/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/submit-question/page\",\n        pathname: \"/submit-question\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsubmit-question%2Fpage&page=%2Fsubmit-question%2Fpage&appPaths=%2Fsubmit-question%2Fpage&pagePath=private-next-app-dir%2Fsubmit-question%2Fpage.tsx&appDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp%2Fglobals.css&server=true!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp%2Fglobals.css&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp%2Fsubmit-question%2Fpage.tsx&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp%2Fsubmit-question%2Fpage.tsx&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/submit-question/page.tsx */ \"(ssr)/./app/submit-question/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRnolMkZEZXNrdG9wJTJGdnNjb2RlJTJGJUU5JTk4JUIyJUU4JUFGJTg4JUU2JThDJTkxJUU2JTg4JTk4JUU2JUI4JUI4JUU2JTg4JThGJTJGYXBwJTJGc3VibWl0LXF1ZXN0aW9uJTJGcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJhdWQtcHJldmVudGlvbi1nYW1lLz84MDU0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvei9EZXNrdG9wL3ZzY29kZS/pmLLor4jmjJHmiJjmuLjmiI8vYXBwL3N1Ym1pdC1xdWVzdGlvbi9wYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp%2Fsubmit-question%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRnolMkZEZXNrdG9wJTJGdnNjb2RlJTJGJUU5JTk4JUIyJUU4JUFGJTg4JUU2JThDJTkxJUU2JTg4JTk4JUU2JUI4JUI4JUU2JTg4JThGJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZsaW5rLmpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2ZyYXVkLXByZXZlbnRpb24tZ2FtZS8/OTVjNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL3ovRGVza3RvcC92c2NvZGUv6Ziy6K+I5oyR5oiY5ri45oiPL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2xpbmsuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/submit-question/page.tsx":
/*!**************************************!*\
  !*** ./app/submit-question/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SubmitQuestionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCircle,Clock,Plus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCircle,Clock,Plus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCircle,Clock,Plus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCircle,Clock,Plus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,CheckCircle,Clock,Plus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/types */ \"(ssr)/./types/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction SubmitQuestionPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        options: [\n            \"\",\n            \"\",\n            \"\",\n            \"\"\n        ],\n        correctAnswer: 0,\n        category: \"job-fraud\",\n        explanation: \"\",\n        source: \"\",\n        optionExplanations: [\n            \"\",\n            \"\",\n            \"\",\n            \"\"\n        ]\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSubmitted, setIsSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 检查用户登录状态（Demo 模式允许未登录用户体验）\n        const userDataStr = localStorage.getItem(\"userData\");\n        if (userDataStr) {\n            setUserData(JSON.parse(userDataStr));\n        } else {\n            // 为未登录用户创建临时身份\n            setUserData({\n                id: \"demo_user\",\n                nickname: \"体验用户\",\n                contributionScore: 0\n            });\n        }\n    }, [\n        router\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n    };\n    const handleOptionChange = (index, value)=>{\n        const newOptions = [\n            ...formData.options\n        ];\n        newOptions[index] = value;\n        setFormData((prev)=>({\n                ...prev,\n                options: newOptions\n            }));\n        if (errors[`option${index}`]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [`option${index}`]: \"\"\n                }));\n        }\n    };\n    const handleOptionExplanationChange = (index, value)=>{\n        const newExplanations = [\n            ...formData.optionExplanations\n        ];\n        newExplanations[index] = value;\n        setFormData((prev)=>({\n                ...prev,\n                optionExplanations: newExplanations\n            }));\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.title.trim()) {\n            newErrors.title = \"请输入题目内容\";\n        } else if (formData.title.length < 10) {\n            newErrors.title = \"题目内容至少需要10个字符\";\n        }\n        formData.options.forEach((option, index)=>{\n            if (!option.trim()) {\n                newErrors[`option${index}`] = `请输入选项${String.fromCharCode(65 + index)}的内容`;\n            }\n        });\n        if (!formData.explanation.trim()) {\n            newErrors.explanation = \"请输入详细解析\";\n        } else if (formData.explanation.length < 20) {\n            newErrors.explanation = \"详细解析至少需要20个字符\";\n        }\n        if (!formData.source.trim()) {\n            newErrors.source = \"请输入案例来源\";\n        }\n        formData.optionExplanations.forEach((explanation, index)=>{\n            if (!explanation.trim()) {\n                newErrors[`optionExplanation${index}`] = `请输入选项${String.fromCharCode(65 + index)}的解析`;\n            }\n        });\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // 模拟API调用\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // 保存提交记录\n            const submission = {\n                ...formData,\n                id: `submission_${Date.now()}`,\n                authorId: userData.id,\n                authorNickname: userData.nickname,\n                status: \"pending\",\n                submittedAt: new Date().toISOString()\n            };\n            const existingSubmissions = JSON.parse(localStorage.getItem(\"questionSubmissions\") || \"[]\");\n            existingSubmissions.push(submission);\n            localStorage.setItem(\"questionSubmissions\", JSON.stringify(existingSubmissions));\n            // 增加用户贡献值\n            const updatedUserData = {\n                ...userData,\n                contributionScore: (userData.contributionScore || 0) + 1\n            };\n            localStorage.setItem(\"userData\", JSON.stringify(updatedUserData));\n            setUserData(updatedUserData);\n            setIsSubmitted(true);\n        } catch (error) {\n            setErrors({\n                general: \"提交失败，请重试\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    if (!userData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, this);\n    }\n    if (isSubmitted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-16 h-16 text-success-500 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: \"提交成功！\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"感谢你的贡献！你的题目已提交审核，通过后将加入题库供其他用户挑战。\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-success-50 border border-success-200 rounded-lg p-4 mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-success-800 text-sm\",\n                                children: [\n                                    \"\\uD83C\\uDF89 你获得了 1 贡献值！当前贡献值：\",\n                                    userData.contributionScore\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/dashboard\",\n                                    className: \"btn-primary\",\n                                    children: \"返回主页\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setIsSubmitted(false);\n                                        setFormData({\n                                            title: \"\",\n                                            options: [\n                                                \"\",\n                                                \"\",\n                                                \"\",\n                                                \"\"\n                                            ],\n                                            correctAnswer: 0,\n                                            category: \"job-fraud\",\n                                            explanation: \"\",\n                                            source: \"\",\n                                            optionExplanations: [\n                                                \"\",\n                                                \"\",\n                                                \"\",\n                                                \"\"\n                                            ]\n                                        });\n                                    },\n                                    className: \"btn-secondary\",\n                                    children: \"继续提交题目\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                lineNumber: 177,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/dashboard\",\n                                className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5 text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-8 h-8 text-success-600\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"提交题目\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"分享真实案例，帮助更多人防范诈骗\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card mb-8 bg-blue-50 border border-blue-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-6 h-6 text-blue-600 mt-1\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-blue-900 mb-2\",\n                                            children: \"提交须知\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-blue-800 text-sm space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• 请基于真实案例或常见诈骗手段出题\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• 题目内容应具有教育意义，帮助他人识别诈骗\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• 所有选项都需要提供详细解析说明\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• 提交后将进入审核流程，通过后加入正式题库\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• 成功贡献题目可获得贡献值奖励\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-8\",\n                        children: [\n                            errors.general && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-danger-50 border border-danger-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-5 h-5 text-danger-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-danger-800\",\n                                            children: errors.general\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                        children: \"基本信息\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"诈骗类型 *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.category,\n                                                        onChange: (e)=>handleInputChange(\"category\", e.target.value),\n                                                        className: \"input-field\",\n                                                        children: Object.entries(_types__WEBPACK_IMPORTED_MODULE_4__.CATEGORY_LABELS).map(([key, label])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: key,\n                                                                children: label\n                                                            }, key, false, {\n                                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"题目内容 *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: formData.title,\n                                                        onChange: (e)=>handleInputChange(\"title\", e.target.value),\n                                                        className: `input-field h-24 ${errors.title ? \"border-danger-300\" : \"\"}`,\n                                                        placeholder: \"请描述一个具体的诈骗场景，让用户判断应该如何应对...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-danger-600\",\n                                                        children: errors.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"案例来源 *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.source,\n                                                        onChange: (e)=>handleInputChange(\"source\", e.target.value),\n                                                        className: `input-field ${errors.source ? \"border-danger-300\" : \"\"}`,\n                                                        placeholder: \"例如：亲身经历、新闻报道、朋友遭遇等\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.source && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-danger-600\",\n                                                        children: errors.source\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                        children: \"答案选项\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: formData.options.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border border-gray-200 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-shrink-0 w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-primary-600 font-semibold\",\n                                                                    children: String.fromCharCode(65 + index)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                                    lineNumber: 326,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"correctAnswer\",\n                                                                checked: formData.correctAnswer === index,\n                                                                onChange: ()=>handleInputChange(\"correctAnswer\", index),\n                                                                className: \"text-primary-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"正确答案\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: option,\n                                                                        onChange: (e)=>handleOptionChange(index, e.target.value),\n                                                                        className: `input-field ${errors[`option${index}`] ? \"border-danger-300\" : \"\"}`,\n                                                                        placeholder: `选项${String.fromCharCode(65 + index)}内容`\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    errors[`option${index}`] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"mt-1 text-sm text-danger-600\",\n                                                                        children: errors[`option${index}`]\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                                        lineNumber: 352,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                        value: formData.optionExplanations[index],\n                                                                        onChange: (e)=>handleOptionExplanationChange(index, e.target.value),\n                                                                        className: `input-field h-20 ${errors[`optionExplanation${index}`] ? \"border-danger-300\" : \"\"}`,\n                                                                        placeholder: `选项${String.fromCharCode(65 + index)}的解析说明`\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                                        lineNumber: 357,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    errors[`optionExplanation${index}`] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"mt-1 text-sm text-danger-600\",\n                                                                        children: errors[`optionExplanation${index}`]\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                                        lineNumber: 364,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                        children: \"详细解析\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"题目解析 *\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.explanation,\n                                                onChange: (e)=>handleInputChange(\"explanation\", e.target.value),\n                                                className: `input-field h-32 ${errors.explanation ? \"border-danger-300\" : \"\"}`,\n                                                placeholder: \"请详细解释为什么这样选择是正确的，以及这类诈骗的特点和防范方法...\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.explanation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-sm text-danger-600\",\n                                                children: errors.explanation\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/dashboard\",\n                                        className: \"btn-secondary\",\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        className: \"btn-success flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"提交中...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"提交题目\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx\",\n        lineNumber: 218,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/submit-question/page.tsx\n");

/***/ }),

/***/ "(ssr)/./types/index.ts":
/*!************************!*\
  !*** ./types/index.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CATEGORY_LABELS: () => (/* binding */ CATEGORY_LABELS)\n/* harmony export */ });\n// 用户类型\nconst CATEGORY_LABELS = {\n    \"job-fraud\": \"求职骗局\",\n    \"rental-scam\": \"租房陷阱\",\n    \"loan-trap\": \"网贷陷阱\",\n    \"training-scam\": \"培训诈骗\",\n    \"telecom-fraud\": \"电信诈骗\",\n    \"fake-authority\": \"冒充公检法\",\n    \"part-time-scam\": \"兼职诈骗\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi90eXBlcy9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsT0FBTztBQXlDQSxNQUFNQSxrQkFBb0Q7SUFDL0QsYUFBYTtJQUNiLGVBQWU7SUFDZixhQUFhO0lBQ2IsaUJBQWlCO0lBQ2pCLGlCQUFpQjtJQUNqQixrQkFBa0I7SUFDbEIsa0JBQWtCO0FBQ3BCLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mcmF1ZC1wcmV2ZW50aW9uLWdhbWUvLi90eXBlcy9pbmRleC50cz9kZjJhIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIOeUqOaIt+exu+Wei1xuZXhwb3J0IGludGVyZmFjZSBVc2VyIHtcbiAgaWQ6IHN0cmluZ1xuICBpbnZpdGVDb2RlOiBzdHJpbmdcbiAgbmlja25hbWU6IHN0cmluZ1xuICBjcmVhdGVkQXQ6IERhdGVcbiAgbGFzdENoYWxsZW5nZURhdGU/OiBEYXRlXG4gIGNvbnRyaWJ1dGlvblNjb3JlOiBudW1iZXJcbn1cblxuLy8g6aKY55uu57G75Z6LXG5leHBvcnQgaW50ZXJmYWNlIFF1ZXN0aW9uIHtcbiAgaWQ6IHN0cmluZ1xuICB0aXRsZTogc3RyaW5nXG4gIG9wdGlvbnM6IFF1ZXN0aW9uT3B0aW9uW11cbiAgY29ycmVjdEFuc3dlcjogbnVtYmVyXG4gIGNhdGVnb3J5OiBRdWVzdGlvbkNhdGVnb3J5XG4gIGRpZmZpY3VsdHk6ICdiYXNpYycgfCAnYWR2YW5jZWQnXG4gIGV4cGxhbmF0aW9uOiBzdHJpbmdcbiAgc291cmNlPzogc3RyaW5nXG4gIGF1dGhvcklkPzogc3RyaW5nXG4gIHN0YXR1czogJ3BlbmRpbmcnIHwgJ2FwcHJvdmVkJyB8ICdyZWplY3RlZCdcbiAgY3JlYXRlZEF0OiBEYXRlXG4gIGFwcHJvdmVkQXQ/OiBEYXRlXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUXVlc3Rpb25PcHRpb24ge1xuICB0ZXh0OiBzdHJpbmdcbiAgZXhwbGFuYXRpb246IHN0cmluZ1xufVxuXG4vLyDpopjnm67liIbnsbtcbmV4cG9ydCB0eXBlIFF1ZXN0aW9uQ2F0ZWdvcnkgPSBcbiAgfCAnam9iLWZyYXVkJyAgICAgIC8vIOaxguiBjOmql+WxgFxuICB8ICdyZW50YWwtc2NhbScgICAgLy8g56ef5oi/6Zm36ZixXG4gIHwgJ2xvYW4tdHJhcCcgICAgICAvLyDnvZHotLfpmbfpmLFcbiAgfCAndHJhaW5pbmctc2NhbScgIC8vIOWfueiureiviOmql1xuICB8ICd0ZWxlY29tLWZyYXVkJyAgLy8g55S15L+h6K+I6aqXXG4gIHwgJ2Zha2UtYXV0aG9yaXR5JyAvLyDlhpLlhYXlhazmo4Dms5VcbiAgfCAncGFydC10aW1lLXNjYW0nIC8vIOWFvOiBjOiviOmql1xuXG5leHBvcnQgY29uc3QgQ0FURUdPUllfTEFCRUxTOiBSZWNvcmQ8UXVlc3Rpb25DYXRlZ29yeSwgc3RyaW5nPiA9IHtcbiAgJ2pvYi1mcmF1ZCc6ICfmsYLogYzpqpflsYAnLFxuICAncmVudGFsLXNjYW0nOiAn56ef5oi/6Zm36ZixJyxcbiAgJ2xvYW4tdHJhcCc6ICfnvZHotLfpmbfpmLEnLFxuICAndHJhaW5pbmctc2NhbSc6ICfln7norq3or4jpqpcnLFxuICAndGVsZWNvbS1mcmF1ZCc6ICfnlLXkv6Hor4jpqpcnLFxuICAnZmFrZS1hdXRob3JpdHknOiAn5YaS5YWF5YWs5qOA5rOVJyxcbiAgJ3BhcnQtdGltZS1zY2FtJzogJ+WFvOiBjOiviOmqlycsXG59XG5cbi8vIOaMkeaImOiusOW9lVxuZXhwb3J0IGludGVyZmFjZSBDaGFsbGVuZ2VSZWNvcmQge1xuICBpZDogc3RyaW5nXG4gIHVzZXJJZDogc3RyaW5nXG4gIGNhdGVnb3J5PzogUXVlc3Rpb25DYXRlZ29yeVxuICBzY29yZTogbnVtYmVyXG4gIHRpbWVVc2VkOiBudW1iZXIgLy8g56eSXG4gIHF1ZXN0aW9uc0Fuc3dlcmVkOiBudW1iZXJcbiAgY29ycmVjdEFuc3dlcnM6IG51bWJlclxuICBjcmVhdGVkQXQ6IERhdGVcbiAgYW5zd2VyczogQ2hhbGxlbmdlQW5zd2VyW11cbn1cblxuZXhwb3J0IGludGVyZmFjZSBDaGFsbGVuZ2VBbnN3ZXIge1xuICBxdWVzdGlvbklkOiBzdHJpbmdcbiAgc2VsZWN0ZWRBbnN3ZXI6IG51bWJlclxuICBpc0NvcnJlY3Q6IGJvb2xlYW5cbiAgdGltZVVzZWQ6IG51bWJlclxufVxuXG4vLyDmjpLooYzmppzmnaHnm65cbmV4cG9ydCBpbnRlcmZhY2UgTGVhZGVyYm9hcmRFbnRyeSB7XG4gIHJhbms6IG51bWJlclxuICBuaWNrbmFtZTogc3RyaW5nXG4gIHNjb3JlOiBudW1iZXJcbiAgdGltZVVzZWQ6IG51bWJlclxuICBjYXRlZ29yeT86IFF1ZXN0aW9uQ2F0ZWdvcnlcbiAgY3JlYXRlZEF0OiBEYXRlXG59XG5cbi8vIOaIkOe7qea1t+aKpeaVsOaNrlxuZXhwb3J0IGludGVyZmFjZSBTY29yZVBvc3RlciB7XG4gIG5pY2tuYW1lOiBzdHJpbmdcbiAgc2NvcmU6IG51bWJlclxuICB0aW1lVXNlZDogbnVtYmVyXG4gIGNhdGVnb3J5PzogUXVlc3Rpb25DYXRlZ29yeVxuICByYW5rPzogbnVtYmVyXG4gIGRhdGU6IERhdGVcbiAgdGVtcGxhdGU6ICdjbGFzc2ljJyB8ICdtb2Rlcm4nIHwgJ21pbmltYWwnXG59XG4iXSwibmFtZXMiOlsiQ0FURUdPUllfTEFCRUxTIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./types/index.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"11b37adaa9c3\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mcmF1ZC1wcmV2ZW50aW9uLWdhbWUvLi9hcHAvZ2xvYmFscy5jc3M/ODJlZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjExYjM3YWRhYTljM1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: \"大学生防诈挑战游戏\",\n    description: \"通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱\",\n    keywords: \"防诈骗,大学生,安全教育,答题游戏\",\n    authors: [\n        {\n            name: \"防诈挑战游戏团队\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxVQUFVO0lBQ1ZDLFNBQVM7UUFBQztZQUFFQyxNQUFNO1FBQVc7S0FBRTtBQUNqQyxFQUFDO0FBRU0sTUFBTUMsV0FBVztJQUN0QkMsT0FBTztJQUNQQyxjQUFjO0FBQ2hCLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7c0JBQ0MsNEVBQUNDO2dCQUFJQyxXQUFVOzBCQUNaTDs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJhdWQtcHJldmVudGlvbi1nYW1lLy4vYXBwL2xheW91dC50c3g/OTk4OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICflpKflrabnlJ/pmLLor4jmjJHmiJjmuLjmiI8nLFxuICBkZXNjcmlwdGlvbjogJ+mAmui/h+etlOmimOaMkeaImOaPkOWNh+mYsuiviOmql+aEj+ivhu+8jOS/neaKpOiHquW3sei/nOemu+WQhOenjeiviOmql+mZt+mYsScsXG4gIGtleXdvcmRzOiAn6Ziy6K+I6aqXLOWkp+WtpueUnyzlronlhajmlZnogrIs562U6aKY5ri45oiPJyxcbiAgYXV0aG9yczogW3sgbmFtZTogJ+mYsuiviOaMkeaImOa4uOaIj+WboumYnycgfV0sXG59XG5cbmV4cG9ydCBjb25zdCB2aWV3cG9ydCA9IHtcbiAgd2lkdGg6ICdkZXZpY2Utd2lkdGgnLFxuICBpbml0aWFsU2NhbGU6IDEsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHk+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuXCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJrZXl3b3JkcyIsImF1dGhvcnMiLCJuYW1lIiwidmlld3BvcnQiLCJ3aWR0aCIsImluaXRpYWxTY2FsZSIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Home!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Home!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Home!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-16 h-16 text-yellow-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 10,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-6xl font-bold text-gray-900 mb-2\",\n                                children: \"404\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 11,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-700 mb-4\",\n                                children: \"页面未找到\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 12,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"抱歉，你访问的页面不存在。可能是链接错误或页面已被移除。\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 13,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"btn-primary w-full flex items-center justify-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"返回首页\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"btn-secondary w-full flex items-center justify-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"返回上一页\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-800 text-sm\",\n                            children: [\n                                \"\\uD83D\\uDCA1 \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"提示：\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 18\n                                }, this),\n                                \"如果你是通过链接访问的，请检查链接是否正确。 如果问题持续存在，请联系我们。\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                lineNumber: 8,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./app/submit-question/page.tsx":
/*!**************************************!*\
  !*** ./app/submit-question/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsubmit-question%2Fpage&page=%2Fsubmit-question%2Fpage&appPaths=%2Fsubmit-question%2Fpage&pagePath=private-next-app-dir%2Fsubmit-question%2Fpage.tsx&appDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();