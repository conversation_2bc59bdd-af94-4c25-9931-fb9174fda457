(()=>{var e={};e.id=9736,e.ids=[9736],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},6266:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=s(7096),n=s(6132),r=s(7284),i=s.n(r),l=s(2564),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c=["",{children:["submit-question",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,3374)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],d=["/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx"],x="/submit-question/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/submit-question/page",pathname:"/submit-question",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},4386:(e,t,s)=>{Promise.resolve().then(s.bind(s,3409))},3409:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>SubmitQuestionPage});var a=s(784),n=s(9885),r=s(2032),i=s(9803),l=s(2385),o=s(4409),c=s(5441),d=s(1440),x=s.n(d),m=s(7114),p=s(3165);function SubmitQuestionPage(){let e=(0,m.useRouter)(),[t,s]=(0,n.useState)(null),[d,u]=(0,n.useState)({title:"",options:["","","",""],correctAnswer:0,category:"job-fraud",explanation:"",source:"",optionExplanations:["","","",""]}),[h,g]=(0,n.useState)({}),[b,j]=(0,n.useState)(!1),[f,N]=(0,n.useState)(!1);(0,n.useEffect)(()=>{let e=localStorage.getItem("userData");e?s(JSON.parse(e)):s({id:"demo_user",nickname:"体验用户",contributionScore:0})},[e]);let handleInputChange=(e,t)=>{u(s=>({...s,[e]:t})),h[e]&&g(t=>({...t,[e]:""}))},handleOptionChange=(e,t)=>{let s=[...d.options];s[e]=t,u(e=>({...e,options:s})),h[`option${e}`]&&g(t=>({...t,[`option${e}`]:""}))},handleOptionExplanationChange=(e,t)=>{let s=[...d.optionExplanations];s[e]=t,u(e=>({...e,optionExplanations:s}))},validateForm=()=>{let e={};return d.title.trim()?d.title.length<10&&(e.title="题目内容至少需要10个字符"):e.title="请输入题目内容",d.options.forEach((t,s)=>{t.trim()||(e[`option${s}`]=`请输入选项${String.fromCharCode(65+s)}的内容`)}),d.explanation.trim()?d.explanation.length<20&&(e.explanation="详细解析至少需要20个字符"):e.explanation="请输入详细解析",d.source.trim()||(e.source="请输入案例来源"),d.optionExplanations.forEach((t,s)=>{t.trim()||(e[`optionExplanation${s}`]=`请输入选项${String.fromCharCode(65+s)}的解析`)}),g(e),0===Object.keys(e).length},handleSubmit=async e=>{if(e.preventDefault(),validateForm()){j(!0);try{await new Promise(e=>setTimeout(e,2e3));let e={...d,id:`submission_${Date.now()}`,authorId:t.id,authorNickname:t.nickname,status:"pending",submittedAt:new Date().toISOString()},a=JSON.parse(localStorage.getItem("questionSubmissions")||"[]");a.push(e),localStorage.setItem("questionSubmissions",JSON.stringify(a));let n={...t,contributionScore:(t.contributionScore||0)+1};localStorage.setItem("userData",JSON.stringify(n)),s(n),N(!0)}catch(e){g({general:"提交失败，请重试"})}finally{j(!1)}}};return t?f?a.jsx("div",{className:"min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8",children:a.jsx("div",{className:"max-w-md w-full",children:(0,a.jsxs)("div",{className:"card text-center",children:[a.jsx(r.Z,{className:"w-16 h-16 text-success-500 mx-auto mb-4"}),a.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"提交成功！"}),a.jsx("p",{className:"text-gray-600 mb-6",children:"感谢你的贡献！你的题目已提交审核，通过后将加入题库供其他用户挑战。"}),a.jsx("div",{className:"bg-success-50 border border-success-200 rounded-lg p-4 mb-6",children:(0,a.jsxs)("p",{className:"text-success-800 text-sm",children:["\uD83C\uDF89 你获得了 1 贡献值！当前贡献值：",t.contributionScore]})}),(0,a.jsxs)("div",{className:"flex flex-col gap-3",children:[a.jsx(x(),{href:"/dashboard",className:"btn-primary",children:"返回主页"}),a.jsx("button",{onClick:()=>{N(!1),u({title:"",options:["","","",""],correctAnswer:0,category:"job-fraud",explanation:"",source:"",optionExplanations:["","","",""]})},className:"btn-secondary",children:"继续提交题目"})]})]})})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[a.jsx("header",{className:"bg-white shadow-sm border-b",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(x(),{href:"/dashboard",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(i.Z,{className:"w-5 h-5 text-gray-600"})}),a.jsx(l.Z,{className:"w-8 h-8 text-success-600"}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"提交题目"}),a.jsx("p",{className:"text-sm text-gray-600",children:"分享真实案例，帮助更多人防范诈骗"})]})]})})}),(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[a.jsx("div",{className:"card mb-8 bg-blue-50 border border-blue-200",children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[a.jsx(o.Z,{className:"w-6 h-6 text-blue-600 mt-1"}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-blue-900 mb-2",children:"提交须知"}),(0,a.jsxs)("ul",{className:"text-blue-800 text-sm space-y-1",children:[a.jsx("li",{children:"• 请基于真实案例或常见诈骗手段出题"}),a.jsx("li",{children:"• 题目内容应具有教育意义，帮助他人识别诈骗"}),a.jsx("li",{children:"• 所有选项都需要提供详细解析说明"}),a.jsx("li",{children:"• 提交后将进入审核流程，通过后加入正式题库"}),a.jsx("li",{children:"• 成功贡献题目可获得贡献值奖励"})]})]})]})}),(0,a.jsxs)("form",{onSubmit:handleSubmit,className:"space-y-8",children:[h.general&&a.jsx("div",{className:"bg-danger-50 border border-danger-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(o.Z,{className:"w-5 h-5 text-danger-600"}),a.jsx("span",{className:"text-danger-800",children:h.general})]})}),(0,a.jsxs)("div",{className:"card",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-6",children:"基本信息"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"诈骗类型 *"}),a.jsx("select",{value:d.category,onChange:e=>handleInputChange("category",e.target.value),className:"input-field",children:Object.entries(p.H).map(([e,t])=>a.jsx("option",{value:e,children:t},e))})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"题目内容 *"}),a.jsx("textarea",{value:d.title,onChange:e=>handleInputChange("title",e.target.value),className:`input-field h-24 ${h.title?"border-danger-300":""}`,placeholder:"请描述一个具体的诈骗场景，让用户判断应该如何应对..."}),h.title&&a.jsx("p",{className:"mt-1 text-sm text-danger-600",children:h.title})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"案例来源 *"}),a.jsx("input",{type:"text",value:d.source,onChange:e=>handleInputChange("source",e.target.value),className:`input-field ${h.source?"border-danger-300":""}`,placeholder:"例如：亲身经历、新闻报道、朋友遭遇等"}),h.source&&a.jsx("p",{className:"mt-1 text-sm text-danger-600",children:h.source})]})]})]}),(0,a.jsxs)("div",{className:"card",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-6",children:"答案选项"}),a.jsx("div",{className:"space-y-6",children:d.options.map((e,t)=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[a.jsx("div",{className:"flex-shrink-0 w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center",children:a.jsx("span",{className:"text-primary-600 font-semibold",children:String.fromCharCode(65+t)})}),a.jsx("input",{type:"radio",name:"correctAnswer",checked:d.correctAnswer===t,onChange:()=>handleInputChange("correctAnswer",t),className:"text-primary-600"}),a.jsx("label",{className:"text-sm font-medium text-gray-700",children:"正确答案"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[a.jsx("input",{type:"text",value:e,onChange:e=>handleOptionChange(t,e.target.value),className:`input-field ${h[`option${t}`]?"border-danger-300":""}`,placeholder:`选项${String.fromCharCode(65+t)}内容`}),h[`option${t}`]&&a.jsx("p",{className:"mt-1 text-sm text-danger-600",children:h[`option${t}`]})]}),(0,a.jsxs)("div",{children:[a.jsx("textarea",{value:d.optionExplanations[t],onChange:e=>handleOptionExplanationChange(t,e.target.value),className:`input-field h-20 ${h[`optionExplanation${t}`]?"border-danger-300":""}`,placeholder:`选项${String.fromCharCode(65+t)}的解析说明`}),h[`optionExplanation${t}`]&&a.jsx("p",{className:"mt-1 text-sm text-danger-600",children:h[`optionExplanation${t}`]})]})]})]},t))})]}),(0,a.jsxs)("div",{className:"card",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-6",children:"详细解析"}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"题目解析 *"}),a.jsx("textarea",{value:d.explanation,onChange:e=>handleInputChange("explanation",e.target.value),className:`input-field h-32 ${h.explanation?"border-danger-300":""}`,placeholder:"请详细解释为什么这样选择是正确的，以及这类诈骗的特点和防范方法..."}),h.explanation&&a.jsx("p",{className:"mt-1 text-sm text-danger-600",children:h.explanation})]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx(x(),{href:"/dashboard",className:"btn-secondary",children:"取消"}),a.jsx("button",{type:"submit",disabled:b,className:"btn-success flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed",children:b?(0,a.jsxs)(a.Fragment,{children:[a.jsx(c.Z,{className:"w-5 h-5 animate-spin"}),"提交中..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(l.Z,{className:"w-5 h-5"}),"提交题目"]})})]})]})]})]}):a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"})})}},3165:(e,t,s)=>{"use strict";s.d(t,{H:()=>a});let a={"job-fraud":"求职骗局","rental-scam":"租房陷阱","loan-trap":"网贷陷阱","training-scam":"培训诈骗","telecom-fraud":"电信诈骗","fake-authority":"冒充公检法","part-time-scam":"兼职诈骗"}},3374:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>r,default:()=>o});var a=s(5153);let n=(0,a.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/submit-question/page.tsx`),{__esModule:r,$$typeof:i}=n,l=n.default,o=l}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[9678,5607,1563,2035],()=>__webpack_exec__(6266));module.exports=s})();