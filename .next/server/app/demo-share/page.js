(()=>{var e={};e.id=7061,e.ids=[7061],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},3391:(e,t,l)=>{"use strict";l.r(t),l.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>c,routeModule:()=>f,tree:()=>d});var a=l(7096),s=l(6132),r=l(7284),i=l.n(r),o=l(2564),n={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);l.d(t,n);let d=["",{children:["demo-share",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(l.bind(l,8220)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(l.bind(l,5345)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(l.bind(l,4293)),"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx"]}],c=["/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx"],x="/demo-share/page",m={require:l,loadChunk:()=>Promise.resolve()},f=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/demo-share/page",pathname:"/demo-share",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},128:(e,t,l)=>{Promise.resolve().then(l.bind(l,3768))},3768:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>DemoSharePage});var a=l(784),s=l(9885),r=l(9803),i=l(6386),o=l(7439),n=l(5574),d=l(1440),c=l.n(d),x=l(7114);function DemoSharePage(){let e=(0,x.useRouter)(),t=(0,s.useRef)(null),[l,d]=(0,s.useState)(null),[m,f]=(0,s.useState)("classic"),[p,h]=(0,s.useState)(!1);(0,s.useEffect)(()=>{let t=localStorage.getItem("demoResult");if(!t){e.push("/demo-quiz");return}d(JSON.parse(t))},[e]);let formatTime=e=>`${Math.floor(e/60)}:${(e%60).toString().padStart(2,"0")}`,generatePoster=async()=>{if(!t.current||!l)return;h(!0);let e=t.current,a=e.getContext("2d");if(a){switch(e.width=600,e.height=800,m){case"classic":drawClassicTemplate(a,e.width,e.height);break;case"modern":drawModernTemplate(a,e.width,e.height);break;case"minimal":drawMinimalTemplate(a,e.width,e.height)}h(!1)}},drawClassicTemplate=(e,t,a)=>{let s=e.createLinearGradient(0,0,0,a);s.addColorStop(0,"#3B82F6"),s.addColorStop(1,"#1E40AF"),e.fillStyle=s,e.fillRect(0,0,t,a),e.fillStyle="#FFFFFF",e.font="bold 36px Arial",e.textAlign="center",e.fillText("防诈挑战游戏",t/2,80),e.font="bold 24px Arial",e.fillText("免费体验版",t/2,120),e.fillStyle="rgba(255, 255, 255, 0.95)",e.roundRect(50,180,t-100,350,20),e.fill(),e.fillStyle="#1F2937",e.font="bold 48px Arial",e.fillText(`${l.score}/${l.total}`,t/2,280),e.font="24px Arial",e.fillText("正确题数",t/2,310),e.font="bold 32px Arial",e.fillText(`${Math.round(l.score/l.total*100)}%`,t/2,370),e.font="20px Arial",e.fillText("正确率",t/2,400),e.font="bold 24px Arial",e.fillText(formatTime(l.timeUsed),t/2,450),e.font="18px Arial",e.fillText("用时",t/2,475),e.font="18px Arial",e.fillStyle="#6B7280",e.fillText(new Date(l.completedAt).toLocaleDateString(),t/2,510),e.fillStyle="#FFFFFF",e.font="20px Arial",e.fillText("提升防诈骗意识，保护自己远离陷阱",t/2,650),e.font="16px Arial",e.fillText("扫码体验完整版功能",t/2,700)},drawModernTemplate=(e,t,a)=>{e.fillStyle="#F8FAFC",e.fillRect(0,0,t,a);let s=e.createLinearGradient(0,0,t,0);s.addColorStop(0,"#8B5CF6"),s.addColorStop(1,"#EC4899"),e.fillStyle=s,e.fillRect(0,0,t,150),e.fillStyle="#FFFFFF",e.font="bold 32px Arial",e.textAlign="center",e.fillText("\uD83D\uDEE1️ 防诈挑战成绩",t/2,60),e.fillText("(体验版)",t/2,110),e.fillStyle="#FFFFFF",e.shadowColor="rgba(0, 0, 0, 0.1)",e.shadowBlur=20,e.roundRect(40,200,t-80,300,15),e.fill(),e.shadowBlur=0,e.fillStyle="#1F2937",e.font="bold 56px Arial",e.fillText(`${l.score}/${l.total}`,t/2,300),e.font="24px Arial",e.fillStyle="#6B7280",e.fillText("正确题数",t/2,330),e.fillStyle="#3B82F6",e.font="bold 28px Arial",e.fillText(`${Math.round(l.score/l.total*100)}%`,t/2,390),e.fillStyle="#6B7280",e.font="20px Arial",e.fillText("正确率",t/2,415),e.fillStyle="#8B5CF6",e.font="bold 22px Arial",e.fillText(formatTime(l.timeUsed),t/2,460),e.fillStyle="#6B7280",e.font="18px Arial",e.fillText("用时",t/2,485)},drawMinimalTemplate=(e,t,a)=>{e.fillStyle="#FFFFFF",e.fillRect(0,0,t,a),e.strokeStyle="#E5E7EB",e.lineWidth=2,e.strokeRect(20,20,t-40,a-40),e.fillStyle="#1F2937",e.font="bold 28px Arial",e.textAlign="center",e.fillText("防诈挑战成绩单",t/2,100),e.font="20px Arial",e.fillStyle="#6B7280",e.fillText("(免费体验版)",t/2,130),e.strokeStyle="#D1D5DB",e.lineWidth=1,e.beginPath(),e.moveTo(80,160),e.lineTo(t-80,160),e.stroke(),e.font="bold 48px Arial",e.fillStyle="#059669",e.fillText(`${l.score}/${l.total}`,t/2,250),e.font="20px Arial",e.fillStyle="#6B7280",e.fillText("正确题数",t/2,280),e.font="bold 32px Arial",e.fillStyle="#3B82F6",e.fillText(`${Math.round(l.score/l.total*100)}%`,t/2,340),e.font="18px Arial",e.fillStyle="#6B7280",e.fillText("正确率",t/2,365),e.font="16px Arial",e.fillText(new Date(l.completedAt).toLocaleDateString(),t/2,450),e.font="18px Arial",e.fillStyle="#1F2937",e.fillText("继续学习，提升防诈骗能力",t/2,550)},sharePoster=async()=>{if(t.current)try{let e=await new Promise(e=>{t.current.toBlob(t=>{e(t)})});if(navigator.share){let t=new File([e],"score.png",{type:"image/png"});await navigator.share({title:"我的防诈挑战体验成绩",text:`我在防诈挑战中答对了${l.score}/${l.total}题！一起来体验吧！`,files:[t]})}else await navigator.clipboard.writeText(window.location.origin),alert("链接已复制到剪贴板")}catch(e){console.error("分享失败:",e),alert("分享失败，请尝试下载图片手动分享")}};return((0,s.useEffect)(()=>{l&&generatePoster()},[l,m]),l)?(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-4",children:[a.jsx("header",{className:"bg-white shadow-sm border-b mb-6",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(c(),{href:"/demo-quiz",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(r.Z,{className:"w-5 h-5 text-gray-600"})}),a.jsx(i.Z,{className:"w-8 h-8 text-primary-600"}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"分享体验成绩"}),a.jsx("p",{className:"text-sm text-gray-600",children:"生成精美的成绩海报"})]})]})})}),a.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"card",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[a.jsx(o.Z,{className:"w-5 h-5 text-primary-600"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"选择模板"})]}),a.jsx("div",{className:"grid grid-cols-3 gap-3",children:[{id:"classic",name:"经典",desc:"蓝色渐变"},{id:"modern",name:"现代",desc:"彩色卡片"},{id:"minimal",name:"简约",desc:"黑白风格"}].map(e=>(0,a.jsxs)("button",{onClick:()=>f(e.id),className:`p-3 border-2 rounded-lg text-center transition-all ${m===e.id?"border-primary-500 bg-primary-50":"border-gray-200 hover:border-primary-300"}`,children:[a.jsx("div",{className:"font-medium text-gray-900",children:e.name}),a.jsx("div",{className:"text-xs text-gray-600",children:e.desc})]},e.id))})]}),(0,a.jsxs)("div",{className:"card",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"体验成绩"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"正确题数"}),(0,a.jsxs)("span",{className:"font-semibold text-primary-600",children:[l.score,"/",l.total]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"正确率"}),(0,a.jsxs)("span",{className:"font-semibold text-success-600",children:[Math.round(l.score/l.total*100),"%"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"用时"}),a.jsx("span",{className:"font-semibold text-blue-600",children:formatTime(l.timeUsed)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"完成时间"}),a.jsx("span",{className:"font-semibold text-gray-900",children:new Date(l.completedAt).toLocaleDateString()})]})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{onClick:()=>{if(!t.current)return;let e=document.createElement("a");e.download=`防诈挑战体验成绩-${new Date().toLocaleDateString()}.png`,e.href=t.current.toDataURL(),e.click()},disabled:p,className:"w-full btn-primary flex items-center justify-center gap-2 disabled:opacity-50",children:[a.jsx(n.Z,{className:"w-5 h-5"}),"下载海报"]}),(0,a.jsxs)("button",{onClick:sharePoster,disabled:p,className:"w-full btn-secondary flex items-center justify-center gap-2 disabled:opacity-50",children:[a.jsx(i.Z,{className:"w-5 h-5"}),"分享海报"]})]}),(0,a.jsxs)("div",{className:"card bg-gradient-to-r from-primary-500 to-purple-600 text-white",children:[a.jsx("h3",{className:"text-lg font-bold mb-2",children:"想要更多功能？"}),a.jsx("p",{className:"text-primary-100 mb-4 text-sm",children:"注册完整账户，解锁每日挑战、排行榜、社区交流等功能！"}),a.jsx(c(),{href:"/login",className:"btn-primary bg-white text-primary-600 hover:bg-gray-100 text-sm",children:"立即注册"})]})]}),(0,a.jsxs)("div",{className:"card",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"海报预览"}),a.jsx("div",{className:"flex justify-center",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx("canvas",{ref:t,className:"max-w-full h-auto border border-gray-200 rounded-lg shadow-lg",style:{maxHeight:"500px"}}),p&&a.jsx("div",{className:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"})})]})})]})]})})]}):a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"})})}},8220:(e,t,l)=>{"use strict";l.r(t),l.d(t,{$$typeof:()=>i,__esModule:()=>r,default:()=>n});var a=l(5153);let s=(0,a.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx`),{__esModule:r,$$typeof:i}=s,o=s.default,n=o}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),l=t.X(0,[9678,5607,5276,5883,2035],()=>__webpack_exec__(3391));module.exports=l})();