/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/demo-share/page";
exports.ids = ["app/demo-share/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdemo-share%2Fpage&page=%2Fdemo-share%2Fpage&appPaths=%2Fdemo-share%2Fpage&pagePath=private-next-app-dir%2Fdemo-share%2Fpage.tsx&appDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdemo-share%2Fpage&page=%2Fdemo-share%2Fpage&appPaths=%2Fdemo-share%2Fpage&pagePath=private-next-app-dir%2Fdemo-share%2Fpage.tsx&appDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'demo-share',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/demo-share/page.tsx */ \"(rsc)/./app/demo-share/page.tsx\")), \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\")), \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/demo-share/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/demo-share/page\",\n        pathname: \"/demo-share\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdemo-share%2Fpage&page=%2Fdemo-share%2Fpage&appPaths=%2Fdemo-share%2Fpage&pagePath=private-next-app-dir%2Fdemo-share%2Fpage.tsx&appDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp%2Fdemo-share%2Fpage.tsx&server=true!":
/*!************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp%2Fdemo-share%2Fpage.tsx&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/demo-share/page.tsx */ \"(ssr)/./app/demo-share/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRnolMkZEZXNrdG9wJTJGdnNjb2RlJTJGJUU5JTk4JUIyJUU4JUFGJTg4JUU2JThDJTkxJUU2JTg4JTk4JUU2JUI4JUI4JUU2JTg4JThGJTJGYXBwJTJGZGVtby1zaGFyZSUyRnBhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2ZyYXVkLXByZXZlbnRpb24tZ2FtZS8/ZDA4NiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL3ovRGVza3RvcC92c2NvZGUv6Ziy6K+I5oyR5oiY5ri45oiPL2FwcC9kZW1vLXNoYXJlL3BhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp%2Fdemo-share%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp%2Fglobals.css&server=true!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp%2Fglobals.css&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRnolMkZEZXNrdG9wJTJGdnNjb2RlJTJGJUU5JTk4JUIyJUU4JUFGJTg4JUU2JThDJTkxJUU2JTg4JTk4JUU2JUI4JUI4JUU2JTg4JThGJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZsaW5rLmpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2ZyYXVkLXByZXZlbnRpb24tZ2FtZS8/OTVjNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL3ovRGVza3RvcC92c2NvZGUv6Ziy6K+I5oyR5oiY5ri45oiPL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2xpbmsuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/demo-share/page.tsx":
/*!*********************************!*\
  !*** ./app/demo-share/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DemoSharePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Palette_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Palette,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Palette_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Palette,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Palette_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Palette,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Palette_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Palette,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DemoSharePage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [demoResult, setDemoResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"classic\");\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 获取体验结果\n        const resultStr = localStorage.getItem(\"demoResult\");\n        if (!resultStr) {\n            router.push(\"/demo-quiz\");\n            return;\n        }\n        setDemoResult(JSON.parse(resultStr));\n    }, [\n        router\n    ]);\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins}:${secs.toString().padStart(2, \"0\")}`;\n    };\n    const generatePoster = async ()=>{\n        if (!canvasRef.current || !demoResult) return;\n        setIsGenerating(true);\n        const canvas = canvasRef.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        // 设置画布尺寸\n        canvas.width = 600;\n        canvas.height = 800;\n        // 根据模板绘制不同样式\n        switch(selectedTemplate){\n            case \"classic\":\n                drawClassicTemplate(ctx, canvas.width, canvas.height);\n                break;\n            case \"modern\":\n                drawModernTemplate(ctx, canvas.width, canvas.height);\n                break;\n            case \"minimal\":\n                drawMinimalTemplate(ctx, canvas.width, canvas.height);\n                break;\n        }\n        setIsGenerating(false);\n    };\n    const drawClassicTemplate = (ctx, width, height)=>{\n        // 背景渐变\n        const gradient = ctx.createLinearGradient(0, 0, 0, height);\n        gradient.addColorStop(0, \"#3B82F6\");\n        gradient.addColorStop(1, \"#1E40AF\");\n        ctx.fillStyle = gradient;\n        ctx.fillRect(0, 0, width, height);\n        // 标题\n        ctx.fillStyle = \"#FFFFFF\";\n        ctx.font = \"bold 36px Arial\";\n        ctx.textAlign = \"center\";\n        ctx.fillText(\"防诈挑战游戏\", width / 2, 80);\n        // 体验标识\n        ctx.font = \"bold 24px Arial\";\n        ctx.fillText(\"免费体验版\", width / 2, 120);\n        // 成绩卡片背景\n        ctx.fillStyle = \"rgba(255, 255, 255, 0.95)\";\n        ctx.roundRect(50, 180, width - 100, 350, 20);\n        ctx.fill();\n        // 成绩信息\n        ctx.fillStyle = \"#1F2937\";\n        ctx.font = \"bold 48px Arial\";\n        ctx.fillText(`${demoResult.score}/${demoResult.total}`, width / 2, 280);\n        ctx.font = \"24px Arial\";\n        ctx.fillText(\"正确题数\", width / 2, 310);\n        ctx.font = \"bold 32px Arial\";\n        ctx.fillText(`${Math.round(demoResult.score / demoResult.total * 100)}%`, width / 2, 370);\n        ctx.font = \"20px Arial\";\n        ctx.fillText(\"正确率\", width / 2, 400);\n        ctx.font = \"bold 24px Arial\";\n        ctx.fillText(formatTime(demoResult.timeUsed), width / 2, 450);\n        ctx.font = \"18px Arial\";\n        ctx.fillText(\"用时\", width / 2, 475);\n        // 日期\n        ctx.font = \"18px Arial\";\n        ctx.fillStyle = \"#6B7280\";\n        ctx.fillText(new Date(demoResult.completedAt).toLocaleDateString(), width / 2, 510);\n        // 底部文字\n        ctx.fillStyle = \"#FFFFFF\";\n        ctx.font = \"20px Arial\";\n        ctx.fillText(\"提升防诈骗意识，保护自己远离陷阱\", width / 2, 650);\n        ctx.font = \"16px Arial\";\n        ctx.fillText(\"扫码体验完整版功能\", width / 2, 700);\n    };\n    const drawModernTemplate = (ctx, width, height)=>{\n        // 现代风格背景\n        ctx.fillStyle = \"#F8FAFC\";\n        ctx.fillRect(0, 0, width, height);\n        // 顶部色块\n        const gradient = ctx.createLinearGradient(0, 0, width, 0);\n        gradient.addColorStop(0, \"#8B5CF6\");\n        gradient.addColorStop(1, \"#EC4899\");\n        ctx.fillStyle = gradient;\n        ctx.fillRect(0, 0, width, 150);\n        // 标题\n        ctx.fillStyle = \"#FFFFFF\";\n        ctx.font = \"bold 32px Arial\";\n        ctx.textAlign = \"center\";\n        ctx.fillText(\"\\uD83D\\uDEE1️ 防诈挑战成绩\", width / 2, 60);\n        ctx.fillText(\"(体验版)\", width / 2, 110);\n        // 成绩卡片\n        ctx.fillStyle = \"#FFFFFF\";\n        ctx.shadowColor = \"rgba(0, 0, 0, 0.1)\";\n        ctx.shadowBlur = 20;\n        ctx.roundRect(40, 200, width - 80, 300, 15);\n        ctx.fill();\n        ctx.shadowBlur = 0;\n        // 成绩数据\n        ctx.fillStyle = \"#1F2937\";\n        ctx.font = \"bold 56px Arial\";\n        ctx.fillText(`${demoResult.score}/${demoResult.total}`, width / 2, 300);\n        ctx.font = \"24px Arial\";\n        ctx.fillStyle = \"#6B7280\";\n        ctx.fillText(\"正确题数\", width / 2, 330);\n        // 其他信息\n        ctx.fillStyle = \"#3B82F6\";\n        ctx.font = \"bold 28px Arial\";\n        ctx.fillText(`${Math.round(demoResult.score / demoResult.total * 100)}%`, width / 2, 390);\n        ctx.fillStyle = \"#6B7280\";\n        ctx.font = \"20px Arial\";\n        ctx.fillText(\"正确率\", width / 2, 415);\n        ctx.fillStyle = \"#8B5CF6\";\n        ctx.font = \"bold 22px Arial\";\n        ctx.fillText(formatTime(demoResult.timeUsed), width / 2, 460);\n        ctx.fillStyle = \"#6B7280\";\n        ctx.font = \"18px Arial\";\n        ctx.fillText(\"用时\", width / 2, 485);\n    };\n    const drawMinimalTemplate = (ctx, width, height)=>{\n        // 简约风格白色背景\n        ctx.fillStyle = \"#FFFFFF\";\n        ctx.fillRect(0, 0, width, height);\n        // 简单边框\n        ctx.strokeStyle = \"#E5E7EB\";\n        ctx.lineWidth = 2;\n        ctx.strokeRect(20, 20, width - 40, height - 40);\n        // 标题\n        ctx.fillStyle = \"#1F2937\";\n        ctx.font = \"bold 28px Arial\";\n        ctx.textAlign = \"center\";\n        ctx.fillText(\"防诈挑战成绩单\", width / 2, 100);\n        ctx.font = \"20px Arial\";\n        ctx.fillStyle = \"#6B7280\";\n        ctx.fillText(\"(免费体验版)\", width / 2, 130);\n        // 分割线\n        ctx.strokeStyle = \"#D1D5DB\";\n        ctx.lineWidth = 1;\n        ctx.beginPath();\n        ctx.moveTo(80, 160);\n        ctx.lineTo(width - 80, 160);\n        ctx.stroke();\n        // 成绩信息\n        ctx.font = \"bold 48px Arial\";\n        ctx.fillStyle = \"#059669\";\n        ctx.fillText(`${demoResult.score}/${demoResult.total}`, width / 2, 250);\n        ctx.font = \"20px Arial\";\n        ctx.fillStyle = \"#6B7280\";\n        ctx.fillText(\"正确题数\", width / 2, 280);\n        ctx.font = \"bold 32px Arial\";\n        ctx.fillStyle = \"#3B82F6\";\n        ctx.fillText(`${Math.round(demoResult.score / demoResult.total * 100)}%`, width / 2, 340);\n        ctx.font = \"18px Arial\";\n        ctx.fillStyle = \"#6B7280\";\n        ctx.fillText(\"正确率\", width / 2, 365);\n        // 底部信息\n        ctx.font = \"16px Arial\";\n        ctx.fillText(new Date(demoResult.completedAt).toLocaleDateString(), width / 2, 450);\n        ctx.font = \"18px Arial\";\n        ctx.fillStyle = \"#1F2937\";\n        ctx.fillText(\"继续学习，提升防诈骗能力\", width / 2, 550);\n    };\n    const downloadPoster = ()=>{\n        if (!canvasRef.current) return;\n        const link = document.createElement(\"a\");\n        link.download = `防诈挑战体验成绩-${new Date().toLocaleDateString()}.png`;\n        link.href = canvasRef.current.toDataURL();\n        link.click();\n    };\n    const sharePoster = async ()=>{\n        if (!canvasRef.current) return;\n        try {\n            const blob = await new Promise((resolve)=>{\n                canvasRef.current.toBlob((blob)=>{\n                    resolve(blob);\n                });\n            });\n            if (navigator.share) {\n                const file = new File([\n                    blob\n                ], \"score.png\", {\n                    type: \"image/png\"\n                });\n                await navigator.share({\n                    title: \"我的防诈挑战体验成绩\",\n                    text: `我在防诈挑战中答对了${demoResult.score}/${demoResult.total}题！一起来体验吧！`,\n                    files: [\n                        file\n                    ]\n                });\n            } else {\n                // 降级到复制链接\n                await navigator.clipboard.writeText(window.location.origin);\n                alert(\"链接已复制到剪贴板\");\n            }\n        } catch (error) {\n            console.error(\"分享失败:\", error);\n            alert(\"分享失败，请尝试下载图片手动分享\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (demoResult) {\n            generatePoster();\n        }\n    }, [\n        demoResult,\n        selectedTemplate\n    ]);\n    if (!demoResult) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                lineNumber: 270,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n            lineNumber: 269,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/demo-quiz\",\n                                className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Palette_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5 text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Palette_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-8 h-8 text-primary-600\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold text-gray-900\",\n                                        children: \"分享体验成绩\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"生成精美的成绩海报\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Palette_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-5 h-5 text-primary-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                    children: \"选择模板\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-3\",\n                                            children: [\n                                                {\n                                                    id: \"classic\",\n                                                    name: \"经典\",\n                                                    desc: \"蓝色渐变\"\n                                                },\n                                                {\n                                                    id: \"modern\",\n                                                    name: \"现代\",\n                                                    desc: \"彩色卡片\"\n                                                },\n                                                {\n                                                    id: \"minimal\",\n                                                    name: \"简约\",\n                                                    desc: \"黑白风格\"\n                                                }\n                                            ].map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedTemplate(template.id),\n                                                    className: `p-3 border-2 rounded-lg text-center transition-all ${selectedTemplate === template.id ? \"border-primary-500 bg-primary-50\" : \"border-gray-200 hover:border-primary-300\"}`,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: template.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: template.desc\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, template.id, true, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                            children: \"体验成绩\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"正确题数\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-primary-600\",\n                                                            children: [\n                                                                demoResult.score,\n                                                                \"/\",\n                                                                demoResult.total\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"正确率\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-success-600\",\n                                                            children: [\n                                                                Math.round(demoResult.score / demoResult.total * 100),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"用时\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-blue-600\",\n                                                            children: formatTime(demoResult.timeUsed)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"完成时间\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-gray-900\",\n                                                            children: new Date(demoResult.completedAt).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: downloadPoster,\n                                            disabled: isGenerating,\n                                            className: \"w-full btn-primary flex items-center justify-center gap-2 disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Palette_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"下载海报\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: sharePoster,\n                                            disabled: isGenerating,\n                                            className: \"w-full btn-secondary flex items-center justify-center gap-2 disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Palette_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"分享海报\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card bg-gradient-to-r from-primary-500 to-purple-600 text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold mb-2\",\n                                            children: \"想要更多功能？\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-primary-100 mb-4 text-sm\",\n                                            children: \"注册完整账户，解锁每日挑战、排行榜、社区交流等功能！\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/login\",\n                                            className: \"btn-primary bg-white text-primary-600 hover:bg-gray-100 text-sm\",\n                                            children: \"立即注册\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: \"海报预览\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                                                ref: canvasRef,\n                                                className: \"max-w-full h-auto border border-gray-200 rounded-lg shadow-lg\",\n                                                style: {\n                                                    maxHeight: \"500px\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 17\n                                            }, this),\n                                            isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx\",\n        lineNumber: 276,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/demo-share/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"11b37adaa9c3\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mcmF1ZC1wcmV2ZW50aW9uLWdhbWUvLi9hcHAvZ2xvYmFscy5jc3M/ODJlZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjExYjM3YWRhYTljM1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/demo-share/page.tsx":
/*!*********************************!*\
  !*** ./app/demo-share/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/vscode/防诈挑战游戏/app/demo-share/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: \"大学生防诈挑战游戏\",\n    description: \"通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱\",\n    keywords: \"防诈骗,大学生,安全教育,答题游戏\",\n    authors: [\n        {\n            name: \"防诈挑战游戏团队\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxVQUFVO0lBQ1ZDLFNBQVM7UUFBQztZQUFFQyxNQUFNO1FBQVc7S0FBRTtBQUNqQyxFQUFDO0FBRU0sTUFBTUMsV0FBVztJQUN0QkMsT0FBTztJQUNQQyxjQUFjO0FBQ2hCLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7c0JBQ0MsNEVBQUNDO2dCQUFJQyxXQUFVOzBCQUNaTDs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJhdWQtcHJldmVudGlvbi1nYW1lLy4vYXBwL2xheW91dC50c3g/OTk4OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICflpKflrabnlJ/pmLLor4jmjJHmiJjmuLjmiI8nLFxuICBkZXNjcmlwdGlvbjogJ+mAmui/h+etlOmimOaMkeaImOaPkOWNh+mYsuiviOmql+aEj+ivhu+8jOS/neaKpOiHquW3sei/nOemu+WQhOenjeiviOmql+mZt+mYsScsXG4gIGtleXdvcmRzOiAn6Ziy6K+I6aqXLOWkp+WtpueUnyzlronlhajmlZnogrIs562U6aKY5ri45oiPJyxcbiAgYXV0aG9yczogW3sgbmFtZTogJ+mYsuiviOaMkeaImOa4uOaIj+WboumYnycgfV0sXG59XG5cbmV4cG9ydCBjb25zdCB2aWV3cG9ydCA9IHtcbiAgd2lkdGg6ICdkZXZpY2Utd2lkdGgnLFxuICBpbml0aWFsU2NhbGU6IDEsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHk+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuXCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJrZXl3b3JkcyIsImF1dGhvcnMiLCJuYW1lIiwidmlld3BvcnQiLCJ3aWR0aCIsImluaXRpYWxTY2FsZSIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Home!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Home!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Home!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-16 h-16 text-yellow-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 10,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-6xl font-bold text-gray-900 mb-2\",\n                                children: \"404\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 11,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-700 mb-4\",\n                                children: \"页面未找到\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 12,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"抱歉，你访问的页面不存在。可能是链接错误或页面已被移除。\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 13,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"btn-primary w-full flex items-center justify-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"返回首页\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"btn-secondary w-full flex items-center justify-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"返回上一页\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-800 text-sm\",\n                            children: [\n                                \"\\uD83D\\uDCA1 \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"提示：\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 18\n                                }, this),\n                                \"如果你是通过链接访问的，请检查链接是否正确。 如果问题持续存在，请联系我们。\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n                lineNumber: 8,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/vscode/防诈挑战游戏/app/not-found.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/not-found.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdemo-share%2Fpage&page=%2Fdemo-share%2Fpage&appPaths=%2Fdemo-share%2Fpage&pagePath=private-next-app-dir%2Fdemo-share%2Fpage.tsx&appDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fz%2FDesktop%2Fvscode%2F%E9%98%B2%E8%AF%88%E6%8C%91%E6%88%98%E6%B8%B8%E6%88%8F&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();