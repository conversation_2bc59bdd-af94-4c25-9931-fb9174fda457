exports.id=2035,exports.ids=[2035],exports.modules={8768:()=>{},4404:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,3724,23)),Promise.resolve().then(t.t.bind(t,5365,23)),Promise.resolve().then(t.t.bind(t,4900,23)),Promise.resolve().then(t.t.bind(t,4714,23)),Promise.resolve().then(t.t.bind(t,5392,23)),Promise.resolve().then(t.t.bind(t,8898,23))},7477:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,614,23))},5345:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>RootLayout,metadata:()=>l,viewport:()=>i});var r=t(4656);t(7272);let l={title:"大学生防诈挑战游戏",description:"通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱",keywords:"防诈骗,大学生,安全教育,答题游戏",authors:[{name:"防诈挑战游戏团队"}]},i={width:"device-width",initialScale:1};function RootLayout({children:e}){return r.jsx("html",{lang:"zh-CN",children:r.jsx("body",{children:r.jsx("div",{className:"min-h-screen",children:e})})})}},4293:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>NotFound});var r=t(4656),l=t(4353),i=t.n(l),n=t(5713),a=t(5695),d=t(1371);function NotFound(){return r.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4 sm:px-6 lg:px-8",children:r.jsx("div",{className:"max-w-md w-full text-center",children:(0,r.jsxs)("div",{className:"card",children:[(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx(n.Z,{className:"w-16 h-16 text-yellow-500 mx-auto mb-4"}),r.jsx("h1",{className:"text-6xl font-bold text-gray-900 mb-2",children:"404"}),r.jsx("h2",{className:"text-2xl font-semibold text-gray-700 mb-4",children:"页面未找到"}),r.jsx("p",{className:"text-gray-600",children:"抱歉，你访问的页面不存在。可能是链接错误或页面已被移除。"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)(i(),{href:"/",className:"btn-primary w-full flex items-center justify-center gap-2",children:[r.jsx(a.Z,{className:"w-5 h-5"}),"返回首页"]}),(0,r.jsxs)(i(),{href:"/",className:"btn-secondary w-full flex items-center justify-center gap-2",children:[r.jsx(d.Z,{className:"w-5 h-5"}),"返回上一页"]})]}),r.jsx("div",{className:"mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,r.jsxs)("p",{className:"text-blue-800 text-sm",children:["\uD83D\uDCA1 ",r.jsx("strong",{children:"提示："}),"如果你是通过链接访问的，请检查链接是否正确。 如果问题持续存在，请联系我们。"]})})]})})})}},7272:()=>{}};