{"version": 4, "routes": {"/category-practice": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/category-practice", "dataRoute": "/category-practice.rsc"}, "/ai-features": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/ai-features", "dataRoute": "/ai-features.rsc"}, "/community": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/community", "dataRoute": "/community.rsc"}, "/dashboard": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/dashboard", "dataRoute": "/dashboard.rsc"}, "/demo-quiz": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/demo-quiz", "dataRoute": "/demo-quiz.rsc"}, "/demo-share": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/demo-share", "dataRoute": "/demo-share.rsc"}, "/features/anonymous": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/features/anonymous", "dataRoute": "/features/anonymous.rsc"}, "/features/category-learning": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/features/category-learning", "dataRoute": "/features/category-learning.rsc"}, "/features/category-practice": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/features/category-practice", "dataRoute": "/features/category-practice.rsc"}, "/features/community-questions": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/features/community-questions", "dataRoute": "/features/community-questions.rsc"}, "/features/leaderboard": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/features/leaderboard", "dataRoute": "/features/leaderboard.rsc"}, "/leaderboard": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/leaderboard", "dataRoute": "/leaderboard.rsc"}, "/features/real-cases": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/features/real-cases", "dataRoute": "/features/real-cases.rsc"}, "/learning-center": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/learning-center", "dataRoute": "/learning-center.rsc"}, "/login": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/login", "dataRoute": "/login.rsc"}, "/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}, "/profile": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/profile", "dataRoute": "/profile.rsc"}, "/share-score": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/share-score", "dataRoute": "/share-score.rsc"}, "/starter-quiz": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/starter-quiz", "dataRoute": "/starter-quiz.rsc"}, "/submit-question": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/submit-question", "dataRoute": "/submit-question.rsc"}, "/multimedia-content": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/multimedia-content", "dataRoute": "/multimedia-content.rsc"}, "/challenge": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/challenge", "dataRoute": "/challenge.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "3b6d57a37e1b96e708c753d656159b55", "previewModeSigningKey": "faa0bc2c796aba4675b187d6e2901cc86f96af438039df5da5879c2be73cedac", "previewModeEncryptionKey": "7e3b2947d90c3d0bbe17249b52046bf86216230329a5324bbe861e172d5aabe0"}}