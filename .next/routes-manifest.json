{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:file((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+)/", "destination": "/:file", "internal": true, "missing": [{"type": "header", "key": "x-nextjs-data"}], "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+))/$"}, {"source": "/:notfile((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+)", "destination": "/:notfile/", "internal": true, "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+))$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/ai-features", "regex": "^/ai\\-features(?:/)?$", "routeKeys": {}, "namedRegex": "^/ai\\-features(?:/)?$"}, {"page": "/category-practice", "regex": "^/category\\-practice(?:/)?$", "routeKeys": {}, "namedRegex": "^/category\\-practice(?:/)?$"}, {"page": "/challenge", "regex": "^/challenge(?:/)?$", "routeKeys": {}, "namedRegex": "^/challenge(?:/)?$"}, {"page": "/community", "regex": "^/community(?:/)?$", "routeKeys": {}, "namedRegex": "^/community(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/demo-quiz", "regex": "^/demo\\-quiz(?:/)?$", "routeKeys": {}, "namedRegex": "^/demo\\-quiz(?:/)?$"}, {"page": "/demo-share", "regex": "^/demo\\-share(?:/)?$", "routeKeys": {}, "namedRegex": "^/demo\\-share(?:/)?$"}, {"page": "/features/anonymous", "regex": "^/features/anonymous(?:/)?$", "routeKeys": {}, "namedRegex": "^/features/anonymous(?:/)?$"}, {"page": "/features/category-learning", "regex": "^/features/category\\-learning(?:/)?$", "routeKeys": {}, "namedRegex": "^/features/category\\-learning(?:/)?$"}, {"page": "/features/category-practice", "regex": "^/features/category\\-practice(?:/)?$", "routeKeys": {}, "namedRegex": "^/features/category\\-practice(?:/)?$"}, {"page": "/features/community-questions", "regex": "^/features/community\\-questions(?:/)?$", "routeKeys": {}, "namedRegex": "^/features/community\\-questions(?:/)?$"}, {"page": "/features/leaderboard", "regex": "^/features/leaderboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/features/leaderboard(?:/)?$"}, {"page": "/features/real-cases", "regex": "^/features/real\\-cases(?:/)?$", "routeKeys": {}, "namedRegex": "^/features/real\\-cases(?:/)?$"}, {"page": "/leaderboard", "regex": "^/leaderboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/leaderboard(?:/)?$"}, {"page": "/learning-center", "regex": "^/learning\\-center(?:/)?$", "routeKeys": {}, "namedRegex": "^/learning\\-center(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/multimedia-content", "regex": "^/multimedia\\-content(?:/)?$", "routeKeys": {}, "namedRegex": "^/multimedia\\-content(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}, {"page": "/share-score", "regex": "^/share\\-score(?:/)?$", "routeKeys": {}, "namedRegex": "^/share\\-score(?:/)?$"}, {"page": "/starter-quiz", "regex": "^/starter\\-quiz(?:/)?$", "routeKeys": {}, "namedRegex": "^/starter\\-quiz(?:/)?$"}, {"page": "/submit-question", "regex": "^/submit\\-question(?:/)?$", "routeKeys": {}, "namedRegex": "^/submit\\-question(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Url", "prefetchHeader": "Next-Router-Prefetch", "contentTypeHeader": "text/x-component"}, "rewrites": []}