(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{2898:function(e,t,s){"use strict";s.d(t,{Z:function(){return createLucideIcon}});var r=s(2265),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let toKebabCase=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),createLucideIcon=(e,t)=>{let s=(0,r.forwardRef)(({color:s="currentColor",size:i=24,strokeWidth:l=2,absoluteStrokeWidth:c,className:n="",children:x,...d},o)=>(0,r.createElement)("svg",{ref:o,...a,width:i,height:i,stroke:s,strokeWidth:c?24*Number(l)/Number(i):l,className:["lucide",`lucide-${toKebabCase(e)}`,n].join(" "),...d},[...t.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(x)?x:[x]]));return s.displayName=`${e}`,s}},2894:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},8291:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},9865:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},9670:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2176:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]])},9883:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},9036:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},6654:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},9868:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z",key:"y3tblf"}]])},8957:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},5750:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},300:function(e,t,s){Promise.resolve().then(s.bind(s,1379))},1379:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return HomePage}});var r=s(7437),a=s(2265),i=s(9036),l=s(8291),c=s(6654),n=s(9883),x=s(9865),d=s(5750),o=s(8957),m=s(2894),h=s(1396),u=s.n(h),g=s(9868),y=s(2176),f=s(9670);let p=[{id:"1",title:'差点被"高薪兼职"骗了5000元',content:"在QQ群里看到日赚300的兼职广告，要求先交2000元保证金。幸好室友提醒我这是诈骗，差点就转账了...",category:"兼职诈骗",author:"小李同学",timeAgo:"2小时前",likes:23,comments:8,views:156,isVerified:!0,severity:"high"},{id:"2",title:"租房遇到假房东，损失1500元",content:"在网上看到便宜房源，房东说要先交定金才能看房。结果交了钱就联系不上了，房子根本不存在...",category:"租房陷阱",author:"张小明",timeAgo:"5小时前",likes:31,comments:12,views:203,isVerified:!1,severity:"medium"},{id:"3",title:'网贷"砍头息"套路深，借3000还6000',content:"急需用钱申请了网贷，说是无息但要交各种手续费。最后发现实际利率超高，还不起就威胁恐吓...",category:"网贷陷阱",author:"王小华",timeAgo:"1天前",likes:45,comments:18,views:312,isVerified:!0,severity:"high"},{id:"4",title:'培训机构承诺"包就业"，结果是骗局',content:"交了8000元培训费，说包分配工作。培训结束后推荐的都是销售岗位，和承诺的技术岗完全不符...",category:"培训诈骗",author:"李小红",timeAgo:"2天前",likes:28,comments:15,views:189,isVerified:!1,severity:"medium"},{id:"5",title:"冒充银行客服，差点泄露银行卡信息",content:'接到"银行客服"电话说卡被盗刷，要求提供验证码。还好想起防诈骗知识，直接挂断并报警...',category:"电信诈骗",author:"陈小军",timeAgo:"3天前",likes:52,comments:22,views:278,isVerified:!0,severity:"high"},{id:"6",title:'求职被要求交"体检费"，及时识破',content:"面试通过后HR说要交500元体检费才能入职。感觉不对劲上网查了查，发现是常见的求职诈骗套路...",category:"求职骗局",author:"刘小芳",timeAgo:"4天前",likes:19,comments:7,views:134,isVerified:!1,severity:"low"},{id:"7",title:'投资理财群里的"专家"都是托',content:"被拉进投资群，看到很多人晒收益截图。跟着投了2万元，结果平台跑路了，钱全没了...",category:"投资诈骗",author:"赵小强",timeAgo:"5天前",likes:67,comments:25,views:445,isVerified:!0,severity:"high"},{id:"8",title:"网购退款诈骗，差点被套取验证码",content:'收到"客服"电话说商品有质量问题要退款，要求提供支付宝验证码。幸好多了个心眼没有提供...',category:"网购诈骗",author:"孙小美",timeAgo:"1周前",likes:34,comments:11,views:198,isVerified:!1,severity:"medium"}];function UserStories(){let[e,t]=(0,a.useState)(0),[s,i]=(0,a.useState)(!0);(0,a.useEffect)(()=>{if(!s)return;let e=setInterval(()=>{t(e=>(e+1)%p.length)},5e3);return()=>clearInterval(e)},[s]);let l=p[e];return(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg border border-gray-200 p-6 h-full",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(m.Z,{className:"w-5 h-5 text-orange-500"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"用户防骗故事"})]}),(0,r.jsx)("button",{onClick:()=>i(!s),className:"px-3 py-1 rounded-full text-xs font-medium transition-colors ".concat(s?"bg-green-100 text-green-700 hover:bg-green-200":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:s?"暂停":"播放"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900 mb-2 line-clamp-2",children:l.title}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:l.author}),l.isVerified&&(0,r.jsx)("span",{className:"w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white text-xs",children:"✓"})}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:"•"}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:l.timeAgo})]})]}),(0,r.jsx)("div",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat((e=>{switch(e){case"high":return"text-red-600 bg-red-100";case"medium":return"text-yellow-600 bg-yellow-100";case"low":return"text-green-600 bg-green-100";default:return"text-gray-600 bg-gray-100"}})(l.severity)),children:(e=>{switch(e){case"high":return"高危";case"medium":return"中危";case"low":return"低危";default:return"未知"}})(l.severity)})]}),(0,r.jsx)("div",{className:"flex items-center gap-2",children:(0,r.jsx)("span",{className:"px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full",children:l.category})}),(0,r.jsx)("p",{className:"text-gray-700 text-sm leading-relaxed line-clamp-4",children:l.content}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-xs text-gray-500 pt-3 border-t border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(g.Z,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:l.likes})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(y.Z,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:l.comments})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(f.Z,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:l.views})]})]})]}),(0,r.jsx)("div",{className:"flex items-center gap-1 mt-6",children:p.map((s,a)=>(0,r.jsx)("button",{onClick:()=>t(a),className:"h-1 rounded-full transition-all duration-300 ".concat(a===e?"bg-primary-600 w-8":"bg-gray-300 w-2 hover:bg-gray-400")},a))}),(0,r.jsx)("div",{className:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,r.jsxs)("p",{className:"text-blue-800 text-xs",children:["\uD83D\uDCA1 ",(0,r.jsx)("strong",{children:"提醒："}),"这些都是真实用户分享的被骗经历，希望能帮助大家提高警惕，避免类似损失。"]})})]})}function HomePage(){let[e,t]=(0,a.useState)({totalUsers:1234,questionsAnswered:8765,successRate:87});return(0,r.jsxs)("div",{className:"min-h-screen",children:[(0,r.jsxs)("section",{className:"relative overflow-hidden bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,r.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"flex justify-center mb-8",children:(0,r.jsx)("div",{className:"p-4 bg-white/10 rounded-full backdrop-blur-sm",children:(0,r.jsx)(i.Z,{className:"w-16 h-16 text-white"})})}),(0,r.jsx)("h1",{className:"text-3xl md:text-4xl font-bold text-white mb-4",children:"大学生防诈挑战游戏"}),(0,r.jsx)("p",{className:"text-lg md:text-xl text-blue-100 mb-6 max-w-3xl mx-auto",children:"通过答题挑战提升防诈骗意识，保护自己远离各种诈骗陷阱"}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,r.jsxs)(u(),{href:"/demo-quiz",className:"btn-primary text-lg px-6 py-3 inline-flex items-center gap-2",children:["免费答题 (10题)",(0,r.jsx)(l.Z,{className:"w-5 h-5"})]}),(0,r.jsxs)(u(),{href:"/category-practice",className:"btn-secondary bg-white/10 text-white border-white/20 hover:bg-white/20 text-lg px-6 py-3 inline-flex items-center gap-2",children:["分类刷题",(0,r.jsx)(c.Z,{className:"w-5 h-5"})]}),(0,r.jsxs)(u(),{href:"/submit-question",className:"btn-secondary bg-white/10 text-white border-white/20 hover:bg-white/20 text-lg px-6 py-3 inline-flex items-center gap-2",children:["体验出题",(0,r.jsx)(n.Z,{className:"w-5 h-5"})]}),(0,r.jsxs)(u(),{href:"/login",className:"btn-secondary bg-white/10 text-white border-white/20 hover:bg-white/20 text-lg px-6 py-3 inline-flex items-center gap-2",children:["完整体验",(0,r.jsx)(l.Z,{className:"w-5 h-5"})]})]})]})})]}),(0,r.jsx)("section",{className:"py-8 bg-white",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-4xl font-bold text-primary-600 mb-2",children:e.totalUsers.toLocaleString()}),(0,r.jsx)("div",{className:"text-gray-600",children:"参与用户"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-4xl font-bold text-primary-600 mb-2",children:e.questionsAnswered.toLocaleString()}),(0,r.jsx)("div",{className:"text-gray-600",children:"题目完成数"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-4xl font-bold text-primary-600 mb-2",children:[e.successRate,"%"]}),(0,r.jsx)("div",{className:"text-gray-600",children:"平均正确率"})]})]})})}),(0,r.jsx)("section",{className:"py-12 bg-gray-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("h2",{className:"text-2xl md:text-3xl font-bold text-gray-900 mb-4",children:"游戏特色"}),(0,r.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"通过科学的游戏机制，让防诈骗学习变得有趣且有效"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-3 grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)(u(),{href:"/features/anonymous",className:"card text-center hover:shadow-lg transition-shadow",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(i.Z,{className:"w-6 h-6 text-primary-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"匿名参与"}),(0,r.jsx)("p",{className:"text-gray-600",children:"无需实名注册，通过邀请码+昵称的方式匿名参与，保护隐私"})]}),(0,r.jsxs)(u(),{href:"/features/category-learning",className:"card text-center hover:shadow-lg transition-shadow",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(x.Z,{className:"w-6 h-6 text-success-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"分类学习"}),(0,r.jsx)("p",{className:"text-gray-600",children:"涵盖求职、租房、网贷、培训等多种诈骗类型，针对性学习"})]}),(0,r.jsxs)(u(),{href:"/features/community-questions",className:"card text-center hover:shadow-lg transition-shadow",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(d.Z,{className:"w-6 h-6 text-purple-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"共建题库"}),(0,r.jsx)("p",{className:"text-gray-600",children:"用户可以提交真实案例，经审核后加入题库，共同建设"})]}),(0,r.jsxs)(u(),{href:"/features/leaderboard",className:"card text-center hover:shadow-lg transition-shadow",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(o.Z,{className:"w-6 h-6 text-orange-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"排行榜"}),(0,r.jsx)("p",{className:"text-gray-600",children:"每日挑战排行榜，激发学习动力，分享成绩海报"})]}),(0,r.jsxs)(u(),{href:"/features/category-practice",className:"card text-center hover:shadow-lg transition-shadow",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(c.Z,{className:"w-6 h-6 text-red-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"分类刷题"}),(0,r.jsx)("p",{className:"text-gray-600",children:"根据个人需求选择特定领域，针对性强化学习"})]}),(0,r.jsxs)(u(),{href:"/features/real-cases",className:"card text-center hover:shadow-lg transition-shadow",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(m.Z,{className:"w-6 h-6 text-indigo-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"真实案例"}),(0,r.jsx)("p",{className:"text-gray-600",children:"基于用户真实被骗经历，警示教育效果更佳"})]})]}),(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsx)(UserStories,{})})]})]})}),(0,r.jsx)("section",{className:"py-20 bg-primary-600",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"flex justify-center mb-6",children:(0,r.jsx)(m.Z,{className:"w-16 h-16 text-yellow-300"})}),(0,r.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-6",children:"提高警惕，远离诈骗"}),(0,r.jsx)("p",{className:"text-xl text-blue-100 mb-8",children:"每天只需几分钟，通过答题挑战提升防诈骗能力，保护自己和身边的人"}),(0,r.jsxs)(u(),{href:"/login",className:"btn-primary bg-white text-primary-600 hover:bg-gray-100 text-lg px-8 py-4 inline-flex items-center gap-2",children:["立即开始学习",(0,r.jsx)(l.Z,{className:"w-5 h-5"})]})]})}),(0,r.jsx)("footer",{className:"bg-gray-900 text-white py-12",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"flex justify-center mb-4",children:(0,r.jsx)(i.Z,{className:"w-8 h-8 text-primary-400"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"大学生防诈挑战游戏"}),(0,r.jsx)("p",{className:"text-gray-400 mb-6",children:"让防诈骗学习变得有趣且有效"}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"\xa9 2024 防诈挑战游戏. 保护大学生远离诈骗陷阱."})]})})})]})}},622:function(e,t,s){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=s(2265),a=Symbol.for("react.element"),i=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,c=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,n={key:!0,ref:!0,__self:!0,__source:!0};function q(e,t,s){var r,i={},x=null,d=null;for(r in void 0!==s&&(x=""+s),void 0!==t.key&&(x=""+t.key),void 0!==t.ref&&(d=t.ref),t)l.call(t,r)&&!n.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===i[r]&&(i[r]=t[r]);return{$$typeof:a,type:e,key:x,ref:d,props:i,_owner:c.current}}t.Fragment=i,t.jsx=q,t.jsxs=q},7437:function(e,t,s){"use strict";e.exports=s(622)},1396:function(e,t,s){e.exports=s(8326)}},function(e){e.O(0,[326,971,472,744],function(){return e(e.s=300)}),_N_E=e.O()}]);