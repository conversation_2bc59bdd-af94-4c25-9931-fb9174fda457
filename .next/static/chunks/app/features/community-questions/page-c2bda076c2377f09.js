(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[718],{2898:function(e,s,t){"use strict";t.d(s,{Z:function(){return createLucideIcon}});var r=t(2265),l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let toKebabCase=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),createLucideIcon=(e,s)=>{let t=(0,r.forwardRef)(({color:t="currentColor",size:a=24,strokeWidth:i=2,absoluteStrokeWidth:n,className:c="",children:d,...o},x)=>(0,r.createElement)("svg",{ref:x,...l,width:a,height:a,stroke:t,strokeWidth:n?24*Number(i)/Number(a):i,className:["lucide",`lucide-${toKebabCase(e)}`,c].join(" "),...o},[...s.map(([e,s])=>(0,r.createElement)(e,s)),...Array.isArray(d)?d:[d]]));return t.displayName=`${e}`,t}},3067:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,r.Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},2505:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,r.Z)("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]])},9883:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,r.Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5340:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,r.Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},5790:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,r.Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},5750:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,r.Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},923:function(e,s,t){Promise.resolve().then(t.bind(t,6504))},6504:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return CommunityQuestionsPage}});var r=t(7437),l=t(2265),a=t(3067),i=t(5750),n=t(5790),c=t(9883),d=t(2505),o=t(5340),x=t(1396),m=t.n(x);function CommunityQuestionsPage(){let[e,s]=(0,l.useState)("overview"),t={totalSubmissions:1247,approvedQuestions:892,activeContributors:156,averageQuality:8.7},getStatusColor=e=>{switch(e){case"approved":return"text-green-600 bg-green-100";case"reviewing":return"text-yellow-600 bg-yellow-100";default:return"text-gray-600 bg-gray-100"}},getStatusLabel=e=>{switch(e){case"approved":return"已通过";case"reviewing":return"审核中";default:return"未知"}};return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(m(),{href:"/",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(a.Z,{className:"w-5 h-5 text-gray-600"})}),(0,r.jsx)(i.Z,{className:"w-8 h-8 text-purple-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"共建题库"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"用户共同建设，知识共享"})]})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"card mb-8 bg-gradient-to-r from-purple-500 to-indigo-600 text-white",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"社区贡献统计"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t.totalSubmissions}),(0,r.jsx)("div",{className:"text-sm text-purple-200",children:"总提交数"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t.approvedQuestions}),(0,r.jsx)("div",{className:"text-sm text-purple-200",children:"已通过题目"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t.activeContributors}),(0,r.jsx)("div",{className:"text-sm text-purple-200",children:"活跃贡献者"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[t.averageQuality,"/10"]}),(0,r.jsx)("div",{className:"text-sm text-purple-200",children:"平均质量分"})]})]})]}),(0,r.jsx)("div",{className:"card mb-8",children:(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:[{key:"overview",label:"概览",icon:n.Z},{key:"submit",label:"提交指南",icon:c.Z},{key:"contributors",label:"贡献者",icon:d.Z}].map(t=>{let{key:l,label:a,icon:i}=t;return(0,r.jsxs)("button",{onClick:()=>s(l),className:"flex items-center gap-2 px-4 py-2 rounded-lg transition-all ".concat(e===l?"bg-purple-100 text-purple-700 border-purple-300 border-2":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:[(0,r.jsx)(i,{className:"w-4 h-4"}),a]},l)})})}),"overview"===e&&(0,r.jsxs)("div",{className:"card",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-6",children:"最近提交的题目"}),(0,r.jsx)("div",{className:"space-y-4",children:[{id:"Q001",title:'虚假招聘要求交"体检费"的识别方法',author:"防诈小能手",category:"求职骗局",status:"approved",submittedAt:"2024-01-15",votes:23,quality:9.2},{id:"Q002",title:"租房遇到假房东的真实案例分析",author:"谨慎租客",category:"租房陷阱",status:"reviewing",submittedAt:"2024-01-14",votes:18,quality:8.8}].map(e=>(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900 mb-1",children:e.title}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,r.jsxs)("span",{children:["作者: ",e.author]}),(0,r.jsxs)("span",{children:["分类: ",e.category]}),(0,r.jsxs)("span",{children:["提交: ",e.submittedAt]})]})]}),(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(getStatusColor(e.status)),children:getStatusLabel(e.status)})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(o.Z,{className:"w-4 h-4 text-yellow-500"}),(0,r.jsxs)("span",{children:[e.quality,"/10"]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(n.Z,{className:"w-4 h-4 text-blue-500"}),(0,r.jsxs)("span",{children:[e.votes," 票"]})]})]})]},e.id))})]}),"submit"===e&&(0,r.jsxs)("div",{className:"card",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-6",children:"题目提交指南"}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900 mb-3",children:"\uD83D\uDCCB 基于真实案例"}),(0,r.jsx)("p",{className:"text-gray-600 mb-3",children:"题目应该基于真实的诈骗案例"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,r.jsx)("li",{children:"• 亲身经历的诈骗尝试"}),(0,r.jsx)("li",{children:"• 新闻报道的真实案例"}),(0,r.jsx)("li",{children:"• 朋友遭遇的诈骗事件"})]})]}),(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900 mb-3",children:"\uD83C\uDFAF 教育意义明确"}),(0,r.jsx)("p",{className:"text-gray-600 mb-3",children:"题目应该具有明确的教育价值"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,r.jsx)("li",{children:"• 识别诈骗特征"}),(0,r.jsx)("li",{children:"• 防范措施说明"}),(0,r.jsx)("li",{children:"• 正确应对方法"})]})]})]}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)(m(),{href:"/submit-question",className:"btn-primary text-lg px-8 py-4 inline-flex items-center gap-2",children:[(0,r.jsx)(c.Z,{className:"w-5 h-5"}),"立即提交题目"]})})]})]}),"contributors"===e&&(0,r.jsxs)("div",{className:"card",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-6",children:"优秀贡献者"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[{nickname:"防诈专家",contributions:45,approvalRate:96,totalVotes:1250,badge:"\uD83C\uDFC6",level:"专家级"},{nickname:"安全卫士",contributions:38,approvalRate:92,totalVotes:980,badge:"\uD83E\uDD47",level:"高级"}].map((e,s)=>(0,r.jsxs)("div",{className:"text-center p-6 border border-gray-200 rounded-lg",children:[(0,r.jsx)("div",{className:"text-4xl mb-3",children:e.badge}),(0,r.jsx)("div",{className:"font-bold text-lg text-gray-900 mb-1",children:e.nickname}),(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-4",children:e.level}),(0,r.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-3",children:[(0,r.jsx)("div",{className:"text-lg font-bold text-blue-600",children:e.contributions}),(0,r.jsx)("div",{className:"text-blue-700",children:"贡献题目"})]}),(0,r.jsxs)("div",{className:"bg-green-50 rounded-lg p-3",children:[(0,r.jsxs)("div",{className:"text-lg font-bold text-green-600",children:[e.approvalRate,"%"]}),(0,r.jsx)("div",{className:"text-green-700",children:"通过率"})]})]})]},s))})]}),(0,r.jsxs)("div",{className:"card mt-8 bg-gradient-to-r from-green-500 to-blue-600 text-white",children:[(0,r.jsx)("h3",{className:"text-xl font-bold mb-4",children:"\uD83C\uDF81 贡献激励机制"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-2",children:"贡献值奖励"}),(0,r.jsxs)("ul",{className:"text-green-100 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• 提交题目: +1 贡献值"}),(0,r.jsx)("li",{children:"• 题目通过: +5 贡献值"}),(0,r.jsx)("li",{children:"• 获得点赞: +0.1 贡献值"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-2",children:"等级系统"}),(0,r.jsxs)("ul",{className:"text-green-100 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• 新手: 0-10 贡献值"}),(0,r.jsx)("li",{children:"• 中级: 11-50 贡献值"}),(0,r.jsx)("li",{children:"• 高级: 51-100 贡献值"}),(0,r.jsx)("li",{children:"• 专家: 100+ 贡献值"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-2",children:"特殊权益"}),(0,r.jsxs)("ul",{className:"text-green-100 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• 优先审核通道"}),(0,r.jsx)("li",{children:"• 专属贡献者标识"}),(0,r.jsx)("li",{children:"• 参与题库管理"}),(0,r.jsx)("li",{children:"• 获得纪念证书"})]})]})]})]})]})]})}},622:function(e,s,t){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=t(2265),l=Symbol.for("react.element"),a=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,n=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function q(e,s,t){var r,a={},d=null,o=null;for(r in void 0!==t&&(d=""+t),void 0!==s.key&&(d=""+s.key),void 0!==s.ref&&(o=s.ref),s)i.call(s,r)&&!c.hasOwnProperty(r)&&(a[r]=s[r]);if(e&&e.defaultProps)for(r in s=e.defaultProps)void 0===a[r]&&(a[r]=s[r]);return{$$typeof:l,type:e,key:d,ref:o,props:a,_owner:n.current}}s.Fragment=a,s.jsx=q,s.jsxs=q},7437:function(e,s,t){"use strict";e.exports=t(622)},1396:function(e,s,t){e.exports=t(8326)}},function(e){e.O(0,[326,971,472,744],function(){return e(e.s=923)}),_N_E=e.O()}]);