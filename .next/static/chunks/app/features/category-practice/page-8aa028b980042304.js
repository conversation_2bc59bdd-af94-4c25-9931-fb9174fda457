(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[699],{2898:function(e,s,t){"use strict";t.d(s,{Z:function(){return createLucideIcon}});var a=t(2265),r={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let toKebabCase=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),createLucideIcon=(e,s)=>{let t=(0,a.forwardRef)(({color:t="currentColor",size:c=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:n="",children:d,...o},x)=>(0,a.createElement)("svg",{ref:x,...r,width:c,height:c,stroke:t,strokeWidth:l?24*Number(i)/Number(c):i,className:["lucide",`lucide-${toKebabCase(e)}`,n].join(" "),...o},[...s.map(([e,s])=>(0,a.createElement)(e,s)),...Array.isArray(d)?d:[d]]));return t.displayName=`${e}`,t}},3067:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});var a=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},3008:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});var a=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6141:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});var a=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4900:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});var a=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},6654:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});var a=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},2157:function(e,s,t){Promise.resolve().then(t.bind(t,8611))},8611:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return CategoryPracticePage}});var a=t(7437),r=t(2265),c=t(3008),i=t(4900),l=t(6141),n=t(3067),d=t(6654),o=t(1396),x=t.n(o);function CategoryPracticePage(){let[e,s]=(0,r.useState)("beginner"),t={totalCategories:7,completedCategories:3,totalQuestions:180,answeredQuestions:85,averageAccuracy:82.5,studyTime:45},getDifficultyColor=e=>{switch(e){case"beginner":return"text-green-600 bg-green-100";case"intermediate":return"text-yellow-600 bg-yellow-100";case"advanced":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},getDifficultyLabel=e=>{switch(e){case"beginner":return"入门";case"intermediate":return"进阶";case"advanced":return"高级";default:return"未知"}},getStatusIcon=e=>{switch(e){case"completed":return(0,a.jsx)(c.Z,{className:"w-5 h-5 text-green-600"});case"current":return(0,a.jsx)(i.Z,{className:"w-5 h-5 text-blue-600"});case"upcoming":return(0,a.jsx)(l.Z,{className:"w-5 h-5 text-gray-400"});default:return null}};return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-indigo-50 to-purple-100",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(x(),{href:"/",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,a.jsx)(n.Z,{className:"w-5 h-5 text-gray-600"})}),(0,a.jsx)(d.Z,{className:"w-8 h-8 text-indigo-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"分类刷题"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"针对性强化，系统提升"})]})]})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"card mb-8 bg-gradient-to-r from-indigo-500 to-purple-600 text-white",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"学习进度统计"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[t.completedCategories,"/",t.totalCategories]}),(0,a.jsx)("div",{className:"text-sm text-indigo-200",children:"完成分类"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[t.answeredQuestions,"/",t.totalQuestions]}),(0,a.jsx)("div",{className:"text-sm text-indigo-200",children:"答题进度"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[t.averageAccuracy,"%"]}),(0,a.jsx)("div",{className:"text-sm text-indigo-200",children:"平均正确率"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[t.studyTime,"h"]}),(0,a.jsx)("div",{className:"text-sm text-indigo-200",children:"学习时长"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)("div",{className:"card mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"练习分类"}),(0,a.jsx)("div",{className:"flex gap-2",children:["beginner","intermediate","advanced"].map(t=>(0,a.jsx)("button",{onClick:()=>s(t),className:"px-3 py-1 rounded-lg text-sm transition-all ".concat(e===t?getDifficultyColor(t):"bg-gray-100 text-gray-600 hover:bg-gray-200"),children:getDifficultyLabel(t)},t))})]}),(0,a.jsx)("div",{className:"space-y-4",children:[{id:"job-fraud",title:"求职防骗",icon:"\uD83D\uDCBC",color:"blue",description:"识别虚假招聘、培训费诈骗等求职陷阱",difficulty:{beginner:{questions:15,completed:15,accuracy:90},intermediate:{questions:12,completed:8,accuracy:85},advanced:{questions:8,completed:0,accuracy:0}},topics:["虚假招聘识别","培训费陷阱","保证金诈骗","网络兼职风险"],recentProgress:85,estimatedTime:"25分钟",lastStudied:"2024-01-15"},{id:"rental-scam",title:"租房安全",icon:"\uD83C\uDFE0",color:"green",description:"防范假房东、押金诈骗等租房风险",difficulty:{beginner:{questions:12,completed:12,accuracy:88},intermediate:{questions:10,completed:5,accuracy:80},advanced:{questions:6,completed:0,accuracy:0}},topics:["假房东识别","押金安全","租房合同","中介陷阱"],recentProgress:70,estimatedTime:"20分钟",lastStudied:"2024-01-14"},{id:"finance-trap",title:"金融理财",icon:"\uD83D\uDCB0",color:"yellow",description:"识别网贷陷阱、投资诈骗等金融风险",difficulty:{beginner:{questions:18,completed:10,accuracy:75},intermediate:{questions:15,completed:0,accuracy:0},advanced:{questions:12,completed:0,accuracy:0}},topics:["网贷陷阱","投资诈骗","理财产品","利率计算"],recentProgress:45,estimatedTime:"35分钟",lastStudied:"2024-01-13"},{id:"education-scam",title:"教育培训",icon:"\uD83C\uDF93",color:"purple",description:"防范培训诈骗、学历造假等教育陷阱",difficulty:{beginner:{questions:10,completed:0,accuracy:0},intermediate:{questions:8,completed:0,accuracy:0},advanced:{questions:5,completed:0,accuracy:0}},topics:["培训机构","学历认证","技能证书","就业承诺"],recentProgress:0,estimatedTime:"30分钟",lastStudied:null}].map(s=>{let t=s.difficulty[e],r=t.questions>0?t.completed/t.questions*100:0;return(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"text-3xl",children:s.icon}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:s.title}),(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(getDifficultyColor(e)),children:getDifficultyLabel(e)})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:s.description}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-3",children:[(0,a.jsxs)("div",{className:"text-center p-2 bg-blue-50 rounded",children:[(0,a.jsx)("div",{className:"font-bold text-blue-600",children:t.questions}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"题目数"})]}),(0,a.jsxs)("div",{className:"text-center p-2 bg-green-50 rounded",children:[(0,a.jsx)("div",{className:"font-bold text-green-600",children:t.completed}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"已完成"})]}),(0,a.jsxs)("div",{className:"text-center p-2 bg-yellow-50 rounded",children:[(0,a.jsxs)("div",{className:"font-bold text-yellow-600",children:[t.accuracy,"%"]}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"正确率"})]}),(0,a.jsxs)("div",{className:"text-center p-2 bg-purple-50 rounded",children:[(0,a.jsx)("div",{className:"font-bold text-purple-600",children:s.estimatedTime}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"预计时长"})]})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-3",children:s.topics.map((e,s)=>(0,a.jsx)("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded",children:e},s))}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1 mr-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm mb-1",children:[(0,a.jsx)("span",{children:"完成进度"}),(0,a.jsxs)("span",{children:[r.toFixed(0),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-primary-600 h-2 rounded-full transition-all",style:{width:"".concat(r,"%")}})})]}),(0,a.jsxs)(x(),{href:"/category-quiz?category=".concat(s.id,"&difficulty=").concat(e),className:"btn-primary flex items-center gap-2",children:[(0,a.jsx)(i.Z,{className:"w-4 h-4"}),0===t.completed?"开始练习":"继续练习"]})]})]})]})},s.id)})})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"card",children:[(0,a.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"学习计划"}),(0,a.jsx)("div",{className:"space-y-3",children:[{week:1,title:"基础防诈骗认知",categories:["求职防骗","租房安全"],status:"completed",progress:100},{week:2,title:"金融安全意识",categories:["金融理财","网络通信"],status:"current",progress:60},{week:3,title:"高级防范技巧",categories:["教育培训","冒充权威"],status:"upcoming",progress:0},{week:4,title:"综合实战演练",categories:["兼职副业","综合测试"],status:"upcoming",progress:0}].map(e=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[getStatusIcon(e.status),(0,a.jsxs)("span",{className:"font-medium text-gray-900",children:["第",e.week,"周"]})]}),(0,a.jsx)("div",{className:"text-sm text-gray-900 mb-2",children:e.title}),(0,a.jsx)("div",{className:"text-xs text-gray-600 mb-2",children:e.categories.join(" • ")}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-1.5",children:(0,a.jsx)("div",{className:"bg-primary-600 h-1.5 rounded-full transition-all",style:{width:"".concat(e.progress,"%")}})})]},e.week))})]}),(0,a.jsxs)("div",{className:"card bg-gradient-to-r from-green-500 to-blue-600 text-white",children:[(0,a.jsx)("h3",{className:"text-lg font-bold mb-4",children:"\uD83D\uDCA1 学习建议"}),(0,a.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)("span",{children:"\uD83D\uDCDA"}),(0,a.jsx)("span",{children:"建议从入门难度开始，循序渐进"})]}),(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)("span",{children:"\uD83C\uDFAF"}),(0,a.jsx)("span",{children:"专注完成一个分类再进入下一个"})]}),(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)("span",{children:"⏰"}),(0,a.jsx)("span",{children:"每天练习20-30分钟效果最佳"})]}),(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)("span",{children:"\uD83D\uDD04"}),(0,a.jsx)("span",{children:"定期复习错题，巩固知识点"})]})]})]}),(0,a.jsxs)("div",{className:"card",children:[(0,a.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"学习成就"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 p-2 bg-yellow-50 rounded-lg",children:[(0,a.jsx)("span",{className:"text-xl",children:"\uD83C\uDFC6"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"求职防骗专家"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"完成求职防骗所有难度"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 p-2 bg-green-50 rounded-lg",children:[(0,a.jsx)("span",{className:"text-xl",children:"\uD83C\uDFAF"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"精准射手"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"单次练习正确率90%+"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 p-2 bg-blue-50 rounded-lg",children:[(0,a.jsx)("span",{className:"text-xl",children:"\uD83D\uDCDA"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"勤奋学者"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"连续学习7天"})]})]})]})]})]})]})]})]})}},622:function(e,s,t){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var a=t(2265),r=Symbol.for("react.element"),c=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,l=a.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,n={key:!0,ref:!0,__self:!0,__source:!0};function q(e,s,t){var a,c={},d=null,o=null;for(a in void 0!==t&&(d=""+t),void 0!==s.key&&(d=""+s.key),void 0!==s.ref&&(o=s.ref),s)i.call(s,a)&&!n.hasOwnProperty(a)&&(c[a]=s[a]);if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===c[a]&&(c[a]=s[a]);return{$$typeof:r,type:e,key:d,ref:o,props:c,_owner:l.current}}s.Fragment=c,s.jsx=q,s.jsxs=q},7437:function(e,s,t){"use strict";e.exports=t(622)},1396:function(e,s,t){e.exports=t(8326)}},function(e){e.O(0,[326,971,472,744],function(){return e(e.s=2157)}),_N_E=e.O()}]);