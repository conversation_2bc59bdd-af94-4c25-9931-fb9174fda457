(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[810],{2898:function(e,t,a){"use strict";a.d(t,{Z:function(){return createLucideIcon}});var r=a(2265),s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let toKebabCase=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),createLucideIcon=(e,t)=>{let a=(0,r.forwardRef)(({color:a="currentColor",size:l=24,strokeWidth:n=2,absoluteStrokeWidth:i,className:c="",children:o,...d},x)=>(0,r.createElement)("svg",{ref:x,...s,width:l,height:l,stroke:a,strokeWidth:i?24*Number(n)/Number(l):n,className:["lucide",`lucide-${toKebabCase(e)}`,c].join(" "),...d},[...t.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(o)?o:[o]]));return a.displayName=`${e}`,a}},2894:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});var r=a(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r.Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},3067:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});var r=a(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r.Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},3008:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});var r=a(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r.Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6654:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});var r=a(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r.Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},2104:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});var r=a(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r.Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},2369:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});var r=a(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r.Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},8169:function(e,t,a){Promise.resolve().then(a.bind(a,7632))},7632:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return ChallengePage}});var r=a(7437),s=a(2265),l=a(3008),n=a(2104),i=a(3067),c=a(6654),o=a(2369),d=a(2894),x=a(1396),m=a.n(x),p=a(4033),u=a(7809),h=a(4940);function ChallengePage(){let e=(0,p.useRouter)(),[t,a]=(0,s.useState)(null),[x,f]=(0,s.useState)(!1),[g,b]=(0,s.useState)(0),[y,j]=(0,s.useState)(0),[v,N]=(0,s.useState)(0),[w,A]=(0,s.useState)(null),[k,D]=(0,s.useState)(!1),[S,C]=(0,s.useState)(null),[Z,_]=(0,s.useState)(!1),O=h.F.filter(e=>!t||e.category===t),I=O[g];(0,s.useEffect)(()=>{let t;let a=localStorage.getItem("userData");if(!a){e.push("/login");return}let r=localStorage.getItem("lastChallengeDate"),s=new Date().toDateString();if(r===s){alert("今日挑战已完成，请明天再来！"),e.push("/dashboard");return}return x&&!k&&(t=setInterval(()=>{w&&N(Math.floor((Date.now()-w)/1e3))},1e3)),()=>{t&&clearInterval(t)}},[x,k,w,e]);let handleAnswerSelect=e=>{C(e)},finishChallenge=()=>{D(!0);let e={score:y,timeUsed:v,category:t,date:new Date().toISOString(),questionsAnswered:g+1};localStorage.setItem("lastChallengeDate",new Date().toDateString()),localStorage.setItem("lastChallengeResult",JSON.stringify(e));let a=JSON.parse(localStorage.getItem("userData")||"{}");a.contributionScore=(a.contributionScore||0)+y,localStorage.setItem("userData",JSON.stringify(a))},formatTime=e=>"".concat(Math.floor(e/60),":").concat((e%60).toString().padStart(2,"0"));return k?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"max-w-2xl w-full",children:(0,r.jsxs)("div",{className:"card text-center",children:[(0,r.jsxs)("div",{className:"mb-6",children:[y>0?(0,r.jsx)(l.Z,{className:"w-16 h-16 text-success-500 mx-auto mb-4"}):(0,r.jsx)(n.Z,{className:"w-16 h-16 text-danger-500 mx-auto mb-4"}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"挑战完成！"}),(0,r.jsx)("p",{className:"text-gray-600",children:y>0?"恭喜你答对了 ".concat(y," 道题！"):"很遗憾，第一题就答错了，继续加油！"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"bg-primary-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-primary-600",children:y}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"正确题数"})]}),(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:formatTime(v)}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"用时"})]}),(0,r.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:t?u.H[t]:"全部"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"挑战类型"})]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)(m(),{href:"/dashboard",className:"btn-primary",children:"返回主页"}),(0,r.jsx)(m(),{href:"/leaderboard",className:"btn-secondary",children:"查看排行榜"}),(0,r.jsx)(m(),{href:"/share-score",className:"btn-success",children:"分享成绩"})]})]})})}):x?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"card mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(c.Z,{className:"w-8 h-8 text-primary-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"每日挑战进行中"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:t?u.H[t]:"全部类型"})]})]}),(0,r.jsx)("div",{className:"text-right",children:(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-lg font-bold text-primary-600",children:y}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"正确"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-lg font-bold text-blue-600",children:formatTime(v)}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"用时"})]})]})})]})}),(0,r.jsxs)("div",{className:"card mb-8",children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-600 mb-2",children:["第 ",g+1," 题 • ",u.H[I.category]]}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:I.title})]}),(0,r.jsx)("div",{className:"space-y-3",children:I.options.map((e,t)=>(0,r.jsx)("button",{onClick:()=>handleAnswerSelect(t),disabled:Z,className:"quiz-option ".concat(S===t?"selected":""," ").concat(Z?t===I.correctAnswer?"correct":S===t?"incorrect":"":""),children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-6 h-6 rounded-full border-2 border-current flex items-center justify-center text-sm font-medium",children:String.fromCharCode(65+t)}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:e.text}),Z&&(0,r.jsx)("div",{className:"mt-2 text-sm opacity-80",children:e.explanation})]})]})},t))}),Z&&(0,r.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-semibold text-blue-900 mb-2",children:"详细解析："}),(0,r.jsx)("p",{className:"text-blue-800 text-sm",children:I.explanation})]})]}),!Z&&(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("button",{onClick:()=>{confirm("确定要退出挑战吗？今日将无法再次参与。")&&e.push("/dashboard")},className:"btn-secondary",children:"退出挑战"}),(0,r.jsx)("button",{onClick:()=>{if(null===S){alert("请选择一个答案");return}let e=S===I.correctAnswer;_(!0),e?(j(y+1),setTimeout(()=>{g<O.length-1?(b(g+1),C(null),_(!1)):finishChallenge()},2e3)):setTimeout(()=>{finishChallenge()},3e3)},disabled:null===S,className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:"提交答案"})]})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(m(),{href:"/dashboard",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(i.Z,{className:"w-5 h-5 text-gray-600"})}),(0,r.jsx)(c.Z,{className:"w-8 h-8 text-primary-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"每日挑战"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"选择类型开始挑战"})]})]})})}),(0,r.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"card mb-8",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)(o.Z,{className:"w-16 h-16 text-yellow-500 mx-auto mb-4"}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"准备开始挑战"}),(0,r.jsx)("p",{className:"text-gray-600",children:"选择一个诈骗类型，持续答题直到答错。每天只能挑战一次，加油！"})]}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"选择挑战类型："}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,r.jsxs)("button",{onClick:()=>a(null),className:"p-4 border-2 rounded-lg text-left transition-all ".concat(null===t?"border-primary-500 bg-primary-50":"border-gray-200 hover:border-primary-300"),children:[(0,r.jsx)("div",{className:"font-semibold text-gray-900",children:"全部类型"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"混合挑战所有诈骗类型"})]}),Object.entries(u.H).map(e=>{let[s,l]=e;return(0,r.jsxs)("button",{onClick:()=>a(s),className:"p-4 border-2 rounded-lg text-left transition-all ".concat(t===s?"border-primary-500 bg-primary-50":"border-gray-200 hover:border-primary-300"),children:[(0,r.jsx)("div",{className:"font-semibold text-gray-900",children:l}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"专项挑战"})]},s)})]})]}),(0,r.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)(d.Z,{className:"w-5 h-5 text-yellow-600 mt-0.5"}),(0,r.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,r.jsx)("p",{className:"font-medium mb-1",children:"挑战规则："}),(0,r.jsxs)("ul",{className:"space-y-1 text-yellow-700",children:[(0,r.jsx)("li",{children:"• 持续答题直到答错或完成所有题目"}),(0,r.jsx)("li",{children:"• 每天只能参与一次挑战"}),(0,r.jsx)("li",{children:"• 答题数量和用时将计入排行榜"}),(0,r.jsx)("li",{children:"• 完成挑战可获得贡献值奖励"})]})]})]})}),(0,r.jsx)("button",{onClick:()=>{if(!t){alert("请选择一个挑战类型");return}f(!0),A(Date.now())},disabled:void 0===t,className:"w-full btn-primary text-lg py-4 disabled:opacity-50 disabled:cursor-not-allowed",children:"开始挑战"})]})})]})}},4940:function(e,t,a){"use strict";a.d(t,{F:function(){return r}});let r=[{id:"starter-1",title:'你在求职网站上看到一份"高薪轻松"的工作，要求先交纳"培训费"或"保证金"，你应该：',options:[{text:"立即交费，抢占先机",explanation:"正规公司不会要求求职者预先交费，这是典型的求职诈骗手段"},{text:"先交一部分试试",explanation:"任何要求预付费用的招聘都是诈骗，不要抱有侥幸心理"},{text:"拒绝交费，举报该信息",explanation:"正确！正规招聘不会要求求职者交费，应该立即举报"},{text:"和朋友商量后再决定",explanation:"虽然商量是好习惯，但这种明显的诈骗应该直接拒绝"}],correctAnswer:2,category:"job-fraud",difficulty:"basic",explanation:'正规公司招聘员工是为了创造价值，不会向求职者收取任何费用。凡是要求交"培训费"、"保证金"、"服装费"等的招聘信息都是诈骗。',status:"approved",createdAt:new Date("2024-01-01"),approvedAt:new Date("2024-01-01")},{id:"starter-2",title:'接到自称"公安局"的电话，说你涉嫌洗钱需要配合调查，要求你将银行卡内资金转到"安全账户"，你应该：',options:[{text:"立即按要求转账配合调查",explanation:"这是典型的冒充公检法诈骗，真正的执法部门不会通过电话要求转账"},{text:"要求对方提供警官证号码",explanation:"虽然有防范意识，但诈骗分子可能提供虚假信息，最好的做法是直接挂断"},{text:"挂断电话，到就近派出所核实",explanation:"正确！遇到此类电话应立即挂断，到官方机构核实情况"},{text:"先转一部分钱试探真假",explanation:"绝对不能转账！这是诈骗分子常用的心理战术"}],correctAnswer:2,category:"fake-authority",difficulty:"basic",explanation:'公检法机关不会通过电话要求当事人转账汇款，更不存在所谓的"安全账户"。接到此类电话应立即挂断，通过官方渠道核实。',status:"approved",createdAt:new Date("2024-01-01"),approvedAt:new Date("2024-01-01")},{id:"starter-3",title:'在网上看到"日赚500元，在家兼职"的广告，要求先购买"工作包"或"激活码"，你的判断是：',options:[{text:"可能是真的，先试试",explanation:"这是典型的兼职诈骗，正规兼职不需要预付费用"},{text:"肯定是诈骗，直接忽略",explanation:"正确！要求预付费用的兼职广告都是诈骗"},{text:"联系客服详细了解",explanation:'诈骗分子会有专门的"客服"进行洗脑，不要主动联系'},{text:"看看别人的评价再决定",explanation:"网上的评价可能是刷出来的，不能作为判断依据"}],correctAnswer:1,category:"part-time-scam",difficulty:"basic",explanation:'正规的兼职工作是雇主付费给员工，而不是员工先付费给雇主。任何要求预付"工作包"、"激活码"、"保证金"的兼职都是诈骗。',status:"approved",createdAt:new Date("2024-01-01"),approvedAt:new Date("2024-01-01")},{id:"starter-4",title:"收到短信称你的快递丢失，需要点击链接填写信息申请赔偿，你应该：",options:[{text:"立即点击链接填写信息",explanation:"这可能是钓鱼链接，会窃取你的个人信息和银行卡信息"},{text:"先确认是否真的有快递丢失",explanation:"正确！应该通过官方渠道核实快递状态，不要轻信短信"},{text:"转发给朋友帮忙判断",explanation:"不要转发可疑链接，可能会害了朋友"},{text:"直接删除短信",explanation:"删除是对的，但最好还是核实一下是否真有快递"}],correctAnswer:1,category:"telecom-fraud",difficulty:"basic",explanation:"快递公司的正规赔偿流程不会通过短信链接进行。收到此类短信应通过快递公司官方客服或APP核实情况。",status:"approved",createdAt:new Date("2024-01-01"),approvedAt:new Date("2024-01-01")},{id:"starter-5",title:'在租房时，房东要求你先交一年房租和押金，但拒绝签署正式租房合同，声称"信任就够了"，你应该：',options:[{text:"相信房东，先交钱再说",explanation:"没有合同保障的租房存在巨大风险，可能遇到假房东诈骗"},{text:"要求签署正式合同才交钱",explanation:"正确！正规租房必须签署合同，这是保护双方权益的基本要求"},{text:"只交押金，房租按月付",explanation:"虽然降低了风险，但没有合同仍然不安全"},{text:"找中介公司代为处理",explanation:"找正规中介是好方法，但关键还是要有正式合同"}],correctAnswer:1,category:"rental-scam",difficulty:"basic",explanation:"正规租房必须签署租房合同，明确双方的权利义务。拒绝签合同的房东很可能是骗子，要么是假房东，要么想在后续过程中耍赖。",status:"approved",createdAt:new Date("2024-01-01"),approvedAt:new Date("2024-01-01")},{id:"starter-6",title:'网上看到"免费领取iPhone"的活动，只需要填写个人信息和银行卡号"验证身份"，你认为：',options:[{text:"免费的不要白不要，赶紧参加",explanation:"天下没有免费的午餐，这是典型的信息收集诈骗"},{text:"填写信息但不填银行卡号",explanation:"个人信息也很重要，不应该随意泄露给不明网站"},{text:"这是诈骗，不要参与",explanation:'正确！这种"免费领取"活动通常是为了收集个人信息进行诈骗'},{text:"先看看活动是否真实",explanation:"即使活动看起来真实，也不应该向不明网站提供敏感信息"}],correctAnswer:2,category:"telecom-fraud",difficulty:"basic",explanation:'诈骗分子经常用"免费领取"、"中奖"等噱头收集个人信息，然后用于后续的精准诈骗。不要向不明网站提供个人敏感信息。',status:"approved",createdAt:new Date("2024-01-01"),approvedAt:new Date("2024-01-01")},{id:"starter-7",title:'朋友推荐你参加一个"投资理财培训班"，声称"包教包会，月收益30%"，学费需要预交5000元，你应该：',options:[{text:"朋友推荐的应该可信，报名参加",explanation:"即使是朋友推荐，也要理性判断。月收益30%是不现实的承诺"},{text:"先交一部分学费试试效果",explanation:"任何承诺高收益的培训都要谨慎，不要轻易交费"},{text:"拒绝参加，提醒朋友注意风险",explanation:"正确！月收益30%是不可能的，这很可能是传销或诈骗"},{text:"要求先免费试听再决定",explanation:"虽然谨慎，但承诺如此高收益的培训本身就不可信"}],correctAnswer:2,category:"training-scam",difficulty:"basic",explanation:"正规的投资理财年收益能达到10%就已经很不错了，月收益30%是完全不现实的。这种培训通常是传销或诈骗的幌子。",status:"approved",createdAt:new Date("2024-01-01"),approvedAt:new Date("2024-01-01")},{id:"starter-8",title:'急需用钱时，看到网贷广告"无需抵押，当天放款，利息超低"，申请时要求先交"手续费"，你应该：',options:[{text:"急需用钱，先交手续费",explanation:"正规贷款机构不会要求预交手续费，这是典型的贷款诈骗"},{text:"拒绝交费，寻找正规贷款渠道",explanation:"正确！正规贷款不需要预交费用，应该通过银行等正规渠道贷款"},{text:"讨价还价，减少手续费",explanation:"无论多少手续费都不应该交，这就是诈骗"},{text:"要求对方提供营业执照",explanation:"虽然有防范意识，但要求预交费用的贷款本身就是诈骗"}],correctAnswer:1,category:"loan-trap",difficulty:"basic",explanation:'正规的贷款机构（如银行）不会要求借款人预先支付任何费用。所有要求预交"手续费"、"保证金"、"激活费"的贷款都是诈骗。',status:"approved",createdAt:new Date("2024-01-01"),approvedAt:new Date("2024-01-01")},{id:"starter-9",title:'收到银行短信说你的信用卡被盗刷，需要立即点击链接"冻结账户"，你应该：',options:[{text:"立即点击链接冻结账户",explanation:"这可能是钓鱼短信，真正的银行不会通过短信链接处理账户问题"},{text:"拨打银行官方客服电话核实",explanation:"正确！应该通过官方渠道核实情况，不要点击短信中的链接"},{text:"先查看信用卡账单",explanation:"查看账单是好习惯，但更重要的是通过官方渠道核实短信真伪"},{text:"转发给朋友帮忙判断",explanation:"不要转发可疑短信，应该直接联系银行官方客服"}],correctAnswer:1,category:"telecom-fraud",difficulty:"basic",explanation:"银行的正规通知不会要求客户点击短信链接处理账户问题。收到此类短信应拨打银行官方客服电话核实，不要点击任何链接。",status:"approved",createdAt:new Date("2024-01-01"),approvedAt:new Date("2024-01-01")},{id:"starter-10",title:'在社交媒体上看到"代购名牌包包，价格只要专柜的三分之一"的广告，要求先付全款再发货，你认为：',options:[{text:"价格便宜，值得一试",explanation:"价格过低的名牌商品很可能是假货，或者是诈骗"},{text:"要求货到付款",explanation:"虽然降低了风险，但仍可能收到假货，最好通过正规渠道购买"},{text:"这很可能是诈骗，不要购买",explanation:"正确！价格异常低廉的名牌商品很可能是诈骗或假货"},{text:"先付定金，收货后付尾款",explanation:"即使分期付款，也存在收到假货或被骗定金的风险"}],correctAnswer:2,category:"telecom-fraud",difficulty:"basic",explanation:"正品名牌商品有严格的价格体系，价格异常低廉的商品要么是假货，要么是诈骗。应该通过官方渠道或正规商城购买。",status:"approved",createdAt:new Date("2024-01-01"),approvedAt:new Date("2024-01-01")}]},7809:function(e,t,a){"use strict";a.d(t,{H:function(){return r}});let r={"job-fraud":"求职骗局","rental-scam":"租房陷阱","loan-trap":"网贷陷阱","training-scam":"培训诈骗","telecom-fraud":"电信诈骗","fake-authority":"冒充公检法","part-time-scam":"兼职诈骗"}},622:function(e,t,a){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=a(2265),s=Symbol.for("react.element"),l=Symbol.for("react.fragment"),n=Object.prototype.hasOwnProperty,i=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function q(e,t,a){var r,l={},o=null,d=null;for(r in void 0!==a&&(o=""+a),void 0!==t.key&&(o=""+t.key),void 0!==t.ref&&(d=t.ref),t)n.call(t,r)&&!c.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===l[r]&&(l[r]=t[r]);return{$$typeof:s,type:e,key:o,ref:d,props:l,_owner:i.current}}t.Fragment=l,t.jsx=q,t.jsxs=q},7437:function(e,t,a){"use strict";e.exports=a(622)},1396:function(e,t,a){e.exports=a(8326)},4033:function(e,t,a){e.exports=a(94)}},function(e){e.O(0,[326,971,472,744],function(){return e(e.s=8169)}),_N_E=e.O()}]);