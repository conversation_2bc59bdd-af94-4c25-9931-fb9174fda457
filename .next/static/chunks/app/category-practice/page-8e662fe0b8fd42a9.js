(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[470],{2898:function(e,t,r){"use strict";r.d(t,{Z:function(){return createLucideIcon}});var s=r(2265),l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let toKebabCase=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),createLucideIcon=(e,t)=>{let r=(0,s.forwardRef)(({color:r="currentColor",size:i=24,strokeWidth:c=2,absoluteStrokeWidth:a,className:n="",children:o,...d},u)=>(0,s.createElement)("svg",{ref:u,...l,width:i,height:i,stroke:r,strokeWidth:a?24*Number(c)/Number(i):c,className:["lucide",`lucide-${toKebabCase(e)}`,n].join(" "),...d},[...t.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(o)?o:[o]]));return r.displayName=`${e}`,r}},3067:function(e,t,r){"use strict";r.d(t,{Z:function(){return l}});var s=r(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,s.Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9865:function(e,t,r){"use strict";r.d(t,{Z:function(){return l}});var s=r(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,s.Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},6141:function(e,t,r){"use strict";r.d(t,{Z:function(){return l}});var s=r(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,s.Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4900:function(e,t,r){"use strict";r.d(t,{Z:function(){return l}});var s=r(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,s.Z)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},8957:function(e,t,r){"use strict";r.d(t,{Z:function(){return l}});var s=r(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,s.Z)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .*********** 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},2086:function(e,t,r){Promise.resolve().then(r.bind(r,181))},181:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return CategoryPracticePage}});var s=r(7437),l=r(2265),i=r(3067),c=r(9865),a=r(8957),n=r(4900),o=r(6141),d=r(1396),u=r.n(d),x=r(4033);let m=[{category:"job-fraud",title:"求职防骗",description:"识别虚假招聘、培训费诈骗等求职陷阱",icon:"\uD83D\uDCBC",difficulty:"beginner",questionCount:15,completedCount:8,bestScore:87,color:"blue"},{category:"rental-scam",title:"租房安全",description:"防范假房东、押金诈骗等租房风险",icon:"\uD83C\uDFE0",difficulty:"beginner",questionCount:12,completedCount:5,bestScore:75,color:"green"},{category:"loan-trap",title:"金融理财",description:"识别网贷陷阱、投资诈骗等金融风险",icon:"\uD83D\uDCB0",difficulty:"intermediate",questionCount:18,completedCount:3,bestScore:65,color:"yellow"},{category:"training-scam",title:"教育培训",description:"防范培训诈骗、学历造假等教育陷阱",icon:"\uD83C\uDF93",difficulty:"intermediate",questionCount:10,completedCount:0,bestScore:0,color:"purple"},{category:"telecom-fraud",title:"网络通信",description:"识别电信诈骗、钓鱼网站等网络风险",icon:"\uD83D\uDCF1",difficulty:"advanced",questionCount:20,completedCount:2,bestScore:45,color:"indigo"},{category:"fake-authority",title:"冒充权威",description:"识别冒充公检法、政府机构等权威诈骗",icon:"⚖️",difficulty:"advanced",questionCount:8,completedCount:0,bestScore:0,color:"red"},{category:"part-time-scam",title:"兼职副业",description:"防范刷单诈骗、兼职陷阱等副业风险",icon:"\uD83D\uDCBB",difficulty:"beginner",questionCount:14,completedCount:6,bestScore:92,color:"pink"}];function CategoryPracticePage(){let e=(0,x.useRouter)(),[t,r]=(0,l.useState)("all"),[d,p]=(0,l.useState)(null);(0,l.useEffect)(()=>{let e=localStorage.getItem("userData");e&&p(JSON.parse(e))},[]);let g="all"===t?m:m.filter(e=>e.difficulty===t),getDifficultyLabel=e=>({beginner:"入门",intermediate:"进阶",advanced:"高级"})[e]||e,getColorClasses=e=>{let t={blue:"from-blue-500 to-blue-600 border-blue-200 bg-blue-50",green:"from-green-500 to-green-600 border-green-200 bg-green-50",yellow:"from-yellow-500 to-yellow-600 border-yellow-200 bg-yellow-50",purple:"from-purple-500 to-purple-600 border-purple-200 bg-purple-50",indigo:"from-indigo-500 to-indigo-600 border-indigo-200 bg-indigo-50",red:"from-red-500 to-red-600 border-red-200 bg-red-50",pink:"from-pink-500 to-pink-600 border-pink-200 bg-pink-50"};return t[e]||t.blue},handleStartPractice=t=>{localStorage.setItem("selectedCategory",t),d?e.push("/category-challenge?category=".concat(t)):e.push("/demo-quiz?category=".concat(t))};return(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(u(),{href:d?"/dashboard":"/",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,s.jsx)(i.Z,{className:"w-5 h-5 text-gray-600"})}),(0,s.jsx)(c.Z,{className:"w-8 h-8 text-primary-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"分类刷题"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"选择感兴趣的领域，针对性学习防诈骗知识"})]})]})})}),(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"card mb-8",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"难度筛选"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-3",children:["all","beginner","intermediate","advanced"].map(e=>(0,s.jsx)("button",{onClick:()=>r(e),className:"px-4 py-2 rounded-full text-sm font-medium transition-all ".concat(t===e?"bg-primary-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"all"===e?"全部":getDifficultyLabel(e)},e))})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:g.map(e=>(0,s.jsxs)("div",{className:"card hover:shadow-lg transition-all duration-300 border-2 ".concat(getColorClasses(e.color)),children:[(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,s.jsx)("div",{className:"text-4xl",children:e.icon}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:e.title}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,s.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("beginner"===e.difficulty?"bg-green-100 text-green-700":"intermediate"===e.difficulty?"bg-yellow-100 text-yellow-700":"bg-red-100 text-red-700"),children:getDifficultyLabel(e.difficulty)}),(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:[e.questionCount," 道题目"]})]}),(0,s.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"完成进度"}),(0,s.jsxs)("span",{className:"font-medium",children:[e.completedCount,"/",e.questionCount]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"h-2 rounded-full bg-gradient-to-r ".concat(getColorClasses(e.color).split(" ")[0]," ").concat(getColorClasses(e.color).split(" ")[1]),style:{width:"".concat(e.completedCount/e.questionCount*100,"%")}})}),e.bestScore>0&&(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,s.jsx)(a.Z,{className:"w-4 h-4 text-yellow-500"}),(0,s.jsxs)("span",{className:"text-gray-600",children:["最佳成绩: ",e.bestScore,"%"]})]})]}),(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsxs)("button",{onClick:()=>handleStartPractice(e.category),className:"flex-1 btn-primary flex items-center justify-center gap-2",children:[(0,s.jsx)(n.Z,{className:"w-4 h-4"}),"开始练习"]}),e.completedCount>0&&(0,s.jsxs)("button",{className:"btn-secondary flex items-center gap-2",children:[(0,s.jsx)(o.Z,{className:"w-4 h-4"}),"复习"]})]})]},e.category))}),(0,s.jsxs)("div",{className:"card mt-8 bg-gradient-to-r from-primary-500 to-purple-600 text-white",children:[(0,s.jsx)("h3",{className:"text-xl font-bold mb-4",children:"\uD83D\uDCA1 学习建议"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold mb-2",children:"新手推荐路径"}),(0,s.jsxs)("ol",{className:"text-primary-100 text-sm space-y-1",children:[(0,s.jsx)("li",{children:"1. 求职防骗 → 基础必学"}),(0,s.jsx)("li",{children:"2. 租房安全 → 生活必备"}),(0,s.jsx)("li",{children:"3. 兼职副业 → 收入相关"}),(0,s.jsx)("li",{children:"4. 金融理财 → 进阶学习"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold mb-2",children:"学习小贴士"}),(0,s.jsxs)("ul",{className:"text-primary-100 text-sm space-y-1",children:[(0,s.jsx)("li",{children:"• 建议每天练习1-2个分类"}),(0,s.jsx)("li",{children:"• 错题要及时复习巩固"}),(0,s.jsx)("li",{children:"• 结合实际生活场景思考"}),(0,s.jsx)("li",{children:"• 分享经验帮助他人学习"})]})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mt-8",children:[(0,s.jsxs)("div",{className:"card text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-primary-600",children:m.reduce((e,t)=>e+t.questionCount,0)}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"总题目数"})]}),(0,s.jsxs)("div",{className:"card text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-success-600",children:m.reduce((e,t)=>e+t.completedCount,0)}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"已完成"})]}),(0,s.jsxs)("div",{className:"card text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:m.filter(e=>e.bestScore>0).length}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"已掌握分类"})]}),(0,s.jsxs)("div",{className:"card text-center",children:[(0,s.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:[Math.round(m.reduce((e,t)=>e+t.bestScore,0)/m.length),"%"]}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"平均成绩"})]})]})]})]})}},622:function(e,t,r){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var s=r(2265),l=Symbol.for("react.element"),i=Symbol.for("react.fragment"),c=Object.prototype.hasOwnProperty,a=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,n={key:!0,ref:!0,__self:!0,__source:!0};function q(e,t,r){var s,i={},o=null,d=null;for(s in void 0!==r&&(o=""+r),void 0!==t.key&&(o=""+t.key),void 0!==t.ref&&(d=t.ref),t)c.call(t,s)&&!n.hasOwnProperty(s)&&(i[s]=t[s]);if(e&&e.defaultProps)for(s in t=e.defaultProps)void 0===i[s]&&(i[s]=t[s]);return{$$typeof:l,type:e,key:o,ref:d,props:i,_owner:a.current}}t.Fragment=i,t.jsx=q,t.jsxs=q},7437:function(e,t,r){"use strict";e.exports=r(622)},1396:function(e,t,r){e.exports=r(8326)},4033:function(e,t,r){e.exports=r(94)}},function(e){e.O(0,[326,971,472,744],function(){return e(e.s=2086)}),_N_E=e.O()}]);