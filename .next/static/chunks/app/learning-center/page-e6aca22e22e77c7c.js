(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[206],{2898:function(e,s,t){"use strict";t.d(s,{Z:function(){return createLucideIcon}});var r=t(2265),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let toKebabCase=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),createLucideIcon=(e,s)=>{let t=(0,r.forwardRef)(({color:t="currentColor",size:i=24,strokeWidth:c=2,absoluteStrokeWidth:n,className:l="",children:d,...o},x)=>(0,r.createElement)("svg",{ref:x,...a,width:i,height:i,stroke:t,strokeWidth:n?24*Number(c)/Number(i):c,className:["lucide",`lucide-${toKebabCase(e)}`,l].join(" "),...o},[...s.map(([e,s])=>(0,r.createElement)(e,s)),...Array.isArray(d)?d:[d]]));return t.displayName=`${e}`,t}},2894:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},3067:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9865:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},9036:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},5750:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},7200:function(e,s,t){Promise.resolve().then(t.bind(t,6869))},6869:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return LearningCenterPage}});var r=t(7437),a=t(2265),i=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,i.Z)("Briefcase",[["rect",{width:"20",height:"14",x:"2",y:"7",rx:"2",ry:"2",key:"eto64e"}],["path",{d:"M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"zwj3tp"}]]),n=(0,i.Z)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]),l=(0,i.Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]),d=(0,i.Z)("GraduationCap",[["path",{d:"M22 10v6M2 10l10-5 10 5-10 5z",key:"1ef52a"}],["path",{d:"M6 12v5c3 3 9 3 12 0v-5",key:"1f75yj"}]]),o=(0,i.Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);var x=t(9036),m=t(5750),h=t(3067),p=t(9865),u=t(2894),g=t(1396),y=t.n(g),f=t(7809);let j=[{category:"job-fraud",icon:(0,r.jsx)(c,{className:"w-6 h-6"}),title:"求职骗局",description:"利用求职者急于找工作的心理，通过虚假招聘信息进行诈骗。",tips:["正规公司不会要求求职者预先交费",'警惕"高薪轻松"、"日赚千元"等诱人条件',"通过官方渠道核实公司信息","面试地点应在正规办公场所"],cases:['虚假招聘要求交"培训费"、"保证金"',"冒充知名企业进行电话面试诈骗",'以"内推"名义收取"介绍费"',"传销组织伪装成正规公司招聘"],prevention:["通过企业官网或正规招聘平台投递简历","面试前核实公司营业执照和办公地址","不向任何招聘方预付费用","保持警惕，相信常识判断"]},{category:"rental-scam",icon:(0,r.jsx)(n,{className:"w-6 h-6"}),title:"租房陷阱",description:"利用租房需求，通过虚假房源信息或不合理条款进行诈骗。",tips:["实地看房，不要仅凭照片决定","必须签署正式租房合同","核实房东身份和房屋产权","警惕价格明显低于市场价的房源"],cases:["假房东收取定金后消失","一房多租，同时收取多人定金","虚假房源照片，实际房屋条件极差","不签合同，口头承诺后反悔"],prevention:["通过正规中介或房屋租赁平台找房","要求查看房产证和房东身份证","签署正式合同并保留所有凭证","不要一次性支付大额租金"]},{category:"loan-trap",icon:(0,r.jsx)(l,{className:"w-6 h-6"}),title:"网贷陷阱",description:'以"无抵押、快速放款"为诱饵，实际收取高额费用的贷款诈骗。',tips:["正规贷款不需要预交任何费用",'警惕"无条件放款"的虚假承诺',"了解正常贷款流程和利率水平","通过银行等正规金融机构贷款"],cases:['要求预交"手续费"、"保证金"','承诺"无条件放款"后收费不放款',"利率远超法律规定的高利贷","套取个人信息后进行其他诈骗"],prevention:["选择银行或持牌金融机构","仔细阅读贷款合同条款","不向任何贷款机构预付费用","保护个人身份和银行信息"]},{category:"training-scam",icon:(0,r.jsx)(d,{className:"w-6 h-6"}),title:"培训诈骗",description:"以技能培训、学历提升为名，收取高额费用但不提供承诺服务。",tips:["核实培训机构的资质和口碑",'警惕"包过"、"包就业"等承诺',"了解正常的培训费用水平",'不要被"限时优惠"冲昏头脑'],cases:['承诺"包过包就业"收取高额培训费',"虚假宣传培训效果和就业率",'以"内部渠道"名义收取额外费用',"培训内容与宣传严重不符"],prevention:["选择有资质的正规培训机构","实地考察培训环境和师资","签署详细的培训协议","保留所有付费和培训凭证"]},{category:"telecom-fraud",icon:(0,r.jsx)(o,{className:"w-6 h-6"}),title:"电信诈骗",description:"通过电话、短信、网络等方式实施的远程诈骗活动。",tips:["不轻信陌生电话和短信","不点击可疑链接","不向陌生人透露个人信息","遇到可疑情况及时报警"],cases:["冒充银行客服套取银行卡信息","虚假中奖信息要求交税费","冒充快递公司要求点击链接","虚假购物退款要求提供验证码"],prevention:["通过官方渠道核实信息真伪","不在陌生网站输入敏感信息","设置复杂密码并定期更换","开启银行卡短信提醒功能"]},{category:"fake-authority",icon:(0,r.jsx)(x.Z,{className:"w-6 h-6"}),title:"冒充公检法",description:"冒充公安、检察院、法院等执法部门进行的诈骗活动。",tips:["执法部门不会电话要求转账",'不存在所谓的"安全账户"',"正规执法程序不会保密进行","遇到此类电话立即挂断"],cases:["声称涉嫌洗钱要求转账配合调查","冒充法院要求交纳保证金","声称快递涉毒要求证明清白","冒充警察要求提供银行信息"],prevention:["立即挂断可疑电话","到就近派出所核实情况","不向任何人转账汇款","保持冷静，理性判断"]},{category:"part-time-scam",icon:(0,r.jsx)(m.Z,{className:"w-6 h-6"}),title:"兼职诈骗",description:"以兼职工作为诱饵，要求预付费用或套取个人信息的诈骗。",tips:["正规兼职不需要预付费用",'警惕"轻松赚钱"的虚假承诺',"通过正规平台寻找兼职","保护个人身份信息安全"],cases:["刷单兼职要求预付保证金","打字兼职要求购买软件","代理加盟要求交纳代理费","网络兼职套取银行卡信息"],prevention:["选择知名的兼职招聘平台","不向任何兼职方预付费用","核实兼职公司的真实性","保留所有沟通和交易记录"]}];function LearningCenterPage(){let[e,s]=(0,a.useState)("job-fraud"),t=j.find(s=>s.category===e);return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(y(),{href:"/dashboard",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(h.Z,{className:"w-5 h-5 text-gray-600"})}),(0,r.jsx)(p.Z,{className:"w-8 h-8 text-primary-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"学习中心"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"防诈骗知识库"})]})]})})}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"card sticky top-8",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"诈骗类型"}),(0,r.jsx)("nav",{className:"space-y-2",children:j.map(t=>(0,r.jsxs)("button",{onClick:()=>s(t.category),className:"w-full flex items-center gap-3 p-3 rounded-lg text-left transition-all ".concat(e===t.category?"bg-primary-100 text-primary-700 border-primary-200":"hover:bg-gray-50 text-gray-700"),children:[t.icon,(0,r.jsx)("span",{className:"font-medium",children:f.H[t.category]})]},t.category))})]})}),(0,r.jsxs)("div",{className:"lg:col-span-3 space-y-8",children:[(0,r.jsxs)("div",{className:"card",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[t.icon,(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:t.title})]}),(0,r.jsx)("p",{className:"text-gray-600 text-lg",children:t.description})]}),(0,r.jsxs)("div",{className:"card",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,r.jsx)(u.Z,{className:"w-6 h-6 text-yellow-500"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"识别要点"})]}),(0,r.jsx)("ul",{className:"space-y-3",children:t.tips.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center mt-0.5",children:(0,r.jsx)("span",{className:"text-yellow-600 text-sm font-bold",children:s+1})}),(0,r.jsx)("span",{className:"text-gray-700",children:e})]},s))})]}),(0,r.jsxs)("div",{className:"card",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"常见案例"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:t.cases.map((e,s)=>(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-6 h-6 bg-red-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(u.Z,{className:"w-4 h-4 text-red-600"})}),(0,r.jsx)("span",{className:"text-red-800 text-sm",children:e})]})},s))})]}),(0,r.jsxs)("div",{className:"card",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,r.jsx)(x.Z,{className:"w-6 h-6 text-green-500"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"防范措施"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:t.prevention.map((e,s)=>(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(x.Z,{className:"w-4 h-4 text-green-600"})}),(0,r.jsx)("span",{className:"text-green-800 text-sm",children:e})]})},s))})]}),(0,r.jsxs)("div",{className:"card bg-gradient-to-r from-primary-500 to-purple-600 text-white",children:[(0,r.jsx)("h3",{className:"text-xl font-bold mb-4",children:"立即行动"}),(0,r.jsxs)("p",{className:"text-primary-100 mb-6",children:["学习了",t.title,"的防范知识，现在就去挑战相关题目，检验你的学习成果吧！"]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsx)(y(),{href:"/challenge",className:"btn-primary bg-white text-primary-600 hover:bg-gray-100",children:"开始挑战"}),(0,r.jsx)(y(),{href:"/starter-quiz",className:"btn-secondary bg-white/10 text-white border-white/20 hover:bg-white/20",children:"重做入门题"})]})]})]})]})})]})}},7809:function(e,s,t){"use strict";t.d(s,{H:function(){return r}});let r={"job-fraud":"求职骗局","rental-scam":"租房陷阱","loan-trap":"网贷陷阱","training-scam":"培训诈骗","telecom-fraud":"电信诈骗","fake-authority":"冒充公检法","part-time-scam":"兼职诈骗"}},622:function(e,s,t){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=t(2265),a=Symbol.for("react.element"),i=Symbol.for("react.fragment"),c=Object.prototype.hasOwnProperty,n=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function q(e,s,t){var r,i={},d=null,o=null;for(r in void 0!==t&&(d=""+t),void 0!==s.key&&(d=""+s.key),void 0!==s.ref&&(o=s.ref),s)c.call(s,r)&&!l.hasOwnProperty(r)&&(i[r]=s[r]);if(e&&e.defaultProps)for(r in s=e.defaultProps)void 0===i[r]&&(i[r]=s[r]);return{$$typeof:a,type:e,key:d,ref:o,props:i,_owner:n.current}}s.Fragment=i,s.jsx=q,s.jsxs=q},7437:function(e,s,t){"use strict";e.exports=t(622)},1396:function(e,s,t){e.exports=t(8326)}},function(e){e.O(0,[326,971,472,744],function(){return e(e.s=7200)}),_N_E=e.O()}]);