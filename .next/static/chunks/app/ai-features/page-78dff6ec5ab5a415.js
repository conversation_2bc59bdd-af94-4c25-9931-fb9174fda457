(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[761],{2898:function(e,s,r){"use strict";r.d(s,{Z:function(){return createLucideIcon}});var t=r(2265),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let toKebabCase=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),createLucideIcon=(e,s)=>{let r=(0,t.forwardRef)(({color:r="currentColor",size:l=24,strokeWidth:c=2,absoluteStrokeWidth:i,className:d="",children:n,...o},x)=>(0,t.createElement)("svg",{ref:x,...a,width:l,height:l,stroke:r,strokeWidth:i?24*Number(c)/Number(l):c,className:["lucide",`lucide-${toKebabCase(e)}`,d].join(" "),...o},[...s.map(([e,s])=>(0,t.createElement)(e,s)),...Array.isArray(n)?n:[n]]));return r.displayName=`${e}`,r}},3067:function(e,s,r){"use strict";r.d(s,{Z:function(){return a}});var t=r(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t.Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},6654:function(e,s,r){"use strict";r.d(s,{Z:function(){return a}});var t=r(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t.Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},5750:function(e,s,r){"use strict";r.d(s,{Z:function(){return a}});var t=r(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t.Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},2369:function(e,s,r){"use strict";r.d(s,{Z:function(){return a}});var t=r(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t.Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},5196:function(e,s,r){Promise.resolve().then(r.bind(r,885))},885:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return AIFeaturesPage}});var t=r(7437),a=r(2265),l=r(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,l.Z)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]);var i=r(6654);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,l.Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);var n=r(5750),o=r(3067);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let x=(0,l.Z)("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]]),m=(0,l.Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]);var h=r(2369),p=r(1396),u=r.n(p);function AIFeaturesPage(){let[e,s]=(0,a.useState)("question-gen"),r={"question-gen":{title:"AI 智能出题",icon:(0,t.jsx)(c,{className:"w-6 h-6"}),description:"基于最新诈骗案例和用户答题数据，AI 自动生成高质量题目",features:["实时案例分析：从新闻、社交媒体抓取最新诈骗案例","难度智能调节：根据用户水平自动调整题目难度","多样化题型：支持单选、多选、情景判断等多种题型","质量评估：AI 自动评估题目质量和教育价值"],mockData:{generatedQuestions:1247,qualityScore:94.2,categories:["求职骗局","网贷陷阱","电信诈骗"],lastUpdate:"2分钟前"}},personalized:{title:"个性化推荐",icon:(0,t.jsx)(i.Z,{className:"w-6 h-6"}),description:"基于用户行为和学习进度，提供个性化的学习路径和题目推荐",features:["学习路径规划：根据用户薄弱环节制定学习计划","智能题目推荐：推荐最适合当前水平的题目","知识图谱构建：构建个人防诈骗知识体系","进度跟踪：实时跟踪学习进度和效果"],mockData:{userLevel:"中级",weakPoints:["租房陷阱","培训诈骗"],recommendedTopics:3,learningProgress:67}},analytics:{title:"智能数据分析",icon:(0,t.jsx)(d,{className:"w-6 h-6"}),description:"深度分析用户行为和学习效果，提供数据驱动的优化建议",features:["学习效果分析：多维度评估学习效果","行为模式识别：识别用户学习习惯和偏好","风险预警：预测用户可能遇到的诈骗风险","优化建议：基于数据提供个性化改进建议"],mockData:{totalUsers:15420,avgImprovement:23.5,riskReduction:78,satisfactionRate:92.3}},team:{title:"团队协作挑战",icon:(0,t.jsx)(n.Z,{className:"w-6 h-6"}),description:"支持团队组建、协作学习和集体挑战，增强学习互动性",features:["智能组队：基于能力水平自动匹配队友","协作任务：设计需要团队合作完成的挑战","集体竞赛：组织跨团队的知识竞赛","社交学习：促进用户间的知识分享和讨论"],mockData:{activeTeams:342,avgTeamSize:4.2,completionRate:89,collaborationScore:8.7}}},l=r[e];return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100",children:[(0,t.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(u(),{href:"/dashboard",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,t.jsx)(o.Z,{className:"w-5 h-5 text-gray-600"})}),(0,t.jsx)(x,{className:"w-8 h-8 text-purple-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"AI 功能演示"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"体验未来的智能防诈骗教育"})]})]})})}),(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"card mb-8",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-6",children:[(0,t.jsx)(m,{className:"w-6 h-6 text-purple-600"}),(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"AI 功能模块"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:Object.entries(r).map(r=>{let[a,l]=r;return(0,t.jsxs)("button",{onClick:()=>s(a),className:"p-4 rounded-lg border-2 text-left transition-all ".concat(e===a?"border-purple-500 bg-purple-50":"border-gray-200 hover:border-purple-300"),children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,t.jsx)("div",{className:"p-2 rounded-lg ".concat(e===a?"bg-purple-100":"bg-gray-100"),children:l.icon}),(0,t.jsx)("h3",{className:"font-semibold text-gray-900",children:l.title})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:l.description})]},a)})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,t.jsx)("div",{className:"p-3 bg-purple-100 rounded-lg",children:l.icon}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:l.title}),(0,t.jsx)("p",{className:"text-gray-600",children:l.description})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900",children:"核心功能："}),(0,t.jsx)("ul",{className:"space-y-3",children:l.features.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-start gap-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mt-0.5",children:(0,t.jsx)(h.Z,{className:"w-4 h-4 text-purple-600"})}),(0,t.jsx)("span",{className:"text-gray-700",children:e})]},s))})]})]}),(0,t.jsxs)("div",{className:"card bg-gradient-to-r from-purple-500 to-indigo-600 text-white",children:[(0,t.jsx)("h4",{className:"text-lg font-bold mb-2",children:"\uD83D\uDE80 开发状态"}),(0,t.jsx)("p",{className:"text-purple-100 mb-4",children:"此功能正在开发中，预计在下个版本中发布。 当前展示的是功能原型和模拟数据。"}),(0,t.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,t.jsx)("span",{className:"text-sm",children:"开发进度"}),(0,t.jsx)("span",{className:"text-sm font-semibold",children:"65%"})]}),(0,t.jsx)("div",{className:"w-full bg-white/20 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-white h-2 rounded-full",style:{width:"65%"}})})]})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"card",children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:"实时数据 (模拟)"}),(0,t.jsx)("div",{className:"grid grid-cols-2 gap-4",children:Object.entries(l.mockData).map(e=>{let[s,r]=e;return(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-purple-600 mb-1",children:"number"==typeof r?r.toLocaleString():r}),(0,t.jsx)("div",{className:"text-sm text-gray-600 capitalize",children:s.replace(/([A-Z])/g," $1").toLowerCase()})]},s)})})]}),(0,t.jsxs)("div",{className:"card",children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:"功能演示"}),"question-gen"===e&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,t.jsx)("h5",{className:"font-semibold text-blue-900 mb-2",children:"AI 生成题目示例："}),(0,t.jsx)("p",{className:"text-blue-800 text-sm mb-3",children:'"你收到一条短信，声称你的快递因地址不详被退回，需要点击链接重新填写地址信息。你应该："'}),(0,t.jsx)("div",{className:"text-xs text-blue-600",children:"✨ 基于最新快递诈骗案例自动生成"})]}),(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:(0,t.jsx)("div",{className:"text-sm text-green-800",children:"质量评分: 94.2/100 | 难度: 中等 | 分类: 电信诈骗"})})]}),"personalized"===e&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,t.jsx)("h5",{className:"font-semibold text-yellow-900 mb-2",children:"个性化推荐："}),(0,t.jsx)("p",{className:"text-yellow-800 text-sm mb-2",children:"基于你的答题记录，建议重点学习："}),(0,t.jsxs)("ul",{className:"text-yellow-700 text-sm space-y-1",children:[(0,t.jsx)("li",{children:"• 租房陷阱识别技巧"}),(0,t.jsx)("li",{children:"• 培训诈骗防范方法"}),(0,t.jsx)("li",{children:"• 合同条款注意事项"})]})]}),(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:(0,t.jsx)("div",{className:"text-sm text-blue-800",children:"预计学习时间: 15分钟 | 提升效果: +18%"})})]}),"analytics"===e&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[(0,t.jsx)("h5",{className:"font-semibold text-purple-900 mb-2",children:"智能分析报告："}),(0,t.jsxs)("div",{className:"space-y-2 text-purple-800 text-sm",children:[(0,t.jsx)("p",{children:"• 用户整体防诈骗意识提升 23.5%"}),(0,t.jsx)("p",{children:"• 最容易混淆的诈骗类型：网贷陷阱"}),(0,t.jsx)("p",{children:"• 建议增加实际案例练习"})]})]}),(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:(0,t.jsx)("div",{className:"text-sm text-green-800",children:"风险降低: 78% | 用户满意度: 92.3%"})})]}),"team"===e&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"bg-indigo-50 border border-indigo-200 rounded-lg p-4",children:[(0,t.jsx)("h5",{className:"font-semibold text-indigo-900 mb-2",children:"团队挑战示例："}),(0,t.jsx)("p",{className:"text-indigo-800 text-sm mb-2",children:'"防诈小分队" 正在进行协作挑战：'}),(0,t.jsxs)("ul",{className:"text-indigo-700 text-sm space-y-1",children:[(0,t.jsx)("li",{children:"• 队员A: 负责求职诈骗案例分析"}),(0,t.jsx)("li",{children:"• 队员B: 负责租房陷阱识别"}),(0,t.jsx)("li",{children:"• 队员C: 负责综合防范策略"})]})]}),(0,t.jsx)("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-3",children:(0,t.jsx)("div",{className:"text-sm text-orange-800",children:"团队进度: 67% | 协作评分: 8.7/10"})})]})]})]})]}),(0,t.jsxs)("div",{className:"card mt-8 text-center",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"期待您的反馈"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"这些 AI 功能还在开发中，您的意见对我们很重要！"}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsx)("button",{className:"btn-primary",disabled:!0,children:"提交反馈 (开发中)"}),(0,t.jsx)("button",{className:"btn-secondary",disabled:!0,children:"申请内测 (开发中)"}),(0,t.jsx)(u(),{href:"/dashboard",className:"btn-secondary",children:"返回主页"})]})]})]})]})}},622:function(e,s,r){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var t=r(2265),a=Symbol.for("react.element"),l=Symbol.for("react.fragment"),c=Object.prototype.hasOwnProperty,i=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,d={key:!0,ref:!0,__self:!0,__source:!0};function q(e,s,r){var t,l={},n=null,o=null;for(t in void 0!==r&&(n=""+r),void 0!==s.key&&(n=""+s.key),void 0!==s.ref&&(o=s.ref),s)c.call(s,t)&&!d.hasOwnProperty(t)&&(l[t]=s[t]);if(e&&e.defaultProps)for(t in s=e.defaultProps)void 0===l[t]&&(l[t]=s[t]);return{$$typeof:a,type:e,key:n,ref:o,props:l,_owner:i.current}}s.Fragment=l,s.jsx=q,s.jsxs=q},7437:function(e,s,r){"use strict";e.exports=r(622)},1396:function(e,s,r){e.exports=r(8326)}},function(e){e.O(0,[326,971,472,744],function(){return e(e.s=5196)}),_N_E=e.O()}]);