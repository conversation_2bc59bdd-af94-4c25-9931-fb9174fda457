(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[702],{2898:function(e,s,t){"use strict";t.d(s,{Z:function(){return createLucideIcon}});var r=t(2265),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let toKebabCase=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),createLucideIcon=(e,s)=>{let t=(0,r.forwardRef)(({color:t="currentColor",size:l=24,strokeWidth:c=2,absoluteStrokeWidth:i,className:n="",children:d,...o},x)=>(0,r.createElement)("svg",{ref:x,...a,width:l,height:l,stroke:t,strokeWidth:i?24*Number(c)/Number(l):c,className:["lucide",`lucide-${toKebabCase(e)}`,n].join(" "),...o},[...s.map(([e,s])=>(0,r.createElement)(e,s)),...Array.isArray(d)?d:[d]]));return t.displayName=`${e}`,t}},8291:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},9865:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},8203:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},6141:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},9883:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},9036:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},5340:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},6654:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},8957:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},5750:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},2369:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},9294:function(e,s,t){Promise.resolve().then(t.bind(t,227))},227:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return DashboardPage}});var r=t(7437),a=t(2265),l=t(9036),c=t(6141),i=t(8203),n=t(5340),d=t(6654),o=t(8291),x=t(2369),m=t(9883),h=t(8957),u=t(9865),g=t(5750),p=t(1396),f=t.n(p),j=t(4033),y=t(7809);function DashboardPage(){let e=(0,j.useRouter)(),[s,t]=(0,a.useState)(null),[p,N]=(0,a.useState)(null),[v,b]=(0,a.useState)(null),[w,k]=(0,a.useState)(!0);return((0,a.useEffect)(()=>{let s=localStorage.getItem("userData");if(!s){e.push("/login");return}let r=JSON.parse(s);t(r);let a=localStorage.getItem("starterCompleted");if(!a){e.push("/starter-quiz");return}let l=localStorage.getItem("starterResult");l&&N(JSON.parse(l));let c=localStorage.getItem("lastChallengeDate"),i=new Date().toDateString();c===i&&k(!1),b(c)},[e]),s)?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(l.Z,{className:"w-8 h-8 text-primary-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"防诈挑战游戏"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["欢迎回来，",s.nickname]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"贡献值"}),(0,r.jsx)("div",{className:"font-semibold text-primary-600",children:s.contributionScore||0})]}),(0,r.jsx)(f(),{href:"/profile",className:"btn-secondary",children:"个人中心"})]})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[p&&(0,r.jsx)("div",{className:"card mb-8 bg-gradient-to-r from-primary-500 to-purple-600 text-white",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"入门挑战已完成！"}),(0,r.jsxs)("p",{className:"text-primary-100 mb-4",children:["你在入门测试中答对了 ",p.score,"/",p.total," 题， 正确率 ",Math.round(p.score/p.total*100),"%"]}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(c.Z,{className:"w-4 h-4"}),"用时 ",Math.floor(p.timeUsed/60),":",(p.timeUsed%60).toString().padStart(2,"0")]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(i.Z,{className:"w-4 h-4"}),new Date(p.completedAt).toLocaleDateString()]})]})]}),(0,r.jsx)("div",{className:"text-right",children:(0,r.jsx)(n.Z,{className:"w-16 h-16 text-yellow-300"})})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"card",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,r.jsx)("div",{className:"p-2 bg-primary-100 rounded-lg",children:(0,r.jsx)(d.Z,{className:"w-6 h-6 text-primary-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"每日挑战"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"持续答题直到答错"})]})]}),w?(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("div",{className:"bg-success-50 border border-success-200 rounded-lg p-3",children:(0,r.jsx)("p",{className:"text-success-800 text-xs",children:"✨ 今日挑战已准备就绪！"})}),(0,r.jsxs)(f(),{href:"/challenge",className:"btn-primary w-full flex items-center justify-center gap-2 text-sm",children:["开始挑战",(0,r.jsx)(o.Z,{className:"w-4 h-4"})]})]}):(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:(0,r.jsx)("p",{className:"text-yellow-800 text-xs",children:"⏰ 今日挑战已完成"})}),(0,r.jsx)("button",{disabled:!0,className:"btn-primary w-full opacity-50 cursor-not-allowed text-sm",children:"已完成"})]})]}),(0,r.jsxs)("div",{className:"card",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,r.jsx)("div",{className:"p-2 bg-orange-100 rounded-lg",children:(0,r.jsx)(x.Z,{className:"w-6 h-6 text-orange-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"分类刷题"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"针对性强化学习"})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-3",children:(0,r.jsx)("p",{className:"text-orange-800 text-xs",children:"\uD83C\uDFAF 选择感兴趣的领域深度学习"})}),(0,r.jsxs)(f(),{href:"/category-practice",className:"btn-primary bg-orange-600 hover:bg-orange-700 w-full flex items-center justify-center gap-2 text-sm",children:["开始刷题",(0,r.jsx)(d.Z,{className:"w-4 h-4"})]})]})]}),(0,r.jsxs)("div",{className:"card",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,r.jsx)("div",{className:"p-2 bg-success-100 rounded-lg",children:(0,r.jsx)(m.Z,{className:"w-6 h-6 text-success-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"贡献题目"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"分享真实案例"})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:(0,r.jsx)("p",{className:"text-blue-800 text-xs",children:"\uD83D\uDCA1 帮助其他人避免诈骗"})}),(0,r.jsxs)(f(),{href:"/submit-question",className:"btn-success w-full flex items-center justify-center gap-2 text-sm",children:["提交题目",(0,r.jsx)(m.Z,{className:"w-4 h-4"})]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,r.jsx)(f(),{href:"/leaderboard",className:"card hover:shadow-lg transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,r.jsx)(h.Z,{className:"w-6 h-6 text-yellow-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900",children:"排行榜"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"查看挑战排名"})]})]})}),(0,r.jsx)(f(),{href:"/learning-center",className:"card hover:shadow-lg transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,r.jsx)(u.Z,{className:"w-6 h-6 text-purple-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900",children:"学习中心"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"防诈骗知识库"})]})]})}),(0,r.jsx)(f(),{href:"/community",className:"card hover:shadow-lg transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,r.jsx)(g.Z,{className:"w-6 h-6 text-green-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900",children:"社区"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"用户交流分享"})]})]})})]}),(0,r.jsxs)("div",{className:"mt-12",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"\uD83D\uDE80 即将推出"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)(f(),{href:"/ai-features",className:"card hover:shadow-lg transition-shadow bg-gradient-to-r from-purple-50 to-indigo-50 border-purple-200",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,r.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,r.jsx)("span",{className:"text-2xl",children:"\uD83E\uDD16"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900",children:"AI 智能功能"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"智能出题、个性化推荐、数据分析"})]})]}),(0,r.jsx)("div",{className:"bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full inline-block",children:"功能演示"})]}),(0,r.jsxs)(f(),{href:"/multimedia-content",className:"card hover:shadow-lg transition-shadow bg-gradient-to-r from-green-50 to-blue-50 border-green-200",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,r.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,r.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFAC"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900",children:"多媒体内容"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"视频教学、音频课程、互动体验"})]})]}),(0,r.jsx)("div",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full inline-block",children:"内容预览"})]})]})]}),(0,r.jsxs)("div",{className:"mt-12",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"诈骗类型分类"}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4",children:Object.entries(y.H).map(e=>{let[s,t]=e;return(0,r.jsxs)("div",{className:"card text-center p-4 hover:shadow-lg transition-shadow",children:[(0,r.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDEE1️"}),(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:t})]},s)})})]})]})]}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"})})}},7809:function(e,s,t){"use strict";t.d(s,{H:function(){return r}});let r={"job-fraud":"求职骗局","rental-scam":"租房陷阱","loan-trap":"网贷陷阱","training-scam":"培训诈骗","telecom-fraud":"电信诈骗","fake-authority":"冒充公检法","part-time-scam":"兼职诈骗"}},622:function(e,s,t){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=t(2265),a=Symbol.for("react.element"),l=Symbol.for("react.fragment"),c=Object.prototype.hasOwnProperty,i=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,n={key:!0,ref:!0,__self:!0,__source:!0};function q(e,s,t){var r,l={},d=null,o=null;for(r in void 0!==t&&(d=""+t),void 0!==s.key&&(d=""+s.key),void 0!==s.ref&&(o=s.ref),s)c.call(s,r)&&!n.hasOwnProperty(r)&&(l[r]=s[r]);if(e&&e.defaultProps)for(r in s=e.defaultProps)void 0===l[r]&&(l[r]=s[r]);return{$$typeof:a,type:e,key:d,ref:o,props:l,_owner:i.current}}s.Fragment=l,s.jsx=q,s.jsxs=q},7437:function(e,s,t){"use strict";e.exports=t(622)},1396:function(e,s,t){e.exports=t(8326)},4033:function(e,s,t){e.exports=t(94)}},function(e){e.O(0,[326,971,472,744],function(){return e(e.s=9294)}),_N_E=e.O()}]);