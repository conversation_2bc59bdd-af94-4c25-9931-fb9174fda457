(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[736],{2898:function(e,t,a){"use strict";a.d(t,{Z:function(){return createLucideIcon}});var s=a(2265),n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let toKebabCase=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),createLucideIcon=(e,t)=>{let a=(0,s.forwardRef)(({color:a="currentColor",size:r=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:c="",children:o,...d},m)=>(0,s.createElement)("svg",{ref:m,...n,width:r,height:r,stroke:a,strokeWidth:i?24*Number(l)/Number(r):l,className:["lucide",`lucide-${toKebabCase(e)}`,c].join(" "),...d},[...t.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(o)?o:[o]]));return a.displayName=`${e}`,a}},2894:function(e,t,a){"use strict";a.d(t,{Z:function(){return n}});var s=a(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,s.Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},3067:function(e,t,a){"use strict";a.d(t,{Z:function(){return n}});var s=a(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,s.Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},3008:function(e,t,a){"use strict";a.d(t,{Z:function(){return n}});var s=a(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,s.Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6141:function(e,t,a){"use strict";a.d(t,{Z:function(){return n}});var s=a(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,s.Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},9883:function(e,t,a){"use strict";a.d(t,{Z:function(){return n}});var s=a(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,s.Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5588:function(e,t,a){Promise.resolve().then(a.bind(a,1043))},1043:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return SubmitQuestionPage}});var s=a(7437),n=a(2265),r=a(3008),l=a(3067),i=a(9883),c=a(2894),o=a(6141),d=a(1396),m=a.n(d),x=a(4033),u=a(7809);function SubmitQuestionPage(){let e=(0,x.useRouter)(),[t,a]=(0,n.useState)(null),[d,h]=(0,n.useState)({title:"",options:["","","",""],correctAnswer:0,category:"job-fraud",explanation:"",source:"",optionExplanations:["","","",""]}),[p,g]=(0,n.useState)({}),[f,b]=(0,n.useState)(!1),[j,N]=(0,n.useState)(!1);(0,n.useEffect)(()=>{let e=localStorage.getItem("userData");e?a(JSON.parse(e)):a({id:"demo_user",nickname:"体验用户",contributionScore:0})},[e]);let handleInputChange=(e,t)=>{h(a=>({...a,[e]:t})),p[e]&&g(t=>({...t,[e]:""}))},handleOptionChange=(e,t)=>{let a=[...d.options];a[e]=t,h(e=>({...e,options:a})),p["option".concat(e)]&&g(t=>({...t,["option".concat(e)]:""}))},handleOptionExplanationChange=(e,t)=>{let a=[...d.optionExplanations];a[e]=t,h(e=>({...e,optionExplanations:a}))},validateForm=()=>{let e={};return d.title.trim()?d.title.length<10&&(e.title="题目内容至少需要10个字符"):e.title="请输入题目内容",d.options.forEach((t,a)=>{t.trim()||(e["option".concat(a)]="请输入选项".concat(String.fromCharCode(65+a),"的内容"))}),d.explanation.trim()?d.explanation.length<20&&(e.explanation="详细解析至少需要20个字符"):e.explanation="请输入详细解析",d.source.trim()||(e.source="请输入案例来源"),d.optionExplanations.forEach((t,a)=>{t.trim()||(e["optionExplanation".concat(a)]="请输入选项".concat(String.fromCharCode(65+a),"的解析"))}),g(e),0===Object.keys(e).length},handleSubmit=async e=>{if(e.preventDefault(),validateForm()){b(!0);try{await new Promise(e=>setTimeout(e,2e3));let e={...d,id:"submission_".concat(Date.now()),authorId:t.id,authorNickname:t.nickname,status:"pending",submittedAt:new Date().toISOString()},s=JSON.parse(localStorage.getItem("questionSubmissions")||"[]");s.push(e),localStorage.setItem("questionSubmissions",JSON.stringify(s));let n={...t,contributionScore:(t.contributionScore||0)+1};localStorage.setItem("userData",JSON.stringify(n)),a(n),N(!0)}catch(e){g({general:"提交失败，请重试"})}finally{b(!1)}}};return t?j?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"max-w-md w-full",children:(0,s.jsxs)("div",{className:"card text-center",children:[(0,s.jsx)(r.Z,{className:"w-16 h-16 text-success-500 mx-auto mb-4"}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"提交成功！"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"感谢你的贡献！你的题目已提交审核，通过后将加入题库供其他用户挑战。"}),(0,s.jsx)("div",{className:"bg-success-50 border border-success-200 rounded-lg p-4 mb-6",children:(0,s.jsxs)("p",{className:"text-success-800 text-sm",children:["\uD83C\uDF89 你获得了 1 贡献值！当前贡献值：",t.contributionScore]})}),(0,s.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,s.jsx)(m(),{href:"/dashboard",className:"btn-primary",children:"返回主页"}),(0,s.jsx)("button",{onClick:()=>{N(!1),h({title:"",options:["","","",""],correctAnswer:0,category:"job-fraud",explanation:"",source:"",optionExplanations:["","","",""]})},className:"btn-secondary",children:"继续提交题目"})]})]})})}):(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(m(),{href:"/dashboard",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,s.jsx)(l.Z,{className:"w-5 h-5 text-gray-600"})}),(0,s.jsx)(i.Z,{className:"w-8 h-8 text-success-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"提交题目"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"分享真实案例，帮助更多人防范诈骗"})]})]})})}),(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsx)("div",{className:"card mb-8 bg-blue-50 border border-blue-200",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(c.Z,{className:"w-6 h-6 text-blue-600 mt-1"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-2",children:"提交须知"}),(0,s.jsxs)("ul",{className:"text-blue-800 text-sm space-y-1",children:[(0,s.jsx)("li",{children:"• 请基于真实案例或常见诈骗手段出题"}),(0,s.jsx)("li",{children:"• 题目内容应具有教育意义，帮助他人识别诈骗"}),(0,s.jsx)("li",{children:"• 所有选项都需要提供详细解析说明"}),(0,s.jsx)("li",{children:"• 提交后将进入审核流程，通过后加入正式题库"}),(0,s.jsx)("li",{children:"• 成功贡献题目可获得贡献值奖励"})]})]})]})}),(0,s.jsxs)("form",{onSubmit:handleSubmit,className:"space-y-8",children:[p.general&&(0,s.jsx)("div",{className:"bg-danger-50 border border-danger-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(c.Z,{className:"w-5 h-5 text-danger-600"}),(0,s.jsx)("span",{className:"text-danger-800",children:p.general})]})}),(0,s.jsxs)("div",{className:"card",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-6",children:"基本信息"}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"诈骗类型 *"}),(0,s.jsx)("select",{value:d.category,onChange:e=>handleInputChange("category",e.target.value),className:"input-field",children:Object.entries(u.H).map(e=>{let[t,a]=e;return(0,s.jsx)("option",{value:t,children:a},t)})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"题目内容 *"}),(0,s.jsx)("textarea",{value:d.title,onChange:e=>handleInputChange("title",e.target.value),className:"input-field h-24 ".concat(p.title?"border-danger-300":""),placeholder:"请描述一个具体的诈骗场景，让用户判断应该如何应对..."}),p.title&&(0,s.jsx)("p",{className:"mt-1 text-sm text-danger-600",children:p.title})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"案例来源 *"}),(0,s.jsx)("input",{type:"text",value:d.source,onChange:e=>handleInputChange("source",e.target.value),className:"input-field ".concat(p.source?"border-danger-300":""),placeholder:"例如：亲身经历、新闻报道、朋友遭遇等"}),p.source&&(0,s.jsx)("p",{className:"mt-1 text-sm text-danger-600",children:p.source})]})]})]}),(0,s.jsxs)("div",{className:"card",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-6",children:"答案选项"}),(0,s.jsx)("div",{className:"space-y-6",children:d.options.map((e,t)=>(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-primary-600 font-semibold",children:String.fromCharCode(65+t)})}),(0,s.jsx)("input",{type:"radio",name:"correctAnswer",checked:d.correctAnswer===t,onChange:()=>handleInputChange("correctAnswer",t),className:"text-primary-600"}),(0,s.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"正确答案"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("input",{type:"text",value:e,onChange:e=>handleOptionChange(t,e.target.value),className:"input-field ".concat(p["option".concat(t)]?"border-danger-300":""),placeholder:"选项".concat(String.fromCharCode(65+t),"内容")}),p["option".concat(t)]&&(0,s.jsx)("p",{className:"mt-1 text-sm text-danger-600",children:p["option".concat(t)]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("textarea",{value:d.optionExplanations[t],onChange:e=>handleOptionExplanationChange(t,e.target.value),className:"input-field h-20 ".concat(p["optionExplanation".concat(t)]?"border-danger-300":""),placeholder:"选项".concat(String.fromCharCode(65+t),"的解析说明")}),p["optionExplanation".concat(t)]&&(0,s.jsx)("p",{className:"mt-1 text-sm text-danger-600",children:p["optionExplanation".concat(t)]})]})]})]},t))})]}),(0,s.jsxs)("div",{className:"card",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-6",children:"详细解析"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"题目解析 *"}),(0,s.jsx)("textarea",{value:d.explanation,onChange:e=>handleInputChange("explanation",e.target.value),className:"input-field h-32 ".concat(p.explanation?"border-danger-300":""),placeholder:"请详细解释为什么这样选择是正确的，以及这类诈骗的特点和防范方法..."}),p.explanation&&(0,s.jsx)("p",{className:"mt-1 text-sm text-danger-600",children:p.explanation})]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)(m(),{href:"/dashboard",className:"btn-secondary",children:"取消"}),(0,s.jsx)("button",{type:"submit",disabled:f,className:"btn-success flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed",children:f?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.Z,{className:"w-5 h-5 animate-spin"}),"提交中..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.Z,{className:"w-5 h-5"}),"提交题目"]})})]})]})]})]}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"})})}},7809:function(e,t,a){"use strict";a.d(t,{H:function(){return s}});let s={"job-fraud":"求职骗局","rental-scam":"租房陷阱","loan-trap":"网贷陷阱","training-scam":"培训诈骗","telecom-fraud":"电信诈骗","fake-authority":"冒充公检法","part-time-scam":"兼职诈骗"}},622:function(e,t,a){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var s=a(2265),n=Symbol.for("react.element"),r=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,i=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function q(e,t,a){var s,r={},o=null,d=null;for(s in void 0!==a&&(o=""+a),void 0!==t.key&&(o=""+t.key),void 0!==t.ref&&(d=t.ref),t)l.call(t,s)&&!c.hasOwnProperty(s)&&(r[s]=t[s]);if(e&&e.defaultProps)for(s in t=e.defaultProps)void 0===r[s]&&(r[s]=t[s]);return{$$typeof:n,type:e,key:o,ref:d,props:r,_owner:i.current}}t.Fragment=r,t.jsx=q,t.jsxs=q},7437:function(e,t,a){"use strict";e.exports=a(622)},1396:function(e,t,a){e.exports=a(8326)},4033:function(e,t,a){e.exports=a(94)}},function(e){e.O(0,[326,971,472,744],function(){return e(e.s=5588)}),_N_E=e.O()}]);