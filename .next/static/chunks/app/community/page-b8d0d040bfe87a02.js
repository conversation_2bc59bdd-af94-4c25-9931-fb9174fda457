(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[616],{2898:function(e,t,s){"use strict";s.d(t,{Z:function(){return createLucideIcon}});var r=s(2265),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let toKebabCase=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),createLucideIcon=(e,t)=>{let s=(0,r.forwardRef)(({color:s="currentColor",size:i=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:c="",children:o,...d},m)=>(0,r.createElement)("svg",{ref:m,...a,width:i,height:i,stroke:s,strokeWidth:n?24*Number(l)/Number(i):l,className:["lucide",`lucide-${toKebabCase(e)}`,c].join(" "),...d},[...t.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(o)?o:[o]]));return s.displayName=`${e}`,s}},2894:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},3067:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},3008:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6141:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},2176:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]])},9868:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z",key:"y3tblf"}]])},5750:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var r=s(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},3505:function(e,t,s){Promise.resolve().then(s.bind(s,852))},852:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return CommunityPage}});var r=s(7437),a=s(2265),i=s(3067),l=s(5750),n=s(2894),c=s(2176),o=s(3008),d=s(6141),m=s(9868),x=s(1396),h=s.n(x);let u=[{id:"1",author:"防诈达人",title:"刚刚遇到的求职诈骗，差点上当！",content:'今天接到一个电话说是某知名公司HR，要我去面试。但是要求我先交500元的"资料审核费"，我想起在防诈游戏里学到的知识，立即拒绝了。正规公司绝对不会要求求职者交费！',category:"求职骗局",likes:23,comments:8,createdAt:"2024-01-15T10:30:00Z",isVerified:!0},{id:"2",author:"谨慎学子",title:"租房遇到假房东，幸好及时发现",content:"在网上看到一个价格很便宜的房子，房东说要先交定金。我要求看房产证，对方各种推脱。后来发现照片是盗用的，差点被骗！大家租房一定要实地看房，核实房东身份。",category:"租房陷阱",likes:18,comments:12,createdAt:"2024-01-14T15:20:00Z",isVerified:!1},{id:"3",author:"理性思考",title:"网贷诈骗套路深，大家要小心",content:'最近收到很多"无抵押快速放款"的短信，点进去看要求先交手续费。记住：正规贷款绝对不会要求预付费用！需要贷款的同学一定要通过银行等正规渠道。',category:"网贷陷阱",likes:31,comments:15,createdAt:"2024-01-13T09:45:00Z",isVerified:!0},{id:"4",author:"警觉青年",title:"冒充公检法电话，差点被吓到",content:'昨天接到电话说我涉嫌洗钱，要求我配合调查转账到"安全账户"。刚开始真的被吓到了，但想起游戏里的知识，公检法绝对不会电话要求转账！立即挂断并报警。',category:"冒充公检法",likes:27,comments:9,createdAt:"2024-01-12T14:10:00Z",isVerified:!0},{id:"5",author:"细心观察",title:"兼职刷单诈骗经历分享",content:'朋友介绍了一个"刷单兼职"，说日赚200很轻松。要求先垫付商品款，承诺立即返还并给佣金。幸好我在防诈游戏里学过，所有要求垫付的兼职都是诈骗！',category:"兼职诈骗",likes:22,comments:11,createdAt:"2024-01-11T16:30:00Z",isVerified:!1}];function CommunityPage(){let[e,t]=(0,a.useState)("all"),[s]=(0,a.useState)(u),x={all:"全部",求职骗局:"求职骗局",租房陷阱:"租房陷阱",网贷陷阱:"网贷陷阱",培训诈骗:"培训诈骗",电信诈骗:"电信诈骗",冒充公检法:"冒充公检法",兼职诈骗:"兼职诈骗"},f="all"===e?s:s.filter(t=>t.category===e),formatTimeAgo=e=>{let t=new Date(e),s=new Date,r=Math.floor((s.getTime()-t.getTime())/36e5);if(r<1)return"刚刚";if(r<24)return"".concat(r,"小时前");let a=Math.floor(r/24);return a<7?"".concat(a,"天前"):t.toLocaleDateString()};return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(h(),{href:"/dashboard",className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(i.Z,{className:"w-5 h-5 text-gray-600"})}),(0,r.jsx)(l.Z,{className:"w-8 h-8 text-primary-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"社区"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"分享防诈骗经验，互相学习"})]})]})})}),(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"card mb-8",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"分类筛选"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:["all","求职骗局","租房陷阱","网贷陷阱","培训诈骗","电信诈骗","冒充公检法","兼职诈骗"].map(s=>(0,r.jsx)("button",{onClick:()=>t(s),className:"px-4 py-2 rounded-full text-sm font-medium transition-all ".concat(e===s?"bg-primary-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:x[s]},s))})]}),(0,r.jsx)("div",{className:"card mb-8 bg-gradient-to-r from-blue-500 to-purple-600 text-white",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)(n.Z,{className:"w-6 h-6 text-yellow-300 mt-1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-bold mb-2",children:"社区公告"}),(0,r.jsx)("p",{className:"text-blue-100 mb-4",children:"欢迎大家分享防诈骗经验！请注意保护个人隐私，不要透露具体的个人信息。 让我们一起建设一个安全、友善的学习社区。"}),(0,r.jsxs)("div",{className:"text-sm text-blue-200",children:["• 分享真实经历，帮助他人避免诈骗",(0,r.jsx)("br",{}),"• 保护个人隐私，不透露敏感信息",(0,r.jsx)("br",{}),"• 理性讨论，文明交流"]})]})]})}),(0,r.jsx)("div",{className:"space-y-6",children:0===f.length?(0,r.jsxs)("div",{className:"card text-center py-12",children:[(0,r.jsx)(c.Z,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-500",children:"暂无相关帖子"})]}):f.map(e=>(0,r.jsxs)("div",{className:"card hover:shadow-lg transition-shadow",children:[(0,r.jsx)("div",{className:"flex items-start justify-between mb-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(l.Z,{className:"w-5 h-5 text-primary-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"font-semibold text-gray-900",children:e.author}),e.isVerified&&(0,r.jsx)(o.Z,{className:"w-4 h-4 text-blue-500"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[(0,r.jsx)(d.Z,{className:"w-4 h-4"}),formatTimeAgo(e.createdAt),(0,r.jsx)("span",{className:"px-2 py-1 bg-gray-100 rounded-full text-xs",children:e.category})]})]})]})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:e.title}),(0,r.jsx)("p",{className:"text-gray-700 mb-4 leading-relaxed",children:e.content}),(0,r.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-gray-200",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("button",{className:"flex items-center gap-2 text-gray-500 hover:text-primary-600 transition-colors",children:[(0,r.jsx)(m.Z,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-sm",children:e.likes})]}),(0,r.jsxs)("button",{className:"flex items-center gap-2 text-gray-500 hover:text-primary-600 transition-colors",children:[(0,r.jsx)(c.Z,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-sm",children:e.comments})]})]}),(0,r.jsx)("button",{className:"text-sm text-primary-600 hover:text-primary-700 font-medium",children:"查看详情"})]})]},e.id))}),(0,r.jsxs)("div",{className:"card mt-8 text-center",children:[(0,r.jsx)(c.Z,{className:"w-12 h-12 text-primary-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"分享你的经验"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"遇到过诈骗或有防诈骗心得？分享给大家，帮助更多人提高警惕！"}),(0,r.jsx)("button",{className:"btn-primary",disabled:!0,children:"发布帖子 (开发中)"})]})]})]})}},622:function(e,t,s){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=s(2265),a=Symbol.for("react.element"),i=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,n=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function q(e,t,s){var r,i={},o=null,d=null;for(r in void 0!==s&&(o=""+s),void 0!==t.key&&(o=""+t.key),void 0!==t.ref&&(d=t.ref),t)l.call(t,r)&&!c.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===i[r]&&(i[r]=t[r]);return{$$typeof:a,type:e,key:o,ref:d,props:i,_owner:n.current}}t.Fragment=i,t.jsx=q,t.jsxs=q},7437:function(e,t,s){"use strict";e.exports=s(622)},1396:function(e,t,s){e.exports=s(8326)}},function(e){e.O(0,[326,971,472,744],function(){return e(e.s=3505)}),_N_E=e.O()}]);