{"version": 3, "sources": ["../../../src/export/helpers/create-incremental-cache.ts"], "names": ["createIncrementalCache", "incremental<PERSON>ache<PERSON>andlerPath", "isrMemoryCacheSize", "fetchCacheKeyPrefix", "distDir", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "require", "default", "incrementalCache", "IncrementalCache", "dev", "requestHeaders", "flushToDisk", "fetchCache", "maxMemoryCacheSize", "getPrerenderManifest", "version", "routes", "dynamicRoutes", "preview", "previewModeEncryptionKey", "previewModeId", "previewModeSigningKey", "notFoundRoutes", "fs", "readFile", "promises", "readFileSync", "writeFile", "f", "d", "mkdir", "dir", "recursive", "stat", "serverDistDir", "path", "join", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minimalMode", "hasNextSupport", "globalThis", "__incrementalCache"], "mappings": ";;;;+BAKgBA;;;eAAAA;;;6DALC;2DACF;kCACkB;wBACF;;;;;;AAExB,SAASA,uBACdC,2BAA+C,EAC/CC,kBAAsC,EACtCC,mBAAuC,EACvCC,OAAe;IAEf,kCAAkC;IAClC,IAAIC;IACJ,IAAIJ,6BAA6B;QAC/BI,eAAeC,QAAQL;QACvBI,eAAeA,aAAaE,OAAO,IAAIF;IACzC;IAEA,MAAMG,mBAAmB,IAAIC,kCAAgB,CAAC;QAC5CC,KAAK;QACLC,gBAAgB,CAAC;QACjBC,aAAa;QACbC,YAAY;QACZC,oBAAoBZ;QACpBC;QACAY,sBAAsB,IAAO,CAAA;gBAC3BC,SAAS;gBACTC,QAAQ,CAAC;gBACTC,eAAe,CAAC;gBAChBC,SAAS;oBACPC,0BAA0B;oBAC1BC,eAAe;oBACfC,uBAAuB;gBACzB;gBACAC,gBAAgB,EAAE;YACpB,CAAA;QACAC,IAAI;YACFC,UAAUD,WAAE,CAACE,QAAQ,CAACD,QAAQ;YAC9BE,cAAcH,WAAE,CAACG,YAAY;YAC7BC,WAAW,CAACC,GAAGC,IAAMN,WAAE,CAACE,QAAQ,CAACE,SAAS,CAACC,GAAGC;YAC9CC,OAAO,CAACC,MAAQR,WAAE,CAACE,QAAQ,CAACK,KAAK,CAACC,KAAK;oBAAEC,WAAW;gBAAK;YACzDC,MAAM,CAACL,IAAML,WAAE,CAACE,QAAQ,CAACQ,IAAI,CAACL;QAChC;QACAM,eAAeC,aAAI,CAACC,IAAI,CAACjC,SAAS;QAClCkC,iBAAiBjC;QACjBkC,aAAaC,sBAAc;IAC7B;IAEEC,WAAmBC,kBAAkB,GAAGlC;IAE1C,OAAOA;AACT"}