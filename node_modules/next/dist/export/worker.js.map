{"version": 3, "sources": ["../../src/export/worker.ts"], "names": ["exportPage", "process", "env", "NEXT_IS_EXPORT_WORKER", "envConfig", "require", "globalThis", "__NEXT_DATA__", "nextExport", "exportPageImpl", "input", "fileWriter", "path", "pathMap", "distDir", "pagesDataDir", "buildExport", "serverRuntimeConfig", "subFolders", "optimizeFonts", "optimizeCss", "disableOptimizedLoading", "debugOutput", "isrMemoryCacheSize", "fetchCache", "fetchCacheKeyPrefix", "incremental<PERSON>ache<PERSON>andlerPath", "enableExperimentalReact", "ampValidator<PERSON>ath", "trailingSlash", "__NEXT_EXPERIMENTAL_REACT", "page", "_isAppDir", "isAppDir", "_isAppPrefetch", "isAppPrefetch", "_isDynamicError", "isDynamicError", "query", "originalQuery", "req", "pathname", "normalizeAppPath", "isDynamic", "isDynamicRoute", "outDir", "join", "params", "filePath", "normalizePagePath", "ampPath", "renderAmpPath", "updatedPath", "__nextSsgPath", "locale", "__next<PERSON><PERSON><PERSON>", "renderOpts", "localePathResult", "normalizeLocalePath", "locales", "detectedLocale", "defaultLocale", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "length", "nonLocalizedPath", "normalizedPage", "getParams", "res", "createRequestResponseMocks", "url", "statusCode", "some", "p", "endsWith", "domainLocales", "dl", "includes", "addRequestMeta", "setConfig", "publicRuntimeConfig", "runtimeConfig", "getHtmlFilename", "sep", "htmlFilename", "pageExt", "extname", "pathExt", "isBuiltinPaths", "isHtmlExtPath", "baseDir", "dirname", "htmlFilepath", "fs", "mkdir", "recursive", "incrementalCache", "createIncrementalCache", "undefined", "isAppRouteRoute", "exportAppRoute", "components", "loadComponents", "isAppPath", "fontManifest", "requireFontManifest", "supportsDynamicHTML", "originalPathname", "hasNextSupport", "isRevalidate", "exportAppPage", "exportPages", "err", "console", "error", "isError", "stack", "setHttpClientAndAgentOptions", "httpAgentOptions", "files", "baseFileWriter", "type", "content", "encodingOptions", "writeFile", "push", "exportPageSpan", "trace", "parentSpanId", "start", "Date", "now", "result", "traceAsyncFn", "duration", "ampValidations", "revalidate", "metadata", "ssgNotFound", "hasEmptyPrelude", "hasPostponed", "on", "isPostpone"], "mappings": ";;;;+BA2TA;;;eAA8BA;;;QAlTvB;sBAIqC;iEAC7B;gCACgB;2BACA;mCACG;yBACE;qCACA;uBACd;mCACuB;gEACzB;6BACW;0BACE;6BAEU;iCACX;wBACD;0BACA;yBACD;uBACF;2BACF;wCACa;4BACZ;;;;;;AAvB3BC,QAAQC,GAAG,CAACC,qBAAqB,GAAG;AAyBpC,MAAMC,YAAYC,QAAQ;AAExBC,WAAmBC,aAAa,GAAG;IACnCC,YAAY;AACd;AAEA,eAAeC,eACbC,KAAsB,EACtBC,UAAsB;IAEtB,MAAM,EACJC,IAAI,EACJC,OAAO,EACPC,OAAO,EACPC,YAAY,EACZC,cAAc,KAAK,EACnBC,mBAAmB,EACnBC,aAAa,KAAK,EAClBC,aAAa,EACbC,WAAW,EACXC,uBAAuB,EACvBC,cAAc,KAAK,EACnBC,kBAAkB,EAClBC,UAAU,EACVC,mBAAmB,EACnBC,2BAA2B,EAC3BC,uBAAuB,EACvBC,gBAAgB,EAChBC,aAAa,EACd,GAAGnB;IAEJ,IAAIiB,yBAAyB;QAC3B1B,QAAQC,GAAG,CAAC4B,yBAAyB,GAAG;IAC1C;IAEA,MAAM,EACJC,IAAI,EAEJ,mCAAmC;IACnCC,WAAWC,WAAW,KAAK,EAE3B,6CAA6C;IAC7CC,gBAAgBC,gBAAgB,KAAK,EAErC,6DAA6D;IAC7DC,iBAAiBC,iBAAiB,KAAK,EAEvC,+BAA+B;IAC/BC,OAAOC,gBAAgB,CAAC,CAAC,EAC1B,GAAG1B;IAEJ,IAAI;YAwEoB2B;QAvEtB,IAAIF,QAAQ;YAAE,GAAGC,aAAa;QAAC;QAC/B,MAAME,WAAWC,IAAAA,0BAAgB,EAACX;QAClC,MAAMY,YAAYC,IAAAA,yBAAc,EAACb;QACjC,MAAMc,SAASZ,WAAWa,IAAAA,UAAI,EAAChC,SAAS,gBAAgBJ,MAAMmC,MAAM;QAEpE,IAAIE;QAEJ,MAAMC,WAAWC,IAAAA,oCAAiB,EAACrC;QACnC,MAAMsC,UAAU,CAAC,EAAEF,SAAS,IAAI,CAAC;QACjC,IAAIG,gBAAgBD;QAEpB,IAAIE,cAAcd,MAAMe,aAAa,IAAIzC;QACzC,OAAO0B,MAAMe,aAAa;QAE1B,IAAIC,SAAShB,MAAMiB,YAAY,IAAI7C,MAAM8C,UAAU,CAACF,MAAM;QAC1D,OAAOhB,MAAMiB,YAAY;QAEzB,IAAI7C,MAAM8C,UAAU,CAACF,MAAM,EAAE;YAC3B,MAAMG,mBAAmBC,IAAAA,wCAAmB,EAC1C9C,MACAF,MAAM8C,UAAU,CAACG,OAAO;YAG1B,IAAIF,iBAAiBG,cAAc,EAAE;gBACnCR,cAAcK,iBAAiBhB,QAAQ;gBACvCa,SAASG,iBAAiBG,cAAc;gBAExC,IAAIN,WAAW5C,MAAM8C,UAAU,CAACK,aAAa,EAAE;oBAC7CV,gBAAgB,CAAC,EAAEF,IAAAA,oCAAiB,EAACG,aAAa,IAAI,CAAC;gBACzD;YACF;QACF;QAEA,gEAAgE;QAChE,0DAA0D;QAC1D,MAAMU,qBAAqBC,OAAOC,IAAI,CAACzB,eAAe0B,MAAM,GAAG;QAE/D,iDAAiD;QACjD,MAAM,EAAExB,UAAUyB,gBAAgB,EAAE,GAAGR,IAAAA,wCAAmB,EACxD9C,MACAF,MAAM8C,UAAU,CAACG,OAAO;QAG1B,IAAIhB,aAAaZ,SAASmC,kBAAkB;YAC1C,MAAMC,iBAAiBlC,WAAWS,IAAAA,0BAAgB,EAACX,QAAQA;YAE3DgB,SAASqB,IAAAA,oBAAS,EAACD,gBAAgBf;YACnC,IAAIL,QAAQ;gBACVT,QAAQ;oBACN,GAAGA,KAAK;oBACR,GAAGS,MAAM;gBACX;YACF;QACF;QAEA,MAAM,EAAEP,GAAG,EAAE6B,GAAG,EAAE,GAAGC,IAAAA,uCAA0B,EAAC;YAAEC,KAAKnB;QAAY;QAEnE,6DAA6D;QAC7D,KAAK,MAAMoB,cAAc;YAAC;YAAK;SAAI,CAAE;YACnC,IACE;gBACE,CAAC,CAAC,EAAEA,WAAW,CAAC;gBAChB,CAAC,CAAC,EAAEA,WAAW,KAAK,CAAC;gBACrB,CAAC,CAAC,EAAEA,WAAW,WAAW,CAAC;aAC5B,CAACC,IAAI,CAAC,CAACC,IAAMA,MAAMtB,eAAe,CAAC,CAAC,EAAEE,OAAO,EAAEoB,EAAE,CAAC,KAAKtB,cACxD;gBACAiB,IAAIG,UAAU,GAAGA;YACnB;QACF;QAEA,+DAA+D;QAC/D,IAAI3C,iBAAiB,GAACW,WAAAA,IAAI+B,GAAG,qBAAP/B,SAASmC,QAAQ,CAAC,OAAM;YAC5CnC,IAAI+B,GAAG,IAAI;QACb;QAEA,IACEjB,UACAtC,eACAN,MAAM8C,UAAU,CAACoB,aAAa,IAC9BlE,MAAM8C,UAAU,CAACoB,aAAa,CAACH,IAAI,CACjC,CAACI;gBACgCA;mBAA/BA,GAAGhB,aAAa,KAAKP,YAAUuB,cAAAA,GAAGlB,OAAO,qBAAVkB,YAAYC,QAAQ,CAACxB,UAAU;YAElE;YACAyB,IAAAA,2BAAc,EAACvC,KAAK,kBAAkB;QACxC;QAEApC,UAAU4E,SAAS,CAAC;YAClB/D;YACAgE,qBAAqBvE,MAAM8C,UAAU,CAAC0B,aAAa;QACrD;QAEA,MAAMC,kBAAkB,CAACT,IACvBxD,aAAa,CAAC,EAAEwD,EAAE,EAAEU,SAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAEV,EAAE,KAAK,CAAC;QAEnD,IAAIW,eAAeF,gBAAgBnC;QAEnC,gFAAgF;QAChF,wBAAwB;QACxB,MAAMsC,UAAU3C,aAAaV,WAAW,KAAKsD,IAAAA,aAAO,EAACxD;QACrD,MAAMyD,UAAU7C,aAAaV,WAAW,KAAKsD,IAAAA,aAAO,EAAC3E;QAErD,6CAA6C;QAC7C,IAAIA,SAAS,aAAa;YACxByE,eAAezE;QACjB,OAEK,IAAI0E,YAAYE,WAAWA,YAAY,IAAI;YAC9C,MAAMC,iBAAiB;gBAAC;gBAAQ;aAAO,CAAChB,IAAI,CAC1C,CAACC,IAAMA,MAAM9D,QAAQ8D,MAAM9D,OAAO;YAEpC,mFAAmF;YACnF,8CAA8C;YAC9C,MAAM8E,gBAAgB,CAACD,kBAAkB7E,KAAK+D,QAAQ,CAAC;YACvDU,eAAeK,gBAAgBP,gBAAgBvE,QAAQA;QACzD,OAAO,IAAIA,SAAS,KAAK;YACvB,+CAA+C;YAC/CyE,eAAe;QACjB;QAEA,MAAMM,UAAU7C,IAAAA,UAAI,EAACD,QAAQ+C,IAAAA,aAAO,EAACP;QACrC,IAAIQ,eAAe/C,IAAAA,UAAI,EAACD,QAAQwC;QAEhC,MAAMS,iBAAE,CAACC,KAAK,CAACJ,SAAS;YAAEK,WAAW;QAAK;QAE1C,mEAAmE;QACnE,gCAAgC;QAChC,MAAMC,mBACJhE,YAAYT,aACR0E,IAAAA,8CAAsB,EACpBxE,6BACAH,oBACAE,qBACAX,WAEFqF;QAEN,qBAAqB;QACrB,IAAIlE,YAAYmE,IAAAA,gCAAe,EAACrE,OAAO;YACrC,OAAO,MAAMsE,IAAAA,wBAAc,EACzB7D,KACA6B,KACAtB,QACAhB,MACAkE,kBACAnF,SACA+E,cACAlF;QAEJ;QAEA,MAAM2F,aAAa,MAAMC,IAAAA,8BAAc,EAAC;YACtCzF;YACAiB;YACAyE,WAAWvE;QACb;QAEA,MAAMuB,aAA+B;YACnC,GAAG8C,UAAU;YACb,GAAG5F,MAAM8C,UAAU;YACnBN,SAASC;YACTJ;YACA5B;YACAC;YACAC;YACAoF,cAActF,gBAAgBuF,IAAAA,4BAAmB,EAAC5F,WAAW;YAC7DwC;YACAqD,qBAAqB;YACrBC,kBAAkB7E;QACpB;QAEA,IAAI8E,sBAAc,EAAE;YAClBrD,WAAWsD,YAAY,GAAG;QAC5B;QAEA,mBAAmB;QACnB,IAAI7E,UAAU;YACZ,qEAAqE;YACrE,cAAc;YACduB,WAAWyC,gBAAgB,GAAGA;YAE9B,OAAO,MAAMc,IAAAA,sBAAa,EACxBvE,KACA6B,KACAtC,MACAnB,MACA6B,UACAH,OACAkB,YACAqC,cACAvE,aACAe,gBACAF,eACAxB;QAEJ;QAEA,OAAO,MAAMqG,IAAAA,kBAAW,EACtBxE,KACA6B,KACAzD,MACAmB,MACAO,OACAuD,cACAR,cACAnC,SACAhC,YACA2B,QACAjB,kBACAb,cACAC,aACA2B,WACAmB,oBACAN,YACA8C,YACA3F;IAEJ,EAAE,OAAOsG,KAAK;QACZC,QAAQC,KAAK,CACX,CAAC,oCAAoC,EAAEvG,KAAK,gEAAgE,CAAC,GAC1GwG,CAAAA,IAAAA,gBAAO,EAACH,QAAQA,IAAII,KAAK,GAAGJ,IAAII,KAAK,GAAGJ,GAAE;QAG/C,OAAO;YAAEE,OAAO;QAAK;IACvB;AACF;AAEe,eAAenH,WAC5BU,KAAsB;IAEtB,4BAA4B;IAC5B4G,IAAAA,+CAA4B,EAAC;QAC3BC,kBAAkB7G,MAAM6G,gBAAgB;IAC1C;IAEA,MAAMC,QAA4B,EAAE;IACpC,MAAMC,iBAA6B,OACjCC,MACA9G,MACA+G,SACAC,kBAAkB,OAAO;QAEzB,MAAM9B,iBAAE,CAACC,KAAK,CAACH,IAAAA,aAAO,EAAChF,OAAO;YAAEoF,WAAW;QAAK;QAChD,MAAMF,iBAAE,CAAC+B,SAAS,CAACjH,MAAM+G,SAASC;QAClCJ,MAAMM,IAAI,CAAC;YAAEJ;YAAM9G;QAAK;IAC1B;IAEA,MAAMmH,iBAAiBC,IAAAA,YAAK,EAAC,sBAAsBtH,MAAMuH,YAAY;IAErE,MAAMC,QAAQC,KAAKC,GAAG;IAEtB,mBAAmB;IACnB,MAAMC,SAAS,MAAMN,eAAeO,YAAY,CAAC;QAC/C,OAAO,MAAM7H,eAAeC,OAAO+G;IACrC;IAEA,kDAAkD;IAClD,IAAI,CAACY,QAAQ;IAEb,iDAAiD;IACjD,IAAI,WAAWA,QAAQ;QACrB,OAAO;YAAElB,OAAOkB,OAAOlB,KAAK;YAAEoB,UAAUJ,KAAKC,GAAG,KAAKF;YAAOV,OAAO,EAAE;QAAC;IACxE;IAEA,sCAAsC;IACtC,OAAO;QACLe,UAAUJ,KAAKC,GAAG,KAAKF;QACvBV;QACAgB,gBAAgBH,OAAOG,cAAc;QACrCC,YAAYJ,OAAOI,UAAU;QAC7BC,UAAUL,OAAOK,QAAQ;QACzBC,aAAaN,OAAOM,WAAW;QAC/BC,iBAAiBP,OAAOO,eAAe;QACvCC,cAAcR,OAAOQ,YAAY;IACnC;AACF;AAEA5I,QAAQ6I,EAAE,CAAC,sBAAsB,CAAC7B;IAChC,mDAAmD;IACnD,kDAAkD;IAClD,IAAI8B,IAAAA,sBAAU,EAAC9B,MAAM;QACnB;IACF;IACAC,QAAQC,KAAK,CAACF;AAChB;AAEAhH,QAAQ6I,EAAE,CAAC,oBAAoB;AAC7B,sEAAsE;AACtE,uEAAuE;AACvE,6DAA6D;AAC/D"}