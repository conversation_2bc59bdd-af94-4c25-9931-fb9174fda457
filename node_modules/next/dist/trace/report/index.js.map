{"version": 3, "sources": ["../../../src/trace/report/index.ts"], "names": ["reporter", "MultiReporter", "constructor", "reporters", "flushAll", "Promise", "all", "map", "report", "spanName", "duration", "timestamp", "id", "parentId", "attrs", "startTime", "for<PERSON>ach", "reportToJson", "reportToTelemetry"], "mappings": ";;;;+BAoDaA;;;eAAAA;;;oEAnDiB;+DACL;;;;;;AAezB,MAAMC;IAGJC,YAAYC,SAAqB,CAAE;aAF3BA,YAAwB,EAAE;QAGhC,IAAI,CAACA,SAAS,GAAGA;IACnB;IAEA,MAAMC,WAAW;QACf,MAAMC,QAAQC,GAAG,CAAC,IAAI,CAACH,SAAS,CAACI,GAAG,CAAC,CAACP,WAAaA,SAASI,QAAQ;IACtE;IAEAI,OACEC,QAAgB,EAChBC,QAAgB,EAChBC,SAAiB,EACjBC,EAAU,EACVC,QAAiB,EACjBC,KAAc,EACdC,SAAkB,EAClB;QACA,IAAI,CAACZ,SAAS,CAACa,OAAO,CAAC,CAAChB,WACtBA,SAASQ,MAAM,CACbC,UACAC,UACAC,WACAC,IACAC,UACAC,OACAC;IAGN;AACF;AAGO,MAAMf,WAAW,IAAIC,cAAc;IAACgB,eAAY;IAAEC,oBAAiB;CAAC"}