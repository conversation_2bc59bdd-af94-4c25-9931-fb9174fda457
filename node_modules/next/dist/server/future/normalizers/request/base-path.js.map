{"version": 3, "sources": ["../../../../../src/server/future/normalizers/request/base-path.ts"], "names": ["BasePathPathnameNormalizer", "constructor", "basePath", "match", "pathname", "startsWith", "normalize", "matched", "substring", "length"], "mappings": ";;;;+BAEaA;;;eAAAA;;;AAAN,MAAMA;IAEXC,YAAYC,QAAgB,CAAE;QAC5B,uCAAuC;QACvC,IAAI,CAACA,YAAYA,aAAa,KAAK;QAEnC,IAAI,CAACA,QAAQ,GAAGA;IAClB;IAEOC,MAAMC,QAAgB,EAAE;QAC7B,0CAA0C;QAC1C,IAAI,CAAC,IAAI,CAACF,QAAQ,EAAE,OAAO;QAE3B,mEAAmE;QACnE,IAAIE,aAAa,IAAI,CAACF,QAAQ,IAAI,CAACE,SAASC,UAAU,CAAC,IAAI,CAACH,QAAQ,GAAG,MACrE,OAAO;QAET,OAAO;IACT;IAEOI,UAAUF,QAAgB,EAAEG,OAAiB,EAAU;QAC5D,sDAAsD;QACtD,IAAI,CAAC,IAAI,CAACL,QAAQ,EAAE,OAAOE;QAE3B,uEAAuE;QACvE,IAAI,CAACG,WAAW,CAAC,IAAI,CAACJ,KAAK,CAACC,WAAW,OAAOA;QAE9C,OAAOA,SAASI,SAAS,CAAC,IAAI,CAACN,QAAQ,CAACO,MAAM;IAChD;AACF"}