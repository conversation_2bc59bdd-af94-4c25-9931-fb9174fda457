{"version": 3, "sources": ["../../../../../src/server/future/normalizers/request/next-data.ts"], "names": ["NextDataPathnameNormalizer", "constructor", "buildID", "Error", "prefix", "match", "pathname", "startsWith", "endsWith", "normalize", "matched", "substring", "length"], "mappings": ";;;;+BAEaA;;;eAAAA;;;AAAN,MAAMA;IAEXC,YAAYC,OAAe,CAAE;QAC3B,IAAI,CAACA,SAAS;YACZ,MAAM,IAAIC,MAAM;QAClB;QAEA,IAAI,CAACC,MAAM,GAAG,CAAC,YAAY,EAAEF,QAAQ,CAAC;IACxC;IAEOG,MAAMC,QAAgB,EAAE;QAC7B,iEAAiE;QACjE,IAAI,CAACA,SAASC,UAAU,CAAC,CAAC,EAAE,IAAI,CAACH,MAAM,CAAC,CAAC,CAAC,GAAG,OAAO;QAEpD,qDAAqD;QACrD,IAAI,CAACE,SAASE,QAAQ,CAAC,UAAU,OAAO;QAExC,OAAO;IACT;IAEOC,UAAUH,QAAgB,EAAEI,OAAiB,EAAU;QAC5D,uEAAuE;QACvE,IAAI,CAACA,WAAW,CAAC,IAAI,CAACL,KAAK,CAACC,WAAW,OAAOA;QAE9C,+CAA+C;QAC/CA,WAAWA,SAASK,SAAS,CAC3B,IAAI,CAACP,MAAM,CAACQ,MAAM,EAClBN,SAASM,MAAM,GAAG,QAAQA,MAAM;QAGlC,uDAAuD;QACvD,IAAIN,aAAa,UAAU;YACzB,OAAO;QACT;QAEA,OAAOA;IACT;AACF"}