{"version": 3, "sources": ["../../../../../src/server/future/route-matcher-providers/dev/dev-app-page-route-matcher-provider.ts"], "names": ["DevAppPageRouteMatcherProvider", "FileCacheRouteMatcherProvider", "constructor", "appDir", "extensions", "reader", "normalizers", "DevAppNormalizers", "expression", "RegExp", "join", "transform", "files", "cache", "Map", "routeFilenames", "Array", "appPaths", "filename", "test", "page", "normalize", "includes", "push", "pathname", "bundlePath", "set", "matchers", "cached", "get", "Error", "AppPageRouteMatcher", "kind", "RouteKind", "APP_PAGE"], "mappings": ";;;;+BAOaA;;;eAAAA;;;qCANuB;2BACV;+CACoB;qBAEZ;AAE3B,MAAMA,uCAAuCC,4DAA6B;IAI/EC,YACEC,MAAc,EACdC,UAAiC,EACjCC,MAAkB,CAClB;QACA,KAAK,CAACF,QAAQE;QAEd,IAAI,CAACC,WAAW,GAAG,IAAIC,sBAAiB,CAACJ,QAAQC;QAEjD,wEAAwE;QACxE,aAAa;QACb,IAAI,CAACI,UAAU,GAAG,IAAIC,OAAO,CAAC,iBAAiB,EAAEL,WAAWM,IAAI,CAAC,KAAK,EAAE,CAAC;IAC3E;IAEA,MAAgBC,UACdC,KAA4B,EACiB;QAC7C,2EAA2E;QAC3E,UAAU;QACV,MAAMC,QAAQ,IAAIC;QAIlB,MAAMC,iBAAiB,IAAIC;QAC3B,MAAMC,WAAqC,CAAC;QAC5C,KAAK,MAAMC,YAAYN,MAAO;YAC5B,4DAA4D;YAC5D,IAAI,CAAC,IAAI,CAACJ,UAAU,CAACW,IAAI,CAACD,WAAW;YAErC,MAAME,OAAO,IAAI,CAACd,WAAW,CAACc,IAAI,CAACC,SAAS,CAACH;YAE7C,6CAA6C;YAC7C,IAAIE,KAAKE,QAAQ,CAAC,OAAO;YAEzB,6DAA6D;YAC7DP,eAAeQ,IAAI,CAACL;YAEpB,MAAMM,WAAW,IAAI,CAAClB,WAAW,CAACkB,QAAQ,CAACH,SAAS,CAACH;YACrD,MAAMO,aAAa,IAAI,CAACnB,WAAW,CAACmB,UAAU,CAACJ,SAAS,CAACH;YAEzD,kCAAkC;YAClCL,MAAMa,GAAG,CAACR,UAAU;gBAAEE;gBAAMI;gBAAUC;YAAW;YAEjD,IAAID,YAAYP,UAAUA,QAAQ,CAACO,SAAS,CAACD,IAAI,CAACH;iBAC7CH,QAAQ,CAACO,SAAS,GAAG;gBAACJ;aAAK;QAClC;QAEA,MAAMO,WAAuC,EAAE;QAC/C,KAAK,MAAMT,YAAYH,eAAgB;YACrC,6CAA6C;YAC7C,MAAMa,SAASf,MAAMgB,GAAG,CAACX;YACzB,IAAI,CAACU,QAAQ;gBACX,MAAM,IAAIE,MAAM;YAClB;YACA,MAAM,EAAEN,QAAQ,EAAEJ,IAAI,EAAEK,UAAU,EAAE,GAAGG;YAEvCD,SAASJ,IAAI,CACX,IAAIQ,wCAAmB,CAAC;gBACtBC,MAAMC,oBAAS,CAACC,QAAQ;gBACxBV;gBACAJ;gBACAK;gBACAP;gBACAD,UAAUA,QAAQ,CAACO,SAAS;YAC9B;QAEJ;QACA,OAAOG;IACT;AACF"}