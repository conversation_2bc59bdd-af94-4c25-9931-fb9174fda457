{"version": 3, "sources": ["../../../src/server/async-storage/static-generation-async-storage-wrapper.ts"], "names": ["StaticGenerationAsyncStorageWrapper", "wrap", "storage", "urlPathname", "renderOpts", "callback", "isStaticGeneration", "supportsDynamicHTML", "isDraftMode", "isServerAction", "store", "pagePath", "originalPathname", "incrementalCache", "globalThis", "__incrementalCache", "isRevalidate", "isPrerendering", "nextExport", "fetchCache", "isOnDemandRevalidate", "experimental", "ppr", "run"], "mappings": ";;;;+BAiCaA;;;eAAAA;;;AAAN,MAAMA,sCAGT;IACFC,MACEC,OAAiD,EACjD,EAAEC,WAAW,EAAEC,UAAU,EAA2B,EACpDC,QAAkD;QAElD;;;;;;;;;;;;;;;;KAgBC,GACD,MAAMC,qBACJ,CAACF,WAAWG,mBAAmB,IAC/B,CAACH,WAAWI,WAAW,IACvB,CAACJ,WAAWK,cAAc;QAE5B,MAAMC,QAA+B;YACnCJ;YACAH;YACAQ,UAAUP,WAAWQ,gBAAgB;YACrCC,kBACE,qEAAqE;YACrE,mDAAmD;YACnDT,WAAWS,gBAAgB,IAAI,AAACC,WAAmBC,kBAAkB;YACvEC,cAAcZ,WAAWY,YAAY;YACrCC,gBAAgBb,WAAWc,UAAU;YACrCC,YAAYf,WAAWe,UAAU;YACjCC,sBAAsBhB,WAAWgB,oBAAoB;YAErDZ,aAAaJ,WAAWI,WAAW;YACnCa,cAAc;gBACZC,KAAKlB,WAAWkB,GAAG;YACrB;QACF;QAEA,sFAAsF;QACtFlB,WAAWM,KAAK,GAAGA;QAEnB,OAAOR,QAAQqB,GAAG,CAACb,OAAOL,UAAUK;IACtC;AACF"}