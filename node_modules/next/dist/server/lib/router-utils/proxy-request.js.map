{"version": 3, "sources": ["../../../../src/server/lib/router-utils/proxy-request.ts"], "names": ["proxyRequest", "req", "res", "parsedUrl", "upgradeHead", "reqBody", "proxyTimeout", "query", "search", "stringifyQuery", "target", "url", "format", "HttpProxy", "require", "proxy", "<PERSON><PERSON><PERSON><PERSON>", "ignore<PERSON><PERSON>", "xfwd", "ws", "undefined", "Promise", "proxyResolve", "proxyReject", "finished", "on", "proxyReq", "destroy", "proxyRes", "destroyed", "innerReq", "innerRes", "cleanup", "err", "removeListener", "once", "console", "error", "statusCode", "end", "web", "buffer"], "mappings": ";;;;+BAMsBA;;;eAAAA;;;4DAHN;kCACe;;;;;;AAExB,eAAeA,aACpBC,GAAoB,EACpBC,GAAmB,EACnBC,SAAiC,EACjCC,WAAiB,EACjBC,OAAa,EACbC,YAA4B;IAE5B,MAAM,EAAEC,KAAK,EAAE,GAAGJ;IAClB,OAAO,AAACA,UAAkBI,KAAK;IAC/BJ,UAAUK,MAAM,GAAGC,IAAAA,gCAAc,EAACR,KAAYM;IAE9C,MAAMG,SAASC,YAAG,CAACC,MAAM,CAACT;IAC1B,MAAMU,YACJC,QAAQ;IAEV,MAAMC,QAAQ,IAAIF,UAAU;QAC1BH;QACAM,cAAc;QACdC,YAAY;QACZC,MAAM;QACNC,IAAI;QACJ,4DAA4D;QAC5D,yDAAyD;QACzDb,cAAcA,iBAAiB,OAAOc,YAAYd,gBAAgB;IACpE;IAEA,MAAM,IAAIe,QAAQ,CAACC,cAAcC;QAC/B,IAAIC,WAAW;QAEf,mEAAmE;QACnE,sEAAsE;QACtE,qEAAqE;QACrE,uEAAuE;QACvE,uEAAuE;QACvE,kEAAkE;QAClE,cAAc;QACdT,MAAMU,EAAE,CAAC,YAAY,CAACC;YACpBxB,IAAIuB,EAAE,CAAC,SAAS,IAAMC,SAASC,OAAO;QACxC;QACAZ,MAAMU,EAAE,CAAC,YAAY,CAACG;YACpB,IAAI1B,IAAI2B,SAAS,EAAE;gBACjBD,SAASD,OAAO;YAClB,OAAO;gBACLzB,IAAIuB,EAAE,CAAC,SAAS,IAAMG,SAASD,OAAO;YACxC;QACF;QAEAZ,MAAMU,EAAE,CAAC,YAAY,CAACG,UAAUE,UAAUC;YACxC,MAAMC,UAAU,CAACC;gBACf,4DAA4D;gBAC5DL,SAASM,cAAc,CAAC,SAASF;gBACjCJ,SAASM,cAAc,CAAC,SAASF;gBACjCD,SAASG,cAAc,CAAC,SAASF;gBACjCD,SAASG,cAAc,CAAC,SAASF;gBAEjC,oEAAoE;gBACpEF,SAASH,OAAO,CAACM;gBACjBL,SAASD,OAAO,CAACM;YACnB;YAEAL,SAASO,IAAI,CAAC,SAASH;YACvBJ,SAASO,IAAI,CAAC,SAASH;YACvBD,SAASI,IAAI,CAAC,SAASH;YACvBD,SAASI,IAAI,CAAC,SAASH;QACzB;QAEAjB,MAAMU,EAAE,CAAC,SAAS,CAACQ;YACjBG,QAAQC,KAAK,CAAC,CAAC,gBAAgB,EAAE3B,OAAO,CAAC,EAAEuB;YAC3C,IAAI,CAACT,UAAU;gBACbA,WAAW;gBACXD,YAAYU;gBAEZ,IAAI,CAAC/B,IAAI2B,SAAS,EAAE;oBAClB3B,IAAIoC,UAAU,GAAG;oBACjBpC,IAAIqC,GAAG,CAAC;gBACV;YACF;QACF;QAEA,wDAAwD;QACxD,IAAInC,aAAa;YACfW,MAAMU,EAAE,CAAC,cAAc,CAACC;gBACtBA,SAASD,EAAE,CAAC,SAAS;oBACnB,IAAI,CAACD,UAAU;wBACbA,WAAW;wBACXF,aAAa;oBACf;gBACF;YACF;YACAP,MAAMI,EAAE,CAAClB,KAA+BC,KAAKE;YAC7CkB,aAAa;QACf,OAAO;YACLP,MAAMU,EAAE,CAAC,YAAY,CAACC;gBACpBA,SAASD,EAAE,CAAC,SAAS;oBACnB,IAAI,CAACD,UAAU;wBACbA,WAAW;wBACXF,aAAa;oBACf;gBACF;YACF;YACAP,MAAMyB,GAAG,CAACvC,KAAKC,KAAK;gBAClBuC,QAAQpC;YACV;QACF;IACF;AACF"}