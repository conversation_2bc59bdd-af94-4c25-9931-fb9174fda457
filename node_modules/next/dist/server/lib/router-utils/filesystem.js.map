{"version": 3, "sources": ["../../../../src/server/lib/router-utils/filesystem.ts"], "names": ["buildCustomRoute", "setupFsCheck", "debug", "setupDebug", "type", "item", "basePath", "caseSensitive", "restrictedRedirectPaths", "map", "p", "match", "getPathMatch", "source", "strict", "removeUnnamedP<PERSON>ms", "regexModifier", "internal", "regex", "modifyRouteRegex", "undefined", "sensitive", "check", "opts", "getItemsLru", "dev", "L<PERSON><PERSON><PERSON>", "max", "length", "value", "key", "fsPath", "itemPath", "nextDataRoutes", "Set", "publicFolderItems", "nextStaticFolderItems", "legacyStaticFolderItems", "appFiles", "pageFiles", "dynamicRoutes", "middlewareMatcher", "distDir", "path", "join", "dir", "config", "publicFolderPath", "nextStaticFolderPath", "legacyStaticFolderPath", "customRoutes", "redirects", "rewrites", "beforeFiles", "afterFiles", "fallback", "headers", "buildId", "prerenderManifest", "middlewareManifest", "buildIdPath", "BUILD_ID_FILE", "fs", "readFile", "file", "recursiveReadDir", "add", "encodeURI", "normalizePathSep", "err", "code", "Log", "warn", "posix", "output", "routesManifestPath", "ROUTES_MANIFEST", "prerenderManifestPath", "PRERENDER_MANIFEST", "middlewareManifestPath", "MIDDLEWARE_MANIFEST", "pagesManifestPath", "PAGES_MANIFEST", "appRoutesManifestPath", "APP_PATH_ROUTES_MANIFEST", "routesManifest", "JSON", "parse", "catch", "pagesManifest", "appRoutesManifest", "Object", "keys", "i18n", "normalizeLocalePath", "locales", "pathname", "escapedBuildId", "escapeStringRegexp", "route", "dataRoutes", "isDynamicRoute", "page", "routeRegex", "getRouteRegex", "push", "re", "toString", "getRouteMatcher", "RegExp", "dataRouteRegex", "replace", "groups", "middleware", "matchers", "getMiddlewareRouteMatcher", "Array", "isArray", "loadCustomRoutes", "version", "routes", "notFoundRoutes", "preview", "previewModeId", "require", "randomBytes", "previewModeSigningKey", "previewModeEncryptionKey", "experimental", "caseSensitiveRoutes", "handleLocale", "locale", "i18nResult", "detectedLocale", "ensureFn", "normalizers", "rsc", "RSCPathnameNormalizer", "postponed", "PostponedPathnameNormalizer", "ppr", "interceptionRoutes", "devVirtualFsItems", "ensure<PERSON><PERSON>back", "fn", "getItem", "originalItemPath", "itemKey", "lruResult", "get", "pathHasPrefix", "removePathPrefix", "minimalMode", "normalize", "endsWith", "substring", "decodedItemPath", "decodeURIComponent", "itemsToCheck", "items", "curI<PERSON><PERSON><PERSON>", "curDecodedItemPath", "isDynamicOutput", "localeResult", "defaultLocale", "nextDataPrefix", "startsWith", "curLocaleResult", "matchedItem", "has", "encodedCurItemPath", "itemsRoot", "isStaticAsset", "includes", "found", "fileExists", "FileType", "File", "tempItemPath", "isAppFile", "normalizeMetadataRoute", "itemResult", "set", "getDynamicRoutes", "getMiddlewareMatchers"], "mappings": ";;;;;;;;;;;;;;;IAmEaA,gBAAgB;eAAhBA;;IA4BSC,YAAY;eAAZA;;;6DApFL;iEACF;6DACM;8DACE;iEACF;yEACQ;gCACI;4BACI;kCACJ;uBACF;8BACI;2BACN;4BACC;8BACE;+BACF;qCACM;kCACH;wCACS;2BAQnC;kCAC0B;kCACM;qBACD;2BACM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkB5C,MAAMC,QAAQC,IAAAA,cAAU,EAAC;AASlB,MAAMH,mBAAmB,CAC9BI,MACAC,MACAC,UACAC;IAEA,MAAMC,0BAA0B;QAAC;KAAS,CAACC,GAAG,CAAC,CAACC,IAC9CJ,WAAW,CAAC,EAAEA,SAAS,EAAEI,EAAE,CAAC,GAAGA;IAEjC,MAAMC,QAAQC,IAAAA,uBAAY,EAACP,KAAKQ,MAAM,EAAE;QACtCC,QAAQ;QACRC,qBAAqB;QACrBC,eAAe,CAAC,AAACX,KAAaY,QAAQ,GAClC,CAACC,QACCC,IAAAA,gCAAgB,EACdD,OACAd,SAAS,aAAaI,0BAA0BY,aAEpDA;QACJC,WAAWd;IACb;IACA,OAAO;QACL,GAAGF,IAAI;QACP,GAAID,SAAS,YAAY;YAAEkB,OAAO;QAAK,IAAI,CAAC,CAAC;QAC7CX;IACF;AACF;AAEO,eAAeV,aAAasB,IAQlC;IACC,MAAMC,cAAc,CAACD,KAAKE,GAAG,GACzB,IAAIC,iBAAQ,CAA0B;QACpCC,KAAK,OAAO;QACZC,QAAOC,KAAK,EAAEC,GAAG;YACf,IAAI,CAACD,OAAO,OAAOC,CAAAA,uBAAAA,IAAKF,MAAM,KAAI;YAClC,OACE,AAACE,CAAAA,OAAO,EAAC,EAAGF,MAAM,GAClB,AAACC,CAAAA,MAAME,MAAM,IAAI,EAAC,EAAGH,MAAM,GAC3BC,MAAMG,QAAQ,CAACJ,MAAM,GACrBC,MAAMzB,IAAI,CAACwB,MAAM;QAErB;IACF,KACAR;IAEJ,kDAAkD;IAClD,MAAMa,iBAAiB,IAAIC;IAC3B,MAAMC,oBAAoB,IAAID;IAC9B,MAAME,wBAAwB,IAAIF;IAClC,MAAMG,0BAA0B,IAAIH;IAEpC,MAAMI,WAAW,IAAIJ;IACrB,MAAMK,YAAY,IAAIL;IACtB,IAAIM,gBAA0C,EAAE;IAEhD,IAAIC,oBAEY,IAAM;IAEtB,MAAMC,UAAUC,aAAI,CAACC,IAAI,CAACrB,KAAKsB,GAAG,EAAEtB,KAAKuB,MAAM,CAACJ,OAAO;IACvD,MAAMK,mBAAmBJ,aAAI,CAACC,IAAI,CAACrB,KAAKsB,GAAG,EAAE;IAC7C,MAAMG,uBAAuBL,aAAI,CAACC,IAAI,CAACF,SAAS;IAChD,MAAMO,yBAAyBN,aAAI,CAACC,IAAI,CAACrB,KAAKsB,GAAG,EAAE;IACnD,IAAIK,eAAmE;QACrEC,WAAW,EAAE;QACbC,UAAU;YACRC,aAAa,EAAE;YACfC,YAAY,EAAE;YACdC,UAAU,EAAE;QACd;QACAC,SAAS,EAAE;IACb;IACA,IAAIC,UAAU;IACd,IAAIC;IAEJ,IAAI,CAACnC,KAAKE,GAAG,EAAE;YAoHTkC,iCAAAA;QAnHJ,MAAMC,cAAcjB,aAAI,CAACC,IAAI,CAACrB,KAAKsB,GAAG,EAAEtB,KAAKuB,MAAM,CAACJ,OAAO,EAAEmB,wBAAa;QAC1EJ,UAAU,MAAMK,iBAAE,CAACC,QAAQ,CAACH,aAAa;QAEzC,IAAI;YACF,KAAK,MAAMI,QAAQ,CAAA,MAAMC,IAAAA,kCAAgB,EAAClB,iBAAgB,EAAG;gBAC3D,6CAA6C;gBAC7CZ,kBAAkB+B,GAAG,CAACC,UAAUC,IAAAA,kCAAgB,EAACJ;YACnD;QACF,EAAE,OAAOK,KAAU;YACjB,IAAIA,IAAIC,IAAI,KAAK,UAAU;gBACzB,MAAMD;YACR;QACF;QAEA,IAAI;YACF,KAAK,MAAML,QAAQ,CAAA,MAAMC,IAAAA,kCAAgB,EAAChB,uBAAsB,EAAG;gBACjE,6CAA6C;gBAC7CZ,wBAAwB6B,GAAG,CAACC,UAAUC,IAAAA,kCAAgB,EAACJ;YACzD;YACAO,KAAIC,IAAI,CACN,CAAC,iIAAiI,CAAC;QAEvI,EAAE,OAAOH,KAAU;YACjB,IAAIA,IAAIC,IAAI,KAAK,UAAU;gBACzB,MAAMD;YACR;QACF;QAEA,IAAI;YACF,KAAK,MAAML,QAAQ,CAAA,MAAMC,IAAAA,kCAAgB,EAACjB,qBAAoB,EAAG;gBAC/D,6CAA6C;gBAC7CZ,sBAAsB8B,GAAG,CACvBvB,aAAI,CAAC8B,KAAK,CAAC7B,IAAI,CAAC,iBAAiBuB,UAAUC,IAAAA,kCAAgB,EAACJ;YAEhE;QACF,EAAE,OAAOK,KAAK;YACZ,IAAI9C,KAAKuB,MAAM,CAAC4B,MAAM,KAAK,cAAc,MAAML;QACjD;QAEA,MAAMM,qBAAqBhC,aAAI,CAACC,IAAI,CAACF,SAASkC,0BAAe;QAC7D,MAAMC,wBAAwBlC,aAAI,CAACC,IAAI,CAACF,SAASoC,6BAAkB;QACnE,MAAMC,yBAAyBpC,aAAI,CAACC,IAAI,CACtCF,SACA,UACAsC,8BAAmB;QAErB,MAAMC,oBAAoBtC,aAAI,CAACC,IAAI,CAACF,SAAS,UAAUwC,yBAAc;QACrE,MAAMC,wBAAwBxC,aAAI,CAACC,IAAI,CAACF,SAAS0C,mCAAwB;QAEzE,MAAMC,iBAAiBC,KAAKC,KAAK,CAC/B,MAAMzB,iBAAE,CAACC,QAAQ,CAACY,oBAAoB;QAGxCjB,oBAAoB4B,KAAKC,KAAK,CAC5B,MAAMzB,iBAAE,CAACC,QAAQ,CAACc,uBAAuB;QAG3C,MAAMlB,qBAAqB2B,KAAKC,KAAK,CACnC,MAAMzB,iBAAE,CAACC,QAAQ,CAACgB,wBAAwB,QAAQS,KAAK,CAAC,IAAM;QAGhE,MAAMC,gBAAgBH,KAAKC,KAAK,CAC9B,MAAMzB,iBAAE,CAACC,QAAQ,CAACkB,mBAAmB;QAEvC,MAAMS,oBAAoBJ,KAAKC,KAAK,CAClC,MAAMzB,iBAAE,CAACC,QAAQ,CAACoB,uBAAuB,QAAQK,KAAK,CAAC,IAAM;QAG/D,KAAK,MAAM1D,OAAO6D,OAAOC,IAAI,CAACH,eAAgB;YAC5C,8CAA8C;YAC9C,IAAIlE,KAAKuB,MAAM,CAAC+C,IAAI,EAAE;gBACpBtD,UAAU2B,GAAG,CACX4B,IAAAA,wCAAmB,EAAChE,KAAKP,KAAKuB,MAAM,CAAC+C,IAAI,CAACE,OAAO,EAAEC,QAAQ;YAE/D,OAAO;gBACLzD,UAAU2B,GAAG,CAACpC;YAChB;QACF;QACA,KAAK,MAAMA,OAAO6D,OAAOC,IAAI,CAACF,mBAAoB;YAChDpD,SAAS4B,GAAG,CAACwB,iBAAiB,CAAC5D,IAAI;QACrC;QAEA,MAAMmE,iBAAiBC,IAAAA,gCAAkB,EAACzC;QAE1C,KAAK,MAAM0C,SAASd,eAAee,UAAU,CAAE;YAC7C,IAAIC,IAAAA,qBAAc,EAACF,MAAMG,IAAI,GAAG;gBAC9B,MAAMC,aAAaC,IAAAA,yBAAa,EAACL,MAAMG,IAAI;gBAC3C9D,cAAciE,IAAI,CAAC;oBACjB,GAAGN,KAAK;oBACRjF,OAAOqF,WAAWG,EAAE,CAACC,QAAQ;oBAC7BhG,OAAOiG,IAAAA,6BAAe,EAAC;wBACrB,+DAA+D;wBAC/D,uCAAuC;wBACvCF,IAAInF,KAAKuB,MAAM,CAAC+C,IAAI,GAChB,IAAIgB,OACFV,MAAMW,cAAc,CAACC,OAAO,CAC1B,CAAC,CAAC,EAAEd,eAAe,CAAC,CAAC,EACrB,CAAC,CAAC,EAAEA,eAAe,uBAAuB,CAAC,KAG/C,IAAIY,OAAOV,MAAMW,cAAc;wBACnCE,QAAQT,WAAWS,MAAM;oBAC3B;gBACF;YACF;YACA/E,eAAeiC,GAAG,CAACiC,MAAMG,IAAI;QAC/B;QAEA,KAAK,MAAMH,SAASd,eAAe7C,aAAa,CAAE;YAChDA,cAAciE,IAAI,CAAC;gBACjB,GAAGN,KAAK;gBACRxF,OAAOiG,IAAAA,6BAAe,EAACJ,IAAAA,yBAAa,EAACL,MAAMG,IAAI;YACjD;QACF;QAEA,KAAI3C,iCAAAA,mBAAmBsD,UAAU,sBAA7BtD,kCAAAA,8BAA+B,CAAC,IAAI,qBAApCA,gCAAsCuD,QAAQ,EAAE;gBAEhDvD,kCAAAA;YADFlB,oBAAoB0E,IAAAA,iDAAyB,GAC3CxD,kCAAAA,mBAAmBsD,UAAU,sBAA7BtD,mCAAAA,+BAA+B,CAAC,IAAI,qBAApCA,iCAAsCuD,QAAQ;QAElD;QAEAhE,eAAe;YACbC,WAAWkC,eAAelC,SAAS;YACnCC,UAAUiC,eAAejC,QAAQ,GAC7BgE,MAAMC,OAAO,CAAChC,eAAejC,QAAQ,IACnC;gBACEC,aAAa,EAAE;gBACfC,YAAY+B,eAAejC,QAAQ;gBACnCG,UAAU,EAAE;YACd,IACA8B,eAAejC,QAAQ,GACzB;gBACEC,aAAa,EAAE;gBACfC,YAAY,EAAE;gBACdC,UAAU,EAAE;YACd;YACJC,SAAS6B,eAAe7B,OAAO;QACjC;IACF,OAAO;QACL,eAAe;QACfN,eAAe,MAAMoE,IAAAA,yBAAgB,EAAC/F,KAAKuB,MAAM;QAEjDY,oBAAoB;YAClB6D,SAAS;YACTC,QAAQ,CAAC;YACThF,eAAe,CAAC;YAChBiF,gBAAgB,EAAE;YAClBC,SAAS;gBACPC,eAAeC,QAAQ,UAAUC,WAAW,CAAC,IAAIlB,QAAQ,CAAC;gBAC1DmB,uBAAuBF,QAAQ,UAC5BC,WAAW,CAAC,IACZlB,QAAQ,CAAC;gBACZoB,0BAA0BH,QAAQ,UAC/BC,WAAW,CAAC,IACZlB,QAAQ,CAAC;YACd;QACF;IACF;IAEA,MAAMnD,UAAUN,aAAaM,OAAO,CAAC/C,GAAG,CAAC,CAACJ,OACxCL,iBACE,UACAK,MACAkB,KAAKuB,MAAM,CAACxC,QAAQ,EACpBiB,KAAKuB,MAAM,CAACkF,YAAY,CAACC,mBAAmB;IAGhD,MAAM9E,YAAYD,aAAaC,SAAS,CAAC1C,GAAG,CAAC,CAACJ,OAC5CL,iBACE,YACAK,MACAkB,KAAKuB,MAAM,CAACxC,QAAQ,EACpBiB,KAAKuB,MAAM,CAACkF,YAAY,CAACC,mBAAmB;IAGhD,MAAM7E,WAAW;QACf,qEAAqE;QACrEC,aAAaH,aAAaE,QAAQ,CAACC,WAAW,CAAC5C,GAAG,CAAC,CAACJ,OAClDL,iBAAiB,wBAAwBK;QAE3CiD,YAAYJ,aAAaE,QAAQ,CAACE,UAAU,CAAC7C,GAAG,CAAC,CAACJ,OAChDL,iBACE,WACAK,MACAkB,KAAKuB,MAAM,CAACxC,QAAQ,EACpBiB,KAAKuB,MAAM,CAACkF,YAAY,CAACC,mBAAmB;QAGhD1E,UAAUL,aAAaE,QAAQ,CAACG,QAAQ,CAAC9C,GAAG,CAAC,CAACJ,OAC5CL,iBACE,WACAK,MACAkB,KAAKuB,MAAM,CAACxC,QAAQ,EACpBiB,KAAKuB,MAAM,CAACkF,YAAY,CAACC,mBAAmB;IAGlD;IAEA,MAAM,EAAEpC,IAAI,EAAE,GAAGtE,KAAKuB,MAAM;IAE5B,MAAMoF,eAAe,CAAClC,UAAkBD;QACtC,IAAIoC;QAEJ,IAAItC,MAAM;YACR,MAAMuC,aAAatC,IAAAA,wCAAmB,EAACE,UAAUD,WAAWF,KAAKE,OAAO;YAExEC,WAAWoC,WAAWpC,QAAQ;YAC9BmC,SAASC,WAAWC,cAAc;QACpC;QACA,OAAO;YAAEF;YAAQnC;QAAS;IAC5B;IAEA9F,MAAM,kBAAkB+B;IACxB/B,MAAM,iBAAiBsC;IACvBtC,MAAM,aAAaqC;IACnBrC,MAAM,YAAYoC;IAElB,IAAIgG;IAEJ,MAAMC,cAAc;QAClB,uEAAuE;QACvE,+BAA+B;QAC/BC,KAAK,IAAIC,0BAAqB,CAAC;QAC/BC,WAAW,IAAIC,sCAA2B,CAACpH,KAAKuB,MAAM,CAACkF,YAAY,CAACY,GAAG;IACzE;IAEA,OAAO;QACLpF;QACAJ;QACAD;QAEAM;QACAyE;QAEA5F;QACAC;QACAC;QACAP;QAEA4G,oBAAoBzH;QAIpB0H,mBAAmB,IAAI5G;QAEvBwB;QACAjB,mBAAmBA;QAEnBsG,gBAAeC,EAAmB;YAChCV,WAAWU;QACb;QAEA,MAAMC,SAAQjH,QAAgB;YAC5B,MAAMkH,mBAAmBlH;YACzB,MAAMmH,UAAUD;YAChB,MAAME,YAAY5H,+BAAAA,YAAa6H,GAAG,CAACF;YAEnC,IAAIC,WAAW;gBACb,OAAOA;YACT;YAEA,MAAM,EAAE9I,QAAQ,EAAE,GAAGiB,KAAKuB,MAAM;YAEhC,IAAIxC,YAAY,CAACgJ,IAAAA,4BAAa,EAACtH,UAAU1B,WAAW;gBAClD,OAAO;YACT;YACA0B,WAAWuH,IAAAA,kCAAgB,EAACvH,UAAU1B,aAAa;YAEnD,kEAAkE;YAClE,YAAY;YACZ,IAAIiB,KAAKiI,WAAW,EAAE;gBACpB,IAAIjB,YAAYC,GAAG,CAAC7H,KAAK,CAACqB,WAAW;oBACnCA,WAAWuG,YAAYC,GAAG,CAACiB,SAAS,CAACzH,UAAU;gBACjD,OAAO,IAAIuG,YAAYG,SAAS,CAAC/H,KAAK,CAACqB,WAAW;oBAChDA,WAAWuG,YAAYG,SAAS,CAACe,SAAS,CAACzH,UAAU;gBACvD;YACF;YAEA,IAAIA,aAAa,OAAOA,SAAS0H,QAAQ,CAAC,MAAM;gBAC9C1H,WAAWA,SAAS2H,SAAS,CAAC,GAAG3H,SAASJ,MAAM,GAAG;YACrD;YAEA,IAAIgI,kBAAkB5H;YAEtB,IAAI;gBACF4H,kBAAkBC,mBAAmB7H;YACvC,EAAE,OAAM,CAAC;YAET,IAAIA,aAAa,gBAAgB;gBAC/B,OAAO;oBACLA;oBACA5B,MAAM;gBACR;YACF;YAEA,MAAM0J,eAAuD;gBAC3D;oBAAC,IAAI,CAAChB,iBAAiB;oBAAE;iBAAmB;gBAC5C;oBAAC1G;oBAAuB;iBAAmB;gBAC3C;oBAACC;oBAAyB;iBAAqB;gBAC/C;oBAACF;oBAAmB;iBAAe;gBACnC;oBAACG;oBAAU;iBAAU;gBACrB;oBAACC;oBAAW;iBAAW;aACxB;YAED,KAAK,IAAI,CAACwH,OAAO3J,KAAK,IAAI0J,aAAc;gBACtC,IAAI3B;gBACJ,IAAI6B,cAAchI;gBAClB,IAAIiI,qBAAqBL;gBAEzB,MAAMM,kBAAkB9J,SAAS,cAAcA,SAAS;gBAExD,IAAIyF,MAAM;oBACR,MAAMsE,eAAejC,aACnBlG,UACA,sDAAsD;oBACtD,qCAAqC;oBACrCkI,kBAAkB9I,YAAY;wBAACyE,wBAAAA,KAAMuE,aAAa;qBAAC;oBAGrD,IAAID,aAAanE,QAAQ,KAAKgE,aAAa;wBACzCA,cAAcG,aAAanE,QAAQ;wBACnCmC,SAASgC,aAAahC,MAAM;wBAE5B,IAAI;4BACF8B,qBAAqBJ,mBAAmBG;wBAC1C,EAAE,OAAM,CAAC;oBACX;gBACF;gBAEA,IAAI5J,SAAS,sBAAsB;oBACjC,IAAI,CAACkJ,IAAAA,4BAAa,EAACU,aAAa,YAAY;wBAC1C;oBACF;oBACAA,cAAcA,YAAYL,SAAS,CAAC,UAAU/H,MAAM;oBAEpD,IAAI;wBACFqI,qBAAqBJ,mBAAmBG;oBAC1C,EAAE,OAAM,CAAC;gBACX;gBAEA,IACE5J,SAAS,sBACT,CAACkJ,IAAAA,4BAAa,EAACU,aAAa,kBAC5B;oBACA;gBACF;gBAEA,MAAMK,iBAAiB,CAAC,YAAY,EAAE5G,QAAQ,CAAC,CAAC;gBAEhD,IACErD,SAAS,cACT4J,YAAYM,UAAU,CAACD,mBACvBL,YAAYN,QAAQ,CAAC,UACrB;oBACAK,QAAQ9H;oBACR,sCAAsC;oBACtC+H,cAAcA,YAAYL,SAAS,CAACU,eAAezI,MAAM,GAAG;oBAE5D,uBAAuB;oBACvBoI,cAAcA,YAAYL,SAAS,CACjC,GACAK,YAAYpI,MAAM,GAAG,QAAQA,MAAM;oBAErC,MAAM2I,kBAAkBrC,aAAa8B;oBACrCA,cACEO,gBAAgBvE,QAAQ,KAAK,WACzB,MACAuE,gBAAgBvE,QAAQ;oBAE9BmC,SAASoC,gBAAgBpC,MAAM;oBAE/B,IAAI;wBACF8B,qBAAqBJ,mBAAmBG;oBAC1C,EAAE,OAAM,CAAC;gBACX;gBAEA,IAAIQ,cAAcT,MAAMU,GAAG,CAACT;gBAE5B,gCAAgC;gBAChC,IAAI,CAACQ,eAAe,CAACjJ,KAAKE,GAAG,EAAE;oBAC7B+I,cAAcT,MAAMU,GAAG,CAACT;oBACxB,IAAIQ,aAAaR,cAAcC;yBAC1B;wBACH,wDAAwD;wBACxD,yGAAyG;wBACzG,wFAAwF;wBACxF,gFAAgF;wBAChF,oFAAoF;wBACpF,IAAI;4BACF,4FAA4F;4BAC5F,MAAMS,qBAAqBvG,UAAU6F;4BACrCQ,cAAcT,MAAMU,GAAG,CAACC;wBAC1B,EAAE,OAAM,CAAC;oBACX;gBACF;gBAEA,IAAIF,eAAejJ,KAAKE,GAAG,EAAE;oBAC3B,IAAIM;oBACJ,IAAI4I;oBAEJ,OAAQvK;wBACN,KAAK;4BAAoB;gCACvBuK,YAAY3H;gCACZgH,cAAcA,YAAYL,SAAS,CAAC,gBAAgB/H,MAAM;gCAC1D;4BACF;wBACA,KAAK;4BAAsB;gCACzB+I,YAAY1H;gCACZ;4BACF;wBACA,KAAK;4BAAgB;gCACnB0H,YAAY5H;gCACZ;4BACF;wBACA;4BAAS;gCACP;4BACF;oBACF;oBAEA,IAAI4H,aAAaX,aAAa;wBAC5BjI,SAASY,aAAI,CAAC8B,KAAK,CAAC7B,IAAI,CAAC+H,WAAWX;oBACtC;oBAEA,kDAAkD;oBAClD,8BAA8B;oBAC9B,IAAI,CAACQ,eAAejJ,KAAKE,GAAG,EAAE;wBAC5B,MAAMmJ,gBAAgB,AACpB;4BACE;4BACA;4BACA;yBACD,CACDC,QAAQ,CAACzK;wBAEX,IAAIwK,iBAAiBD,WAAW;4BAC9B,IAAIG,QAAQ/I,UAAW,MAAMgJ,IAAAA,sBAAU,EAAChJ,QAAQiJ,oBAAQ,CAACC,IAAI;4BAE7D,IAAI,CAACH,OAAO;gCACV,IAAI;oCACF,wCAAwC;oCACxC,2CAA2C;oCAC3C,yBAAyB;oCACzB,MAAMI,eAAerB,mBAAmBG;oCACxCjI,SAASY,aAAI,CAAC8B,KAAK,CAAC7B,IAAI,CAAC+H,WAAWO;oCACpCJ,QAAQ,MAAMC,IAAAA,sBAAU,EAAChJ,QAAQiJ,oBAAQ,CAACC,IAAI;gCAChD,EAAE,OAAM,CAAC;gCAET,IAAI,CAACH,OAAO;oCACV;gCACF;4BACF;wBACF,OAAO,IAAI1K,SAAS,cAAcA,SAAS,WAAW;gCAI3CkI;4BAHT,MAAM6C,YAAY/K,SAAS;4BAC3B,IACEkI,YACA,AAAC,QAAMA,YAAAA,SAAS;gCACdlI;gCACA4B,UAAUmJ,YACNC,IAAAA,wCAAsB,EAACpB,eACvBA;4BACN,uBALO1B,UAKH9C,KAAK,CAAC,IAAM,sBAAsB,iBACtC;gCACA;4BACF;wBACF,OAAO;4BACL;wBACF;oBACF;oBAEA,0CAA0C;oBAC1C,IAAIpF,SAAS,aAAa+H,UAAUA,YAAWtC,wBAAAA,KAAMuE,aAAa,GAAE;wBAClE;oBACF;oBAEA,MAAMiB,aAAa;wBACjBjL;wBACA2B;wBACAoG;wBACAwC;wBACA3I,UAAUgI;oBACZ;oBAEAxI,+BAAAA,YAAa8J,GAAG,CAACnC,SAASkC;oBAC1B,OAAOA;gBACT;YACF;YAEA7J,+BAAAA,YAAa8J,GAAG,CAACnC,SAAS;YAC1B,OAAO;QACT;QACAoC;YACE,kCAAkC;YAClC,OAAO,IAAI,CAAC/I,aAAa;QAC3B;QACAgJ;YACE,OAAO,IAAI,CAAC/I,iBAAiB;QAC/B;IACF;AACF"}