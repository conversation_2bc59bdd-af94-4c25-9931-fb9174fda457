{"version": 3, "sources": ["../../../src/server/app-render/action-handler.ts"], "names": ["handleAction", "formDataFromSearchQueryString", "query", "searchParams", "URLSearchParams", "formData", "FormData", "key", "value", "append", "nodeHeadersToRecord", "headers", "record", "Object", "entries", "undefined", "Array", "isArray", "join", "getForwardedHeaders", "req", "res", "requestHeaders", "requestCookies", "responseHeaders", "getHeaders", "rawSetCookies", "setCookies", "map", "<PERSON><PERSON><PERSON><PERSON>", "cookie", "split", "mergedHeaders", "filterReqHeaders", "actionsForbiddenHeaders", "mergedCookies", "concat", "Headers", "addRevalidationHeader", "staticGenerationStore", "requestStore", "Promise", "all", "pendingRevalidates", "isTagRevalidated", "revalidatedTags", "length", "isCookieRevalidated", "getModifiedCookieValues", "mutableCookies", "<PERSON><PERSON><PERSON><PERSON>", "JSON", "stringify", "createRedirectRenderResult", "redirectUrl", "startsWith", "forwardedHeaders", "set", "RSC", "host", "proto", "incrementalCache", "requestProtocol", "fetchUrl", "URL", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "prerenderManifest", "preview", "previewModeId", "delete", "headResponse", "fetch", "method", "next", "internal", "get", "RSC_CONTENT_TYPE_HEADER", "response", "includes", "FlightRenderResult", "body", "err", "console", "error", "RenderResult", "ComponentMod", "serverModuleMap", "generateFlight", "serverActionsBodySizeLimit", "ctx", "actionId", "ACTION", "toLowerCase", "contentType", "isURLEncodedAction", "isMultipartAction", "isFetchAction", "originHostname", "warn", "Error", "statusCode", "promise", "reject", "type", "result", "actionResult", "skipFlight", "pathWasRevalidated", "bound", "actionAsyncStorage", "formState", "run", "isAction", "process", "env", "NEXT_RUNTIME", "decodeReply", "decodeAction", "decodeFormState", "webRequest", "request", "action", "actionReturnedState", "actionData", "reader", "<PERSON><PERSON><PERSON><PERSON>", "done", "read", "TextDecoder", "decode", "decodeReplyFromBusboy", "require", "busboy", "bb", "pipe", "readableStream", "ReadableStream", "start", "controller", "on", "chunk", "enqueue", "Uint8Array", "close", "fakeRequest", "Request", "duplex", "chunks", "push", "<PERSON><PERSON><PERSON>", "from", "toString", "limit", "parse", "ApiError", "actionModId", "id", "actionHandler", "__next_app__", "returnVal", "apply", "resolve", "isRedirectError", "getURLFromRedirectError", "appendMutableCookies", "values", "isNotFoundError", "asNotFound"], "mappings": ";;;;+BAkNsBA;;;eAAAA;;;kCArMf;0BACyB;0BAIzB;qEACkB;oCAEU;uBAK5B;gCAIA;2BAMA;;;;;;AAGP,SAASC,8BAA8BC,KAAa;IAClD,MAAMC,eAAe,IAAIC,gBAAgBF;IACzC,MAAMG,WAAW,IAAIC;IACrB,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIL,aAAc;QACvCE,SAASI,MAAM,CAACF,KAAKC;IACvB;IACA,OAAOH;AACT;AAEA,SAASK,oBACPC,OAAkD;IAElD,MAAMC,SAAiC,CAAC;IACxC,KAAK,MAAM,CAACL,KAAKC,MAAM,IAAIK,OAAOC,OAAO,CAACH,SAAU;QAClD,IAAIH,UAAUO,WAAW;YACvBH,MAAM,CAACL,IAAI,GAAGS,MAAMC,OAAO,CAACT,SAASA,MAAMU,IAAI,CAAC,QAAQ,CAAC,EAAEV,MAAM,CAAC;QACpE;IACF;IACA,OAAOI;AACT;AAEA,SAASO,oBACPC,GAAoB,EACpBC,GAAmB;IAEnB,kCAAkC;IAClC,MAAMC,iBAAiBF,IAAIT,OAAO;IAClC,MAAMY,iBAAiBD,cAAc,CAAC,SAAS,IAAI;IAEnD,6CAA6C;IAC7C,MAAME,kBAAkBH,IAAII,UAAU;IACtC,MAAMC,gBAAgBF,eAAe,CAAC,aAAa;IACnD,MAAMG,aAAa,AACjBX,CAAAA,MAAMC,OAAO,CAACS,iBAAiBA,gBAAgB;QAACA;KAAc,AAAD,EAC7DE,GAAG,CAAC,CAACC;QACL,qDAAqD;QACrD,MAAM,CAACC,OAAO,GAAG,CAAC,EAAED,UAAU,CAAC,CAACE,KAAK,CAAC,KAAK;QAC3C,OAAOD;IACT;IAEA,qCAAqC;IACrC,MAAME,gBAAgBC,IAAAA,uBAAgB,EACpC;QACE,GAAGvB,oBAAoBY,eAAe;QACtC,GAAGZ,oBAAoBc,gBAAgB;IACzC,GACAU,8BAAuB;IAGzB,gBAAgB;IAChB,MAAMC,gBAAgBZ,eAAeQ,KAAK,CAAC,MAAMK,MAAM,CAACT,YAAYT,IAAI,CAAC;IAEzE,qDAAqD;IACrDc,aAAa,CAAC,SAAS,GAAGG;IAE1B,8CAA8C;IAC9C,OAAOH,aAAa,CAAC,oBAAoB;IAEzC,OAAO,IAAIK,QAAQL;AACrB;AAEA,eAAeM,sBACbjB,GAAmB,EACnB,EACEkB,qBAAqB,EACrBC,YAAY,EAIb;QAiBwBD;IAfzB,MAAME,QAAQC,GAAG,CAACH,sBAAsBI,kBAAkB,IAAI,EAAE;IAEhE,0EAA0E;IAC1E,+EAA+E;IAC/E,2DAA2D;IAE3D,mDAAmD;IACnD,8EAA8E;IAC9E,4BAA4B;IAE5B,2EAA2E;IAC3E,uEAAuE;IACvE,oFAAoF;IACpF,mBAAmB;IAEnB,MAAMC,mBAAmBL,EAAAA,yCAAAA,sBAAsBM,eAAe,qBAArCN,uCAAuCO,MAAM,IAAG,IAAI;IAC7E,MAAMC,sBAAsBC,IAAAA,uCAAuB,EACjDR,aAAaS,cAAc,EAC3BH,MAAM,GACJ,IACA;IAEJzB,IAAI6B,SAAS,CACX,wBACAC,KAAKC,SAAS,CAAC;QAAC,EAAE;QAAER;QAAkBG;KAAoB;AAE9D;AAEA,eAAeM,2BACbjC,GAAoB,EACpBC,GAAmB,EACnBiC,WAAmB,EACnBf,qBAA4C;IAE5ClB,IAAI6B,SAAS,CAAC,qBAAqBI;IACnC,4EAA4E;IAC5E,IAAIA,YAAYC,UAAU,CAAC,MAAM;YAM7BhB;QALF,MAAMiB,mBAAmBrC,oBAAoBC,KAAKC;QAClDmC,iBAAiBC,GAAG,CAACC,qBAAG,EAAE;QAE1B,MAAMC,OAAOvC,IAAIT,OAAO,CAAC,OAAO;QAChC,MAAMiD,QACJrB,EAAAA,0CAAAA,sBAAsBsB,gBAAgB,qBAAtCtB,wCAAwCuB,eAAe,KAAI;QAC7D,MAAMC,WAAW,IAAIC,IAAI,CAAC,EAAEJ,MAAM,GAAG,EAAED,KAAK,EAAEL,YAAY,CAAC;QAE3D,IAAIf,sBAAsBM,eAAe,EAAE;gBAOvCN,mEAAAA,2DAAAA;YANFiB,iBAAiBC,GAAG,CAClBQ,6CAAkC,EAClC1B,sBAAsBM,eAAe,CAAC3B,IAAI,CAAC;YAE7CsC,iBAAiBC,GAAG,CAClBS,iDAAsC,EACtC3B,EAAAA,2CAAAA,sBAAsBsB,gBAAgB,sBAAtCtB,4DAAAA,yCAAwC4B,iBAAiB,sBAAzD5B,oEAAAA,0DAA2D6B,OAAO,qBAAlE7B,kEACI8B,aAAa,KAAI;QAEzB;QAEA,6FAA6F;QAC7F,kDAAkD;QAClDb,iBAAiBc,MAAM,CAAC;QACxB,IAAI;QAEJ,IAAI;YACF,MAAMC,eAAe,MAAMC,MAAMT,UAAU;gBACzCU,QAAQ;gBACR9D,SAAS6C;gBACTkB,MAAM;oBACJ,aAAa;oBACbC,UAAU;gBACZ;YACF;YAEA,IACEJ,aAAa5D,OAAO,CAACiE,GAAG,CAAC,oBAAoBC,yCAAuB,EACpE;gBACA,MAAMC,WAAW,MAAMN,MAAMT,UAAU;oBACrCU,QAAQ;oBACR9D,SAAS6C;oBACTkB,MAAM;wBACJ,aAAa;wBACbC,UAAU;oBACZ;gBACF;gBACA,4EAA4E;gBAC5E,KAAK,MAAM,CAACpE,KAAKC,MAAM,IAAIsE,SAASnE,OAAO,CAAE;oBAC3C,IAAI,CAACuB,8BAAuB,CAAC6C,QAAQ,CAACxE,MAAM;wBAC1Cc,IAAI6B,SAAS,CAAC3C,KAAKC;oBACrB;gBACF;gBAEA,OAAO,IAAIwE,sCAAkB,CAACF,SAASG,IAAI;YAC7C;QACF,EAAE,OAAOC,KAAK;YACZ,+EAA+E;YAC/EC,QAAQC,KAAK,CAAC,CAAC,+BAA+B,CAAC,EAAEF;QACnD;IACF;IACA,OAAO,IAAIG,qBAAY,CAAClC,KAAKC,SAAS,CAAC,CAAC;AAC1C;AAEO,eAAepD,aAAa,EACjCoB,GAAG,EACHC,GAAG,EACHiE,YAAY,EACZC,eAAe,EACfC,cAAc,EACdjD,qBAAqB,EACrBC,YAAY,EACZiD,0BAA0B,EAC1BC,GAAG,EAiBJ;IAWC,IAAIC,WAAWvE,IAAIT,OAAO,CAACiF,wBAAM,CAACC,WAAW,GAAG;IAChD,MAAMC,cAAc1E,IAAIT,OAAO,CAAC,eAAe;IAC/C,MAAMoF,qBACJ3E,IAAIqD,MAAM,KAAK,UAAUqB,gBAAgB;IAC3C,MAAME,oBACJ5E,IAAIqD,MAAM,KAAK,WAAUqB,+BAAAA,YAAavC,UAAU,CAAC;IAEnD,MAAM0C,gBACJN,aAAa5E,aACb,OAAO4E,aAAa,YACpBvE,IAAIqD,MAAM,KAAK;IAEjB,8CAA8C;IAC9C,IAAI,CAAEwB,CAAAA,iBAAiBF,sBAAsBC,iBAAgB,GAAI;QAC/D;IACF;IAEA,MAAME,iBACJ,OAAO9E,IAAIT,OAAO,CAAC,SAAS,KAAK,WAC7B,IAAIqD,IAAI5C,IAAIT,OAAO,CAAC,SAAS,EAAEgD,IAAI,GACnC5C;IACN,MAAM4C,OAAOvC,IAAIT,OAAO,CAAC,mBAAmB,IAAIS,IAAIT,OAAO,CAAC,OAAO;IAEnE,4EAA4E;IAC5E,wDAAwD;IACxD,IAAI,CAACuF,gBAAgB;QACnB,0EAA0E;QAC1E,aAAa;QACbf,QAAQgB,IAAI,CACV;IAEJ,OAAO,IAAI,CAACxC,QAAQuC,mBAAmBvC,MAAM;QAC3C,uDAAuD;QACvDwB,QAAQC,KAAK,CACX;QAGF,MAAMA,QAAQ,IAAIgB,MAAM;QAExB,IAAIH,eAAe;YACjB5E,IAAIgF,UAAU,GAAG;YACjB,MAAM5D,QAAQC,GAAG,CAACH,sBAAsBI,kBAAkB,IAAI,EAAE;YAChE,MAAM2D,UAAU7D,QAAQ8D,MAAM,CAACnB;YAC/B,IAAI;gBACF,MAAMkB;YACR,EAAE,OAAM,CAAC;YAET,OAAO;gBACLE,MAAM;gBACNC,QAAQ,MAAMjB,eAAeE,KAAK;oBAChCgB,cAAcJ;oBACd,6EAA6E;oBAC7EK,YAAY,CAACpE,sBAAsBqE,kBAAkB;gBACvD;YACF;QACF;QAEA,MAAMxB;IACR;IAEA,sDAAsD;IACtD/D,IAAI6B,SAAS,CACX,iBACA;IAEF,IAAI2D,QAAQ,EAAE;IAEd,MAAM,EAAEC,kBAAkB,EAAE,GAAGxB;IAI/B,IAAIoB;IACJ,IAAIK;IAEJ,IAAI;QACF,MAAMD,mBAAmBE,GAAG,CAAC;YAAEC,UAAU;QAAK,GAAG;YAC/C,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;gBACvC,2CAA2C;gBAC3C,MAAM,EAAEC,WAAW,EAAEC,YAAY,EAAEC,eAAe,EAAE,GAAGjC;gBAEvD,MAAMkC,aAAapG;gBACnB,IAAI,CAACoG,WAAWvC,IAAI,EAAE;oBACpB,MAAM,IAAImB,MAAM;gBAClB;gBAEA,IAAIJ,mBAAmB;oBACrB,kCAAkC;oBAClC,MAAM3F,WAAW,MAAMmH,WAAWC,OAAO,CAACpH,QAAQ;oBAClD,IAAI4F,eAAe;wBACjBY,QAAQ,MAAMQ,YAAYhH,UAAUkF;oBACtC,OAAO;wBACL,MAAMmC,SAAS,MAAMJ,aAAajH,UAAUkF;wBAC5C,MAAMoC,sBAAsB,MAAMD;wBAClCX,YAAYQ,gBAAgBI,qBAAqBtH;wBAEjD,sBAAsB;wBACtB;oBACF;gBACF,OAAO;oBACL,IAAIuH,aAAa;oBAEjB,MAAMC,SAASL,WAAWvC,IAAI,CAAC6C,SAAS;oBACxC,MAAO,KAAM;wBACX,MAAM,EAAEC,IAAI,EAAEvH,KAAK,EAAE,GAAG,MAAMqH,OAAOG,IAAI;wBACzC,IAAID,MAAM;4BACR;wBACF;wBAEAH,cAAc,IAAIK,cAAcC,MAAM,CAAC1H;oBACzC;oBAEA,IAAIuF,oBAAoB;wBACtB,MAAM1F,WAAWJ,8BAA8B2H;wBAC/Cf,QAAQ,MAAMQ,YAAYhH,UAAUkF;oBACtC,OAAO;wBACLsB,QAAQ,MAAMQ,YAAYO,YAAYrC;oBACxC;gBACF;YACF,OAAO;gBACL,oEAAoE;gBACpE,MAAM,EACJ8B,WAAW,EACXc,qBAAqB,EACrBb,YAAY,EACZC,eAAe,EAChB,GAAGa,QAAQ,CAAC,mBAAmB,CAAC;gBAEjC,IAAIpC,mBAAmB;oBACrB,IAAIC,eAAe;wBACjB,MAAMoC,SAASD,QAAQ;wBACvB,MAAME,KAAKD,OAAO;4BAAE1H,SAASS,IAAIT,OAAO;wBAAC;wBACzCS,IAAImH,IAAI,CAACD;wBAETzB,QAAQ,MAAMsB,sBAAsBG,IAAI/C;oBAC1C,OAAO;wBACL,uDAAuD;wBACvD,MAAMiD,iBAAiB,IAAIC,eAAe;4BACxCC,OAAMC,UAAU;gCACdvH,IAAIwH,EAAE,CAAC,QAAQ,CAACC;oCACdF,WAAWG,OAAO,CAAC,IAAIC,WAAWF;gCACpC;gCACAzH,IAAIwH,EAAE,CAAC,OAAO;oCACZD,WAAWK,KAAK;gCAClB;gCACA5H,IAAIwH,EAAE,CAAC,SAAS,CAAC1D;oCACfyD,WAAWvD,KAAK,CAACF;gCACnB;4BACF;wBACF;wBAEA,6DAA6D;wBAC7D,0CAA0C;wBAC1C,MAAM+D,cAAc,IAAIC,QAAQ,oBAAoB;4BAClDzE,QAAQ;4BACR,mBAAmB;4BACnB9D,SAAS;gCAAE,gBAAgBmF;4BAAY;4BACvCb,MAAMuD;4BACNW,QAAQ;wBACV;wBACA,MAAM9I,WAAW,MAAM4I,YAAY5I,QAAQ;wBAC3C,MAAMqH,SAAS,MAAMJ,aAAajH,UAAUkF;wBAC5C,MAAMoC,sBAAsB,MAAMD;wBAClCX,YAAY,MAAMQ,gBAAgBI,qBAAqBtH;wBAEvD,sBAAsB;wBACtB;oBACF;gBACF,OAAO;oBACL,MAAM+I,SAAS,EAAE;oBAEjB,WAAW,MAAMP,SAASzH,IAAK;wBAC7BgI,OAAOC,IAAI,CAACC,OAAOC,IAAI,CAACV;oBAC1B;oBAEA,MAAMjB,aAAa0B,OAAOlH,MAAM,CAACgH,QAAQI,QAAQ,CAAC;oBAElD,MAAMC,QAAQrB,QAAQ,4BAA4BsB,KAAK,CACrDjE,8BAA8B;oBAGhC,IAAImC,WAAW9E,MAAM,GAAG2G,OAAO;wBAC7B,MAAM,EAAEE,QAAQ,EAAE,GAAGvB,QAAQ;wBAC7B,MAAM,IAAIuB,SACR,KACA,CAAC,cAAc,EAAElE,2BAA2B;kIACwE,CAAC;oBAEzH;oBAEA,IAAIM,oBAAoB;wBACtB,MAAM1F,WAAWJ,8BAA8B2H;wBAC/Cf,QAAQ,MAAMQ,YAAYhH,UAAUkF;oBACtC,OAAO;wBACLsB,QAAQ,MAAMQ,YAAYO,YAAYrC;oBACxC;gBACF;YACF;YAEA,aAAa;YACb,cAAc;YACd,mBAAmB;YACnB,iBAAiB;YAEjB,kBAAkB;YAClB,mBAAmB;YACnB,gBAAgB;YAEhB,wEAAwE;YACxE,8EAA8E;YAE9E,IAAIqE;YACJ,IAAI;gBACFA,cAAcrE,eAAe,CAACI,SAAS,CAACkE,EAAE;YAC5C,EAAE,OAAO3E,KAAK;gBACZ,yEAAyE;gBACzE,8EAA8E;gBAC9EC,QAAQC,KAAK,CACX,CAAC,8BAA8B,EAAEO,SAAS,2DAA2D,CAAC;gBAExG,OAAO;oBACLa,MAAM;gBACR;YACF;YAEA,MAAMsD,gBACJxE,aAAayE,YAAY,CAAC3B,OAAO,CAACwB,YAAY,CAACjE,SAAS;YAE1D,MAAMqE,YAAY,MAAMF,cAAcG,KAAK,CAAC,MAAMpD;YAElD,4DAA4D;YAC5D,IAAIZ,eAAe;gBACjB,MAAM3D,sBAAsBjB,KAAK;oBAC/BkB;oBACAC;gBACF;gBAEAkE,eAAe,MAAMlB,eAAeE,KAAK;oBACvCgB,cAAcjE,QAAQyH,OAAO,CAACF;oBAC9B,6EAA6E;oBAC7ErD,YAAY,CAACpE,sBAAsBqE,kBAAkB;gBACvD;YACF;QACF;QAEA,OAAO;YACLJ,MAAM;YACNC,QAAQC;YACRK;QACF;IACF,EAAE,OAAO7B,KAAK;QACZ,IAAIiF,IAAAA,yBAAe,EAACjF,MAAM;YACxB,MAAM5B,cAAc8G,IAAAA,iCAAuB,EAAClF;YAE5C,qEAAqE;YACrE,2CAA2C;YAC3C,MAAM5C,sBAAsBjB,KAAK;gBAC/BkB;gBACAC;YACF;YAEA,IAAIyD,eAAe;gBACjB,OAAO;oBACLO,MAAM;oBACNC,QAAQ,MAAMpD,2BACZjC,KACAC,KACAiC,aACAf;gBAEJ;YACF;YAEA,IAAI2C,IAAIjC,cAAc,EAAE;gBACtB,MAAMtC,UAAU,IAAI0B;gBAEpB,gEAAgE;gBAChE,YAAY;gBACZ,IAAIgI,IAAAA,oCAAoB,EAAC1J,SAASuE,IAAIjC,cAAc,GAAG;oBACrD5B,IAAI6B,SAAS,CAAC,cAAclC,MAAMuI,IAAI,CAAC5I,QAAQ2J,MAAM;gBACvD;YACF;YAEAjJ,IAAI6B,SAAS,CAAC,YAAYI;YAC1BjC,IAAIgF,UAAU,GAAG;YACjB,OAAO;gBACLG,MAAM;gBACNC,QAAQ,IAAIpB,qBAAY,CAAC;YAC3B;QACF,OAAO,IAAIkF,IAAAA,yBAAe,EAACrF,MAAM;YAC/B7D,IAAIgF,UAAU,GAAG;YAEjB,MAAM/D,sBAAsBjB,KAAK;gBAC/BkB;gBACAC;YACF;YAEA,IAAIyD,eAAe;gBACjB,MAAMK,UAAU7D,QAAQ8D,MAAM,CAACrB;gBAC/B,IAAI;oBACF,MAAMoB;gBACR,EAAE,OAAM,CAAC;gBACT,OAAO;oBACLE,MAAM;oBACNC,QAAQ,MAAMjB,eAAeE,KAAK;wBAChCiB,YAAY;wBACZD,cAAcJ;wBACdkE,YAAY;oBACd;gBACF;YACF;YACA,OAAO;gBACLhE,MAAM;YACR;QACF;QAEA,IAAIP,eAAe;YACjB5E,IAAIgF,UAAU,GAAG;YACjB,MAAM5D,QAAQC,GAAG,CAACH,sBAAsBI,kBAAkB,IAAI,EAAE;YAChE,MAAM2D,UAAU7D,QAAQ8D,MAAM,CAACrB;YAC/B,IAAI;gBACF,MAAMoB;YACR,EAAE,OAAM,CAAC;YAET,OAAO;gBACLE,MAAM;gBACNC,QAAQ,MAAMjB,eAAeE,KAAK;oBAChCgB,cAAcJ;oBACd,6EAA6E;oBAC7EK,YAAY,CAACpE,sBAAsBqE,kBAAkB;gBACvD;YACF;QACF;QAEA,MAAM1B;IACR;AACF"}