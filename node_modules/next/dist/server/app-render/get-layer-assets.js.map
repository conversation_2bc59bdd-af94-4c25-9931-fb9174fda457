{"version": 3, "sources": ["../../../src/server/app-render/get-layer-assets.tsx"], "names": ["getLayerAssets", "ctx", "layoutOrPagePath", "injectedCSS", "injectedCSSWithCurrentLayout", "injectedJS", "injectedJSWithCurrentLayout", "injectedFontPreloadTags", "injectedFontPreloadTagsWithCurrentLayout", "styles", "styleTags", "scripts", "scriptTags", "getLinkAndScriptTags", "clientReferenceManifest", "preloadedFontFiles", "getPreloadableFonts", "renderOpts", "nextFontManifest", "length", "i", "fontFilename", "ext", "exec", "type", "href", "assetPrefix", "componentMod", "preloadFont", "crossOrigin", "url", "URL", "preconnect", "origin", "error", "map", "index", "fullHref", "getAssetQueryString", "precedence", "process", "env", "NODE_ENV", "preloadStyle", "link", "rel", "key", "fullSrc", "script", "src", "async"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;8DANE;uCACmB;qCACD;qCAEA;;;;;;AAE7B,SAASA,eAAe,EAC7BC,GAAG,EACHC,gBAAgB,EAChBC,aAAaC,4BAA4B,EACzCC,YAAYC,2BAA2B,EACvCC,yBAAyBC,wCAAwC,EAOlE;IACC,MAAM,EAAEC,QAAQC,SAAS,EAAEC,SAASC,UAAU,EAAE,GAAGV,mBAC/CW,IAAAA,2CAAoB,EAClBZ,IAAIa,uBAAuB,EAC3BZ,kBACAE,8BACAE,6BACA,QAEF;QAAEG,QAAQ,EAAE;QAAEE,SAAS,EAAE;IAAC;IAE9B,MAAMI,qBAAqBb,mBACvBc,IAAAA,wCAAmB,EACjBf,IAAIgB,UAAU,CAACC,gBAAgB,EAC/BhB,kBACAM,4CAEF;IAEJ,IAAIO,oBAAoB;QACtB,IAAIA,mBAAmBI,MAAM,EAAE;YAC7B,IAAK,IAAIC,IAAI,GAAGA,IAAIL,mBAAmBI,MAAM,EAAEC,IAAK;gBAClD,MAAMC,eAAeN,kBAAkB,CAACK,EAAE;gBAC1C,MAAME,MAAM,8BAA8BC,IAAI,CAACF,aAAc,CAAC,EAAE;gBAChE,MAAMG,OAAO,CAAC,KAAK,EAAEF,IAAI,CAAC;gBAC1B,MAAMG,OAAO,CAAC,EAAExB,IAAIyB,WAAW,CAAC,OAAO,EAAEL,aAAa,CAAC;gBACvDpB,IAAI0B,YAAY,CAACC,WAAW,CAACH,MAAMD,MAAMvB,IAAIgB,UAAU,CAACY,WAAW;YACrE;QACF,OAAO;YACL,IAAI;gBACF,IAAIC,MAAM,IAAIC,IAAI9B,IAAIyB,WAAW;gBACjCzB,IAAI0B,YAAY,CAACK,UAAU,CAACF,IAAIG,MAAM,EAAE;YAC1C,EAAE,OAAOC,OAAO;gBACd,mEAAmE;gBACnE,8CAA8C;gBAC9CjC,IAAI0B,YAAY,CAACK,UAAU,CAAC,KAAK;YACnC;QACF;IACF;IAEA,MAAMvB,SAASC,YACXA,UAAUyB,GAAG,CAAC,CAACV,MAAMW;QACnB,iEAAiE;QACjE,kDAAkD;QAClD,mDAAmD;QACnD,mEAAmE;QACnE,mEAAmE;QACnE,cAAc;QACd,MAAMC,WAAW,CAAC,EAAEpC,IAAIyB,WAAW,CAAC,OAAO,EAAED,KAAK,EAAEa,IAAAA,wCAAmB,EACrErC,KACA,MACA,CAAC;QAEH,gEAAgE;QAChE,oEAAoE;QACpE,2DAA2D;QAC3D,iEAAiE;QACjE,0DAA0D;QAC1D,+CAA+C;QAC/C,MAAMsC,aACJC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,UAAUjB,OAAO;QAE5DxB,IAAI0B,YAAY,CAACgB,YAAY,CAACN,UAAUpC,IAAIgB,UAAU,CAACY,WAAW;QAElE,qBACE,6BAACe;YACCC,KAAI;YACJpB,MAAMY;YACN,aAAa;YACbE,YAAYA;YACZV,aAAa5B,IAAIgB,UAAU,CAACY,WAAW;YACvCiB,KAAKV;;IAGX,KACA,EAAE;IAEN,MAAMzB,UAAUC,aACZA,WAAWuB,GAAG,CAAC,CAACV,MAAMW;QACpB,MAAMW,UAAU,CAAC,EAAE9C,IAAIyB,WAAW,CAAC,OAAO,EAAED,KAAK,CAAC;QAElD,qBAAO,6BAACuB;YAAOC,KAAKF;YAASG,OAAO;YAAMJ,KAAKV;;IACjD,KACA,EAAE;IAEN,OAAO3B,OAAOU,MAAM,IAAIR,QAAQQ,MAAM,GAAG;WAAIV;WAAWE;KAAQ,GAAG;AACrE"}