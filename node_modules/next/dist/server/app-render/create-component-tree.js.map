{"version": 3, "sources": ["../../../src/server/app-render/create-component-tree.tsx"], "names": ["createComponentTree", "createSegmentPath", "loaderTree", "tree", "parentParams", "firstItem", "rootLayoutIncluded", "injectedCSS", "injectedJS", "injectedFontPreloadTags", "asNotFound", "metadataOutlet", "ctx", "renderOpts", "nextConfigOutput", "staticGenerationStore", "componentMod", "staticGenerationBailout", "NotFoundBoundary", "LayoutRouter", "RenderFromTemplateContext", "StaticGenerationSearchParamsBailoutProvider", "serverHooks", "DynamicServerError", "pagePath", "getDynamicParamFromSegment", "query", "isPrefetch", "searchParamsProps", "page", "layoutOrPagePath", "segment", "components", "parallelRoutes", "parseLoaderTree", "layout", "template", "error", "loading", "notFound", "injectedCSSWithCurrentLayout", "Set", "injectedJSWithCurrentLayout", "injectedFontPreloadTagsWithCurrentLayout", "layerAssets", "getLayerAssets", "Template", "templateStyles", "templateScripts", "createComponentStylesAndScripts", "filePath", "getComponent", "React", "Fragment", "ErrorComponent", "errorStyles", "errorScripts", "Loading", "loadingStyles", "loadingScripts", "isLayout", "isPage", "layoutOrPageMod", "getLayoutOrPageModule", "rootLayoutAtThisLevel", "rootLayoutIncludedAtThisLevelOrAbove", "NotFound", "notFoundStyles", "dynamic", "forceDynamic", "dynamicShouldError", "link", "forceStatic", "fetchCache", "revalidate", "defaultRevalidate", "isStaticGeneration", "dynamicUsageDescription", "dynamicUsageErr", "experimental", "ppr", "LayoutOrPage", "interopDefault", "undefined", "Component", "parallelKeys", "Object", "keys", "hasSlot<PERSON>ey", "length", "componentProps", "NotFoundComponent", "RootLayoutComponent", "process", "env", "NODE_ENV", "isValidElementType", "require", "Error", "segmentParam", "currentParams", "value", "param", "actualSegment", "treeSegment", "parallelRouteMap", "Promise", "all", "map", "parallelRouteKey", "isChildrenRoute<PERSON>ey", "currentSegmentPath", "parallelRoute", "childSegment", "childSegmentParam", "notFoundComponent", "getParallelRoutePair", "currentChildProp", "currentStyles", "parallel<PERSON><PERSON>er<PERSON>ey", "segmentPath", "hasLoading", "Boolean", "childProp", "styles", "childElement", "childPropSegment", "addSearchParamsIfPageSegment", "hasLoadingComponentInTree", "ChildComponent", "childComponentStyles", "child", "current", "parallelRouteComponents", "reduce", "list", "Comp", "children", "isClientComponent", "isClientReference", "meta", "name", "content", "props", "params", "resolve", "then", "preloadComponent", "propsForComponent"], "mappings": ";;;;+BAkBsBA;;;eAAAA;;;8DAjBJ;iCACgB;8BACI;gCAEP;kCACE;uDACY;iCACb;iDAEgB;gCACjB;2CACW;;;;;;AAMnC,eAAeA,oBAAoB,EACxCC,iBAAiB,EACjBC,YAAYC,IAAI,EAChBC,YAAY,EACZC,SAAS,EACTC,kBAAkB,EAClBC,WAAW,EACXC,UAAU,EACVC,uBAAuB,EACvBC,UAAU,EACVC,cAAc,EACdC,GAAG,EAaJ;IAIC,MAAM,EACJC,YAAY,EAAEC,gBAAgB,EAAE,EAChCC,qBAAqB,EACrBC,cAAc,EACZC,uBAAuB,EACvBC,gBAAgB,EAChBC,YAAY,EACZC,yBAAyB,EACzBC,2CAA2C,EAC3CC,aAAa,EAAEC,kBAAkB,EAAE,EACpC,EACDC,QAAQ,EACRC,0BAA0B,EAC1BC,KAAK,EACLC,UAAU,EACVC,iBAAiB,EAClB,GAAGhB;IAEJ,MAAM,EAAEiB,IAAI,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,UAAU,EAAEC,cAAc,EAAE,GACnEC,IAAAA,gCAAe,EAAC/B;IAElB,MAAM,EAAEgC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAE,aAAaC,QAAQ,EAAE,GAAGP;IAEpE,MAAMQ,+BAA+B,IAAIC,IAAIlC;IAC7C,MAAMmC,8BAA8B,IAAID,IAAIjC;IAC5C,MAAMmC,2CAA2C,IAAIF,IACnDhC;IAGF,MAAMmC,cAAcC,IAAAA,8BAAc,EAAC;QACjCjC;QACAkB;QACAvB,aAAaiC;QACbhC,YAAYkC;QACZjC,yBAAyBkC;IAC3B;IAEA,MAAM,CAACG,UAAUC,gBAAgBC,gBAAgB,GAAGZ,WAChD,MAAMa,IAAAA,gEAA+B,EAAC;QACpCrC;QACAsC,UAAUd,QAAQ,CAAC,EAAE;QACrBe,cAAcf,QAAQ,CAAC,EAAE;QACzB7B,aAAaiC;QACbhC,YAAYkC;IACd,KACA;QAACU,cAAK,CAACC,QAAQ;KAAC;IAEpB,MAAM,CAACC,gBAAgBC,aAAaC,aAAa,GAAGnB,QAChD,MAAMY,IAAAA,gEAA+B,EAAC;QACpCrC;QACAsC,UAAUb,KAAK,CAAC,EAAE;QAClBc,cAAcd,KAAK,CAAC,EAAE;QACtB9B,aAAaiC;QACbhC,YAAYkC;IACd,KACA,EAAE;IAEN,MAAM,CAACe,SAASC,eAAeC,eAAe,GAAGrB,UAC7C,MAAMW,IAAAA,gEAA+B,EAAC;QACpCrC;QACAsC,UAAUZ,OAAO,CAAC,EAAE;QACpBa,cAAcb,OAAO,CAAC,EAAE;QACxB/B,aAAaiC;QACbhC,YAAYkC;IACd,KACA,EAAE;IAEN,MAAMkB,WAAW,OAAOzB,WAAW;IACnC,MAAM0B,SAAS,OAAOhC,SAAS;IAC/B,MAAM,CAACiC,gBAAgB,GAAG,MAAMC,IAAAA,mCAAqB,EAAC5D;IAEtD;;GAEC,GACD,MAAM6D,wBAAwBJ,YAAY,CAACtD;IAC3C;;GAEC,GACD,MAAM2D,uCACJ3D,sBAAsB0D;IAExB,MAAM,CAACE,UAAUC,eAAe,GAAG5B,WAC/B,MAAMU,IAAAA,gEAA+B,EAAC;QACpCrC;QACAsC,UAAUX,QAAQ,CAAC,EAAE;QACrBY,cAAcZ,QAAQ,CAAC,EAAE;QACzBhC,aAAaiC;QACbhC,YAAYkC;IACd,KACA,EAAE;IAEN,IAAI0B,UAAUN,mCAAAA,gBAAiBM,OAAO;IAEtC,IAAItD,qBAAqB,UAAU;QACjC,IAAI,CAACsD,WAAWA,YAAY,QAAQ;YAClCA,UAAU;QACZ,OAAO,IAAIA,YAAY,iBAAiB;YACtCrD,sBAAsBsD,YAAY,GAAG;YACrCtD,sBAAsBuD,kBAAkB,GAAG;YAC3CrD,wBAAwB,CAAC,cAAc,CAAC,EAAE;gBACxCmD;gBACAG,MAAM;YACR;QACF;IACF;IAEA,IAAI,OAAOH,YAAY,UAAU;QAC/B,sDAAsD;QACtD,sDAAsD;QACtD,YAAY;QACZ,IAAIA,YAAY,SAAS;YACvBrD,sBAAsBuD,kBAAkB,GAAG;QAC7C,OAAO,IAAIF,YAAY,iBAAiB;YACtCrD,sBAAsBsD,YAAY,GAAG;YACrCpD,wBAAwB,CAAC,aAAa,CAAC,EAAE;gBAAEmD;YAAQ;QACrD,OAAO;YACLrD,sBAAsBuD,kBAAkB,GAAG;YAC3C,IAAIF,YAAY,gBAAgB;gBAC9BrD,sBAAsByD,WAAW,GAAG;YACtC,OAAO;gBACLzD,sBAAsByD,WAAW,GAAG;YACtC;QACF;IACF;IAEA,IAAI,QAAOV,mCAAAA,gBAAiBW,UAAU,MAAK,UAAU;QACnD1D,sBAAsB0D,UAAU,GAAGX,mCAAAA,gBAAiBW,UAAU;IAChE;IAEA,IAAI,QAAOX,mCAAAA,gBAAiBY,UAAU,MAAK,UAAU;QACnD9D,IAAI+D,iBAAiB,GAAGb,gBAAgBY,UAAU;QAElD,IACE,OAAO3D,sBAAsB2D,UAAU,KAAK,eAC3C,OAAO3D,sBAAsB2D,UAAU,KAAK,YAC3C3D,sBAAsB2D,UAAU,GAAG9D,IAAI+D,iBAAiB,EAC1D;YACA5D,sBAAsB2D,UAAU,GAAG9D,IAAI+D,iBAAiB;QAC1D;QAEA,IACE5D,sBAAsB6D,kBAAkB,IACxChE,IAAI+D,iBAAiB,KAAK,GAC1B;YACA,MAAME,0BAA0B,CAAC,yBAAyB,EAAE9C,QAAQ,CAAC;YACrEhB,sBAAsB8D,uBAAuB,GAAGA;YAEhD,MAAM,IAAItD,mBAAmBsD;QAC/B;IACF;IAEA,IACE9D,CAAAA,yCAAAA,sBAAuB+D,eAAe,KACtC,CAAC/D,sBAAsBgE,YAAY,CAACC,GAAG,EACvC;QACA,MAAMjE,sBAAsB+D,eAAe;IAC7C;IAEA,MAAMG,eAAenB,kBACjBoB,IAAAA,8BAAc,EAACpB,mBACfqB;IAEJ;;GAEC,GACD,IAAIC,YAAYH;IAChB,MAAMI,eAAeC,OAAOC,IAAI,CAACtD;IACjC,MAAMuD,aAAaH,aAAaI,MAAM,GAAG;IAEzC,IAAID,cAAcxB,uBAAuB;QACvCoB,YAAY,CAACM;YACX,MAAMC,oBAAoBzB;YAC1B,MAAM0B,sBAAsBX;YAC5B,qBACE,6BAAC/D;gBACCqB,wBACE,4DACGK,2BACD,6BAACgD,2BACEzB,8BACD,6BAACwB;6BAKP,6BAACC,qBAAwBF;QAG/B;IACF;IAEA,IAAIG,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,kBAAkB,EAAE,GAAGC,QAAQ;QACvC,IACE,AAACpC,CAAAA,UAAU,OAAOuB,cAAc,WAAU,KAC1C,CAACY,mBAAmBZ,YACpB;YACA,MAAM,IAAIc,MACR,CAAC,sDAAsD,EAAE1E,SAAS,CAAC,CAAC;QAExE;QAEA,IACE,OAAO8B,mBAAmB,eAC1B,CAAC0C,mBAAmB1C,iBACpB;YACA,MAAM,IAAI4C,MACR,CAAC,8DAA8D,EAAEnE,QAAQ,CAAC;QAE9E;QAEA,IAAI,OAAO0B,YAAY,eAAe,CAACuC,mBAAmBvC,UAAU;YAClE,MAAM,IAAIyC,MACR,CAAC,0DAA0D,EAAEnE,QAAQ,CAAC;QAE1E;QAEA,IAAI,OAAOmC,aAAa,eAAe,CAAC8B,mBAAmB9B,WAAW;YACpE,MAAM,IAAIgC,MACR,CAAC,2DAA2D,EAAEnE,QAAQ,CAAC;QAE3E;IACF;IAEA,iCAAiC;IACjC,MAAMoE,eAAe1E,2BAA2BM;IAChD;;GAEC,GACD,MAAMqE,gBACJ,mDAAmD;IACnDD,gBAAgBA,aAAaE,KAAK,KAAK,OACnC;QACE,GAAGjG,YAAY;QACf,CAAC+F,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;IAC1C,IAEAjG;IACN,4BAA4B;IAC5B,MAAMmG,gBAAgBJ,eAAeA,aAAaK,WAAW,GAAGzE;IAEhE,mHAAmH;IACnH,MAAM0E,mBAAmB,MAAMC,QAAQC,GAAG,CACxCrB,OAAOC,IAAI,CAACtD,gBAAgB2E,GAAG,CAC7B,OAAOC;QACL,MAAMC,qBAAqBD,qBAAqB;QAChD,MAAME,qBAAwC1G,YAC1C;YAACwG;SAAiB,GAClB;YAACN;YAAeM;SAAiB;QAErC,MAAMG,gBAAgB/E,cAAc,CAAC4E,iBAAiB;QAEtD,MAAMI,eAAeD,aAAa,CAAC,EAAE;QACrC,MAAME,oBAAoBzF,2BAA2BwF;QACrD,MAAME,oBACJjD,YAAY4C,mCAAqB,6BAAC5C,kBAAciB;QAElD,SAASiC,qBACPC,gBAA2B,EAC3BC,aAA8B;YAE9B,4CAA4C;YAC5C,OAAO;gBACLT;8BACA,6BAAC1F;oBACCoG,mBAAmBV;oBACnBW,aAAavH,kBAAkB8G;oBAC/BzE,SAASmB,wBAAU,6BAACA,iBAAa0B;oBACjCzB,eAAeA;oBACfC,gBAAgBA;oBAChB,sKAAsK;oBACtK8D,YAAYC,QAAQjE;oBACpBpB,OAAOiB;oBACPC,aAAaA;oBACbC,cAAcA;oBACdpB,wBACE,6BAACU,8BACC,6BAAC1B;oBAGL2B,gBAAgBA;oBAChBC,iBAAiBA;oBACjBT,UAAU4E;oBACVhD,gBAAgBA;oBAChBwD,WAAWN;oBACXO,QAAQN;;aAEX;QACH;QAEA,yEAAyE;QACzE,gDAAgD;QAChD,wEAAwE;QACxE,IAAIA,gBAAgBnC;QACpB,IAAI0C,eAAe;QACnB,MAAMC,mBAAmBC,IAAAA,mEAA4B,EACnDb,oBAAoBA,kBAAkBV,WAAW,GAAGS,cACpDvF;QAEF,IACE,CACEC,CAAAA,cACC8B,CAAAA,WAAW,CAACuE,IAAAA,oDAAyB,EAAChB,cAAa,CAAC,GAEvD;YACA,6BAA6B;YAC7B,MAAM,EAAE5B,WAAW6C,cAAc,EAAEL,QAAQM,oBAAoB,EAAE,GAC/D,MAAMlI,oBAAoB;gBACxBC,mBAAmB,CAACkI;oBAClB,OAAOlI,kBAAkB;2BAAI8G;2BAAuBoB;qBAAM;gBAC5D;gBACAjI,YAAY8G;gBACZ5G,cAAcgG;gBACd9F,oBAAoB2D;gBACpB1D,aAAaiC;gBACbhC,YAAYkC;gBACZjC,yBAAyBkC;gBACzBjC;gBACAC;gBACAC;YACF;YAEF0G,gBAAgBY;YAChBL,6BAAe,6BAACI;QAClB;QAEA,MAAMN,YAAuB;YAC3BS,SAASP;YACT9F,SAAS+F;QACX;QAEA,OAAOV,qBAAqBO,WAAWL;IACzC;IAIJ,uFAAuF;IACvF,MAAMe,0BAA0B5B,iBAAiB6B,MAAM,CACrD,CAACC,MAAM,CAAC1B,kBAAkB2B,KAAK;QAC7BD,IAAI,CAAC1B,iBAAiB,GAAG2B;QACzB,OAAOD;IACT,GACA,CAAC;IAGH,wIAAwI;IACxI,IAAI,CAACnD,WAAW;QACd,OAAO;YACLA,WAAW,kBAAM,4DAAGiD,wBAAwBI,QAAQ;YACpDb,QAAQhF;QACV;IACF;IAEA,MAAM8F,oBAAoBC,IAAAA,kCAAiB,EAAC7E;IAE5C,oEAAoE;IACpE,iEAAiE;IACjE,IAAIqD,oBAAoB,CAAC;IACzB,IACEjD,YACAxD,cACA,2GAA2G;IAC3G,6DAA6D;IAC7D,CAAC+F,iBAAiBhB,MAAM,EACxB;QACA0B,oBAAoB;YAClBsB,wBACE,0EACE,6BAACG;gBAAKC,MAAK;gBAASC,SAAQ;gBAC3BjD,QAAQC,GAAG,CAACC,QAAQ,KAAK,+BACxB,6BAAC6C;gBAAKC,MAAK;gBAAaC,SAAQ;gBAEjC3E,8BACD,6BAACD;QAGP;IACF;IAEA,MAAM6E,QAAQ;QACZ,GAAGV,uBAAuB;QAC1B,GAAGlB,iBAAiB;QACpB,8GAA8G;QAC9G,gEAAgE;QAChE,+GAA+G;QAC/G6B,QAAQ5C;QACR,iCAAiC;QACjC,GAAG,AAAC,CAAA;YACF,IAAIsC,qBAAqB3H,sBAAsB6D,kBAAkB,EAAE;gBACjE,OAAO,CAAC;YACV;YAEA,IAAIf,QAAQ;gBACV,OAAOjC;YACT;QACF,CAAA,GAAI;IACN;IAEA,kEAAkE;IAClE,IAAI,CAAC8G,mBAAmB;QACtBtD,YAAY,MAAMsB,QAAQuC,OAAO,GAAGC,IAAI,CAAC,IACvCC,IAAAA,kCAAgB,EAAC/D,WAAW2D;IAEhC;IAEA,OAAO;QACL3D,WAAW;YACT,qBACE,4DACGvB,SAASlD,iBAAiB,MAE1BkD,UAAU6E,kCACT,6BAACrH;gBACC+H,mBAAmBL;gBACnB3D,WAAWA;gBACXR,oBAAoB7D,sBAAsB6D,kBAAkB;+BAG9D,6BAACQ,WAAc2D,QAUhB;QAGP;QACAnB,QAAQhF;IACV;AACF"}