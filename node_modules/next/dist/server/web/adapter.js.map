{"version": 3, "sources": ["../../../src/server/web/adapter.ts"], "names": ["adapter", "NextRequestHint", "NextRequest", "constructor", "params", "input", "init", "sourcePage", "page", "request", "PageSignatureError", "respondWith", "waitUntil", "FLIGHT_PARAMETERS", "RSC", "NEXT_ROUTER_STATE_TREE", "NEXT_ROUTER_PREFETCH", "ensureInstrumentationRegistered", "isEdgeRendering", "self", "__BUILD_MANIFEST", "prerenderManifest", "__PRERENDER_MANIFEST", "JSON", "parse", "undefined", "url", "normalizeRscURL", "requestUrl", "NextURL", "headers", "nextConfig", "keys", "searchParams", "key", "value", "getAll", "NEXT_QUERY_PARAM_PREFIX", "startsWith", "normalizedKey", "substring", "length", "delete", "val", "append", "buildId", "isDataReq", "pathname", "requestHeaders", "fromNodeOutgoingHttpHeaders", "flightHeaders", "Map", "param", "toString", "toLowerCase", "get", "set", "normalizeUrl", "process", "env", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "URL", "stripInternalSearchParams", "body", "geo", "ip", "method", "signal", "Object", "defineProperty", "enumerable", "globalThis", "__incrementalCache", "IncrementalCache", "appDir", "fetchCache", "minimalMode", "NODE_ENV", "fetchCacheKeyPrefix", "__NEXT_FETCH_CACHE_KEY_PREFIX", "dev", "requestProtocol", "getPrerenderManifest", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "previewModeId", "event", "NextFetchEvent", "response", "cookiesFromResponse", "isMiddleware", "RequestAsyncStorageWrapper", "wrap", "requestAsyncStorage", "req", "renderOpts", "onUpdateCookies", "cookies", "previewProps", "previewModeEncryptionKey", "previewModeSigningKey", "handler", "Response", "TypeError", "rewrite", "rewriteUrl", "forceLocale", "host", "nextUrl", "String", "relativizedRewrite", "relativizeURL", "__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE", "match", "redirect", "redirectURL", "finalResponse", "NextResponse", "next", "middlewareOverrideHeaders", "overwrittenHeaders", "push", "join", "Promise", "all", "waitUntilSymbol", "fetchMetrics"], "mappings": ";;;;+BA8<PERSON><PERSON>;;;eAAAA;;;uBA3Da;uBACS;4BACb;yBACH;0BACC;+BACC;yBAEN;+BACkB;0BACV;kCAKzB;2BACiC;yBACQ;4CACL;6CACP;AAEpC,MAAMC,wBAAwBC,oBAAW;IAIvCC,YAAYC,MAIX,CAAE;QACD,KAAK,CAACA,OAAOC,KAAK,EAAED,OAAOE,IAAI;QAC/B,IAAI,CAACC,UAAU,GAAGH,OAAOI,IAAI;IAC/B;IAEA,IAAIC,UAAU;QACZ,MAAM,IAAIC,yBAAkB,CAAC;YAAEF,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;IAEAI,cAAc;QACZ,MAAM,IAAID,yBAAkB,CAAC;YAAEF,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;IAEAK,YAAY;QACV,MAAM,IAAIF,yBAAkB,CAAC;YAAEF,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;AACF;AAEA,MAAMM,oBAAoB;IACxB;QAACC,qBAAG;KAAC;IACL;QAACC,wCAAsB;KAAC;IACxB;QAACC,sCAAoB;KAAC;CACvB;AASM,eAAehB,QACpBI,MAAsB;IAEtB,MAAMa,IAAAA,wCAA+B;IAErC,yCAAyC;IACzC,MAAMC,kBAAkB,OAAOC,KAAKC,gBAAgB,KAAK;IACzD,MAAMC,oBACJ,OAAOF,KAAKG,oBAAoB,KAAK,WACjCC,KAAKC,KAAK,CAACL,KAAKG,oBAAoB,IACpCG;IAENrB,OAAOK,OAAO,CAACiB,GAAG,GAAGC,IAAAA,yBAAe,EAACvB,OAAOK,OAAO,CAACiB,GAAG;IAEvD,MAAME,aAAa,IAAIC,gBAAO,CAACzB,OAAOK,OAAO,CAACiB,GAAG,EAAE;QACjDI,SAAS1B,OAAOK,OAAO,CAACqB,OAAO;QAC/BC,YAAY3B,OAAOK,OAAO,CAACsB,UAAU;IACvC;IAEA,yIAAyI;IACzI,4CAA4C;IAC5C,MAAMC,OAAO;WAAIJ,WAAWK,YAAY,CAACD,IAAI;KAAG;IAChD,KAAK,MAAME,OAAOF,KAAM;QACtB,MAAMG,QAAQP,WAAWK,YAAY,CAACG,MAAM,CAACF;QAE7C,IACEA,QAAQG,kCAAuB,IAC/BH,IAAII,UAAU,CAACD,kCAAuB,GACtC;YACA,MAAME,gBAAgBL,IAAIM,SAAS,CAACH,kCAAuB,CAACI,MAAM;YAClEb,WAAWK,YAAY,CAACS,MAAM,CAACH;YAE/B,KAAK,MAAMI,OAAOR,MAAO;gBACvBP,WAAWK,YAAY,CAACW,MAAM,CAACL,eAAeI;YAChD;YACAf,WAAWK,YAAY,CAACS,MAAM,CAACR;QACjC;IACF;IAEA,4DAA4D;IAC5D,MAAMW,UAAUjB,WAAWiB,OAAO;IAClCjB,WAAWiB,OAAO,GAAG;IAErB,MAAMC,YAAY1C,OAAOK,OAAO,CAACqB,OAAO,CAAC,gBAAgB;IAEzD,IAAIgB,aAAalB,WAAWmB,QAAQ,KAAK,UAAU;QACjDnB,WAAWmB,QAAQ,GAAG;IACxB;IAEA,MAAMC,iBAAiBC,IAAAA,kCAA2B,EAAC7C,OAAOK,OAAO,CAACqB,OAAO;IACzE,MAAMoB,gBAAgB,IAAIC;IAC1B,oDAAoD;IACpD,IAAI,CAACjC,iBAAiB;QACpB,KAAK,MAAMkC,SAASvC,kBAAmB;YACrC,MAAMqB,MAAMkB,MAAMC,QAAQ,GAAGC,WAAW;YACxC,MAAMnB,QAAQa,eAAeO,GAAG,CAACrB;YACjC,IAAIC,OAAO;gBACTe,cAAcM,GAAG,CAACtB,KAAKc,eAAeO,GAAG,CAACrB;gBAC1Cc,eAAeN,MAAM,CAACR;YACxB;QACF;IACF;IAEA,MAAMuB,eAAeC,QAAQC,GAAG,CAACC,kCAAkC,GAC/D,IAAIC,IAAIzD,OAAOK,OAAO,CAACiB,GAAG,IAC1BE;IAEJ,MAAMnB,UAAU,IAAIR,gBAAgB;QAClCO,MAAMJ,OAAOI,IAAI;QACjB,mDAAmD;QACnDH,OAAOyD,IAAAA,wCAAyB,EAACL,cAAc,MAAMJ,QAAQ;QAC7D/C,MAAM;YACJyD,MAAM3D,OAAOK,OAAO,CAACsD,IAAI;YACzBC,KAAK5D,OAAOK,OAAO,CAACuD,GAAG;YACvBlC,SAASkB;YACTiB,IAAI7D,OAAOK,OAAO,CAACwD,EAAE;YACrBC,QAAQ9D,OAAOK,OAAO,CAACyD,MAAM;YAC7BnC,YAAY3B,OAAOK,OAAO,CAACsB,UAAU;YACrCoC,QAAQ/D,OAAOK,OAAO,CAAC0D,MAAM;QAC/B;IACF;IAEA;;;;GAIC,GACD,IAAIrB,WAAW;QACbsB,OAAOC,cAAc,CAAC5D,SAAS,YAAY;YACzC6D,YAAY;YACZnC,OAAO;QACT;IACF;IAEA,IACE,CAAC,AAACoC,WAAmBC,kBAAkB,IACvC,AAACpE,OAAeqE,gBAAgB,EAChC;QACEF,WAAmBC,kBAAkB,GAAG,IAAI,AAC5CpE,OACAqE,gBAAgB,CAAC;YACjBC,QAAQ;YACRC,YAAY;YACZC,aAAalB,QAAQC,GAAG,CAACkB,QAAQ,KAAK;YACtCC,qBAAqBpB,QAAQC,GAAG,CAACoB,6BAA6B;YAC9DC,KAAKtB,QAAQC,GAAG,CAACkB,QAAQ,KAAK;YAC9B7B,gBAAgB5C,OAAOK,OAAO,CAACqB,OAAO;YACtCmD,iBAAiB;YACjBC,sBAAsB;gBACpB,OAAO;oBACLC,SAAS,CAAC;oBACVC,QAAQ,CAAC;oBACTC,eAAe,CAAC;oBAChBC,gBAAgB,EAAE;oBAClBC,SAAS;wBACPC,eAAe;oBACjB;gBACF;YACF;QACF;IACF;IAEA,MAAMC,QAAQ,IAAIC,0BAAc,CAAC;QAAEjF;QAASD,MAAMJ,OAAOI,IAAI;IAAC;IAC9D,IAAImF;IACJ,IAAIC;IAEJ,8DAA8D;IAC9D,MAAMC,eACJzF,OAAOI,IAAI,KAAK,iBAAiBJ,OAAOI,IAAI,KAAK;IACnD,IAAIqF,cAAc;QAChBF,WAAW,MAAMG,sDAA0B,CAACC,IAAI,CAC9CC,gDAAmB,EACnB;YACEC,KAAKxF;YACLyF,YAAY;gBACVC,iBAAiB,CAACC;oBAChBR,sBAAsBQ;gBACxB;gBACA,2EAA2E;gBAC3EC,cAAchF,CAAAA,qCAAAA,kBAAmBkE,OAAO,KAAI;oBAC1CC,eAAe;oBACfc,0BAA0B;oBAC1BC,uBAAuB;gBACzB;YACF;QACF,GACA,IAAMnG,OAAOoG,OAAO,CAAC/F,SAASgF;IAElC,OAAO;QACLE,WAAW,MAAMvF,OAAOoG,OAAO,CAAC/F,SAASgF;IAC3C;IAEA,yCAAyC;IACzC,IAAIE,YAAY,CAAEA,CAAAA,oBAAoBc,QAAO,GAAI;QAC/C,MAAM,IAAIC,UAAU;IACtB;IAEA,IAAIf,YAAYC,qBAAqB;QACnCD,SAAS7D,OAAO,CAAC0B,GAAG,CAAC,cAAcoC;IACrC;IAEA;;;;;GAKC,GACD,MAAMe,UAAUhB,4BAAAA,SAAU7D,OAAO,CAACyB,GAAG,CAAC;IACtC,IAAIoC,YAAYgB,SAAS;QACvB,MAAMC,aAAa,IAAI/E,gBAAO,CAAC8E,SAAS;YACtCE,aAAa;YACb/E,SAAS1B,OAAOK,OAAO,CAACqB,OAAO;YAC/BC,YAAY3B,OAAOK,OAAO,CAACsB,UAAU;QACvC;QAEA,IAAI,CAAC2B,QAAQC,GAAG,CAACC,kCAAkC,EAAE;YACnD,IAAIgD,WAAWE,IAAI,KAAKrG,QAAQsG,OAAO,CAACD,IAAI,EAAE;gBAC5CF,WAAW/D,OAAO,GAAGA,WAAW+D,WAAW/D,OAAO;gBAClD8C,SAAS7D,OAAO,CAAC0B,GAAG,CAAC,wBAAwBwD,OAAOJ;YACtD;QACF;QAEA;;;;KAIC,GACD,MAAMK,qBAAqBC,IAAAA,4BAAa,EACtCF,OAAOJ,aACPI,OAAOpF;QAGT,IACEkB,aACA,kDAAkD;QAClD,oDAAoD;QACpD,yCAAyC;QACzC,CACEY,CAAAA,QAAQC,GAAG,CAACwD,0CAA0C,IACtDF,mBAAmBG,KAAK,CAAC,gBAAe,GAE1C;YACAzB,SAAS7D,OAAO,CAAC0B,GAAG,CAAC,oBAAoByD;QAC3C;IACF;IAEA;;;;GAIC,GACD,MAAMI,WAAW1B,4BAAAA,SAAU7D,OAAO,CAACyB,GAAG,CAAC;IACvC,IAAIoC,YAAY0B,YAAY,CAACnG,iBAAiB;QAC5C,MAAMoG,cAAc,IAAIzF,gBAAO,CAACwF,UAAU;YACxCR,aAAa;YACb/E,SAAS1B,OAAOK,OAAO,CAACqB,OAAO;YAC/BC,YAAY3B,OAAOK,OAAO,CAACsB,UAAU;QACvC;QAEA;;;KAGC,GACD4D,WAAW,IAAIc,SAASd,SAAS5B,IAAI,EAAE4B;QAEvC,IAAI,CAACjC,QAAQC,GAAG,CAACC,kCAAkC,EAAE;YACnD,IAAI0D,YAAYR,IAAI,KAAKrG,QAAQsG,OAAO,CAACD,IAAI,EAAE;gBAC7CQ,YAAYzE,OAAO,GAAGA,WAAWyE,YAAYzE,OAAO;gBACpD8C,SAAS7D,OAAO,CAAC0B,GAAG,CAAC,YAAYwD,OAAOM;YAC1C;QACF;QAEA;;;;KAIC,GACD,IAAIxE,WAAW;YACb6C,SAAS7D,OAAO,CAACY,MAAM,CAAC;YACxBiD,SAAS7D,OAAO,CAAC0B,GAAG,CAClB,qBACA0D,IAAAA,4BAAa,EAACF,OAAOM,cAAcN,OAAOpF;QAE9C;IACF;IAEA,MAAM2F,gBAAgB5B,WAAWA,WAAW6B,sBAAY,CAACC,IAAI;IAE7D,iFAAiF;IACjF,MAAMC,4BAA4BH,cAAczF,OAAO,CAACyB,GAAG,CACzD;IAEF,MAAMoE,qBAA+B,EAAE;IACvC,IAAID,2BAA2B;QAC7B,KAAK,MAAM,CAACxF,KAAKC,MAAM,IAAIe,cAAe;YACxCqE,cAAczF,OAAO,CAAC0B,GAAG,CAAC,CAAC,qBAAqB,EAAEtB,IAAI,CAAC,EAAEC;YACzDwF,mBAAmBC,IAAI,CAAC1F;QAC1B;QAEA,IAAIyF,mBAAmBlF,MAAM,GAAG,GAAG;YACjC8E,cAAczF,OAAO,CAAC0B,GAAG,CACvB,iCACAkE,4BAA4B,MAAMC,mBAAmBE,IAAI,CAAC;QAE9D;IACF;IAEA,OAAO;QACLlC,UAAU4B;QACV3G,WAAWkH,QAAQC,GAAG,CAACtC,KAAK,CAACuC,2BAAe,CAAC;QAC7CC,cAAcxH,QAAQwH,YAAY;IACpC;AACF"}