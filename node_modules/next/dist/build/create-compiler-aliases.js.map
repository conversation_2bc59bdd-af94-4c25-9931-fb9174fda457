{"version": 3, "sources": ["../../src/build/create-compiler-aliases.ts"], "names": ["createWebpackAliases", "createServerOnlyClientOnlyAliases", "createRSCAliases", "getOptimizedModuleAliases", "createServerComponentsNoopAliases", "isClient", "isEdgeServer", "isNodeServer", "dev", "config", "pagesDir", "appDir", "dir", "reactProductionProfiling", "hasRewrites", "distDir", "path", "join", "pageExtensions", "clientResolveRewrites", "require", "resolve", "customAppAliases", "customDocumentAliases", "nextDistPath", "PAGES_DIR_ALIAS", "reduce", "prev", "ext", "push", "NEXT_PROJECT_ROOT", "NEXT_PROJECT_ROOT_DIST", "undefined", "hasExternalOtelApiPackage", "images", "loaderFile", "next", "defaultOverrides", "APP_DIR_ALIAS", "ROOT_DIR_ALIAS", "DOT_NEXT_ALIAS", "getReactProfilingInProduction", "getBarrelOptimizationAliases", "experimental", "optimizePackageImports", "RSC_ACTION_VALIDATE_ALIAS", "RSC_ACTION_CLIENT_WRAPPER_ALIAS", "RSC_ACTION_PROXY_ALIAS", "RSC_ACTION_ENCRYPTION_ALIAS", "dirname", "setimmediate", "isServer", "bundledReactChannel", "layer", "alias", "react$", "WEBPACK_LAYERS", "serverSideRendering", "Object", "assign", "reactServerComponents", "unfetch", "url", "packages", "aliases", "mainFields", "pkg", "descriptionFileData", "descriptionFilePath", "field", "hasOwnProperty"], "mappings": ";;;;;;;;;;;;;;;;;;IAyBgBA,oBAAoB;eAApBA;;IA2LAC,iCAAiC;eAAjCA;;IAsBAC,gBAAgB;eAAhBA;;IAkFAC,yBAAyB;eAAzBA;;IAyDAC,iCAAiC;eAAjCA;;;6DArXC;2BAWV;6BAE0B;+BAK1B;;;;;;AAOA,SAASJ,qBAAqB,EACnCK,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,GAAG,EACHC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,GAAG,EACHC,wBAAwB,EACxBC,WAAW,EAYZ;IACC,MAAMC,UAAUC,aAAI,CAACC,IAAI,CAACL,KAAKH,OAAOM,OAAO;IAC7C,MAAMG,iBAAiBT,OAAOS,cAAc;IAC5C,MAAMC,wBAAwBC,QAAQC,OAAO,CAC3C;IAEF,MAAMC,mBAAoC,CAAC;IAC3C,MAAMC,wBAAyC,CAAC;IAEhD,oDAAoD;IACpD,qDAAqD;IACrD,sCAAsC;IACtC,IAAIf,KAAK;QACP,MAAMgB,eAAe,eAAgBlB,CAAAA,eAAe,SAAS,EAAC;QAC9DgB,gBAAgB,CAAC,CAAC,EAAEG,0BAAe,CAAC,KAAK,CAAC,CAAC,GAAG;eACxCf,WACAQ,eAAeQ,MAAM,CAAC,CAACC,MAAMC;gBAC3BD,KAAKE,IAAI,CAACb,aAAI,CAACC,IAAI,CAACP,UAAU,CAAC,KAAK,EAAEkB,IAAI,CAAC;gBAC3C,OAAOD;YACT,GAAG,EAAE,IACL,EAAE;YACN,CAAC,EAAEH,aAAa,aAAa,CAAC;SAC/B;QACDF,gBAAgB,CAAC,CAAC,EAAEG,0BAAe,CAAC,OAAO,CAAC,CAAC,GAAG;eAC1Cf,WACAQ,eAAeQ,MAAM,CAAC,CAACC,MAAMC;gBAC3BD,KAAKE,IAAI,CAACb,aAAI,CAACC,IAAI,CAACP,UAAU,CAAC,OAAO,EAAEkB,IAAI,CAAC;gBAC7C,OAAOD;YACT,GAAG,EAAE,IACL,EAAE;YACN,CAAC,EAAEH,aAAa,eAAe,CAAC;SACjC;QACDD,qBAAqB,CAAC,CAAC,EAAEE,0BAAe,CAAC,UAAU,CAAC,CAAC,GAAG;eAClDf,WACAQ,eAAeQ,MAAM,CAAC,CAACC,MAAMC;gBAC3BD,KAAKE,IAAI,CAACb,aAAI,CAACC,IAAI,CAACP,UAAU,CAAC,UAAU,EAAEkB,IAAI,CAAC;gBAChD,OAAOD;YACT,GAAG,EAAE,IACL,EAAE;YACN,CAAC,EAAEH,aAAa,kBAAkB,CAAC;SACpC;IACH;IAEA,OAAO;QACL,cAAc;QAEd,mDAAmD;QACnD,0CAA0C;QAC1C,GAAIlB,eACA;YACE,mBAAmB;YACnB,oBAAoB;YACpB,oBAAoB;YACpB,mBAAmB;YACnB,iBAAiB;YACjB,oBAAoB;YAEpB,sCAAsC;YACtC,CAACU,aAAI,CAACC,IAAI,CAACa,gCAAiB,EAAE,UAAU,EACtC;YACF,CAACd,aAAI,CAACC,IAAI,CAACa,gCAAiB,EAAE,MAAM,EAClC;YACF,CAACd,aAAI,CAACC,IAAI,CAACc,qCAAsB,EAAE,UAAU,QAAQ,EACnD;YACF,CAACf,aAAI,CAACC,IAAI,CACRa,gCAAiB,EACjB,QACA,UACA,OACA,kBACA,EAAE;YACJ,CAACd,aAAI,CAACC,IAAI,CAACc,qCAAsB,EAAE,UAAU,UAAU,EACrD;YACF,CAACf,aAAI,CAACC,IAAI,CAACc,qCAAsB,EAAE,UAAU,UAAU,EACrD;YACF,CAACf,aAAI,CAACC,IAAI,CAACc,qCAAsB,EAAE,UAAU,OAAO,QAAQ,EAC1D;YACF,CAACf,aAAI,CAACC,IAAI,CAACc,qCAAsB,EAAE,UAAU,OAAO,WAAW,EAC7D;YACF,CAACf,aAAI,CAACC,IAAI,CAACc,qCAAsB,EAAE,SAAS,aAAa,EACvD;YACF,CAACf,aAAI,CAACC,IAAI,CAACc,qCAAsB,EAAE,SAAS,QAAQ,EAClD;YACF,CAACf,aAAI,CAACC,IAAI,CACRc,qCAAsB,EACtB,UACA,cACA,cACA,EAAE;YACJ,CAACf,aAAI,CAACC,IAAI,CACRc,qCAAsB,EACtB,UACA,cACA,WACA,EAAE;QACN,IACAC,SAAS;QAEb,wBAAwB;QACxB,GAAI,CAACC,IAAAA,wCAAyB,OAAM;YAClC,sBAAsB;QACxB,CAAC;QAED,GAAIxB,OAAOyB,MAAM,CAACC,UAAU,GACxB;YACE,qCAAqC1B,OAAOyB,MAAM,CAACC,UAAU;YAC7D,GAAI7B,gBAAgB;gBAClB,yCAAyCG,OAAOyB,MAAM,CAACC,UAAU;YACnE,CAAC;QACH,IACAH,SAAS;QAEbI,MAAMN,gCAAiB;QAEvB,qBAAqBO,6BAAgB,CAAC,mBAAmB;QACzD,eAAeA,6BAAgB,CAAC,aAAa;QAE7C,GAAGf,gBAAgB;QACnB,GAAGC,qBAAqB;QAExB,GAAIb,WAAW;YAAE,CAACe,0BAAe,CAAC,EAAEf;QAAS,IAAI,CAAC,CAAC;QACnD,GAAIC,SAAS;YAAE,CAAC2B,wBAAa,CAAC,EAAE3B;QAAO,IAAI,CAAC,CAAC;QAC7C,CAAC4B,yBAAc,CAAC,EAAE3B;QAClB,CAAC4B,yBAAc,CAAC,EAAEzB;QAClB,GAAIV,YAAYC,eAAeH,8BAA8B,CAAC,CAAC;QAC/D,GAAIU,2BAA2B4B,kCAAkC,CAAC,CAAC;QAEnE,wEAAwE;QACxE,6BAA6B;QAC7B,GAAIlC,eACAmC,6BACEjC,OAAOkC,YAAY,CAACC,sBAAsB,IAAI,EAAE,IAElD,CAAC,CAAC;QAEN,CAACC,oCAAyB,CAAC,EACzB;QAEF,CAACC,0CAA+B,CAAC,EAC/B;QAEF,CAACC,iCAAsB,CAAC,EACtB;QAEF,CAACC,sCAA2B,CAAC,EAC3B;QAEF,GAAI3C,YAAYC,eACZ;YACE,CAACa,sBAAsB,EAAEL,cACrBK,wBAEA;QACN,IACA,CAAC,CAAC;QAEN,kBAAkBH,aAAI,CAACC,IAAI,CACzBD,aAAI,CAACiC,OAAO,CAAC7B,QAAQC,OAAO,CAAC,+BAC7B;QAGF6B,cAAc;IAChB;AACF;AAEO,SAASjD,kCACdkD,QAAiB;IAEjB,OAAOA,WACH;QACE,gBAAgB;QAChB,gBAAgB;QAChB,mCACE;QACF,mCACE;IACJ,IACA;QACE,gBAAgB;QAChB,gBAAgB;QAChB,mCACE;QACF,kCACE;IACJ;AACN;AAEO,SAASjD,iBACdkD,mBAA2B,EAC3B,EACEC,KAAK,EACL/C,YAAY,EACZO,wBAAwB,EAKzB;IAED,IAAIyC,QAAgC;QAClCC,QAAQ,CAAC,wBAAwB,EAAEH,oBAAoB,CAAC;QACxD,cAAc,CAAC,4BAA4B,EAAEA,oBAAoB,CAAC;QAClE,sBAAsB,CAAC,wBAAwB,EAAEA,oBAAoB,YAAY,CAAC;QAClF,0BAA0B,CAAC,wBAAwB,EAAEA,oBAAoB,gBAAgB,CAAC;QAC1F,qBAAqB,CAAC,4BAA4B,EAAEA,oBAAoB,OAAO,CAAC;QAChF,qBAAqB,CAAC,4BAA4B,EAAEA,oBAAoB,OAAO,CAAC;QAChF,qBAAqB,CAAC,gDAAgD,CAAC;QACvE,0BAA0B,CAAC,qDAAqD,CAAC;QACjF,6BAA6B,CAAC,wDAAwD,CAAC;QACvF,0BAA0B,CAAC,4BAA4B,EAAEA,oBAAoB,YAAY,CAAC;QAC1F,6BAA6B,CAAC,4BAA4B,EAAEA,oBAAoB,eAAe,CAAC;QAChG,oCAAoC,CAAC,2CAA2C,EAAEA,oBAAoB,OAAO,CAAC;QAC9G,yCAAyC,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;QACxH,yCAAyC,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;QACxH,yCAAyC,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;QACxH,+DAA+D;QAC/D,2DAA2D,CAAC,4CAA4C,CAAC;QACzG,wDAAwD,CAAC,4CAA4C,CAAC;IACxG;IAEA,IAAI,CAAC9C,cAAc;QACjB,IAAI+C,UAAUG,yBAAc,CAACC,mBAAmB,EAAE;YAChDH,QAAQI,OAAOC,MAAM,CAACL,OAAO;gBAC3B,sBAAsB,CAAC,wDAAwD,EAAED,MAAM,kBAAkB,CAAC;gBAC1G,0BAA0B,CAAC,wDAAwD,EAAEA,MAAM,sBAAsB,CAAC;gBAClHE,QAAQ,CAAC,wDAAwD,EAAEF,MAAM,MAAM,CAAC;gBAChF,cAAc,CAAC,wDAAwD,EAAEA,MAAM,UAAU,CAAC;gBAC1F,yCAAyC,CAAC,wDAAwD,EAAEA,MAAM,qCAAqC,CAAC;YAClJ;QACF,OAAO,IAAIA,UAAUG,yBAAc,CAACI,qBAAqB,EAAE;YACzDN,QAAQI,OAAOC,MAAM,CAACL,OAAO;gBAC3B,sBAAsB,CAAC,wDAAwD,EAAED,MAAM,kBAAkB,CAAC;gBAC1G,0BAA0B,CAAC,wDAAwD,EAAEA,MAAM,sBAAsB,CAAC;gBAClHE,QAAQ,CAAC,wDAAwD,EAAEF,MAAM,MAAM,CAAC;gBAChF,cAAc,CAAC,wDAAwD,EAAEA,MAAM,UAAU,CAAC;gBAC1F,yCAAyC,CAAC,wDAAwD,EAAEA,MAAM,qCAAqC,CAAC;gBAChJ,yCAAyC,CAAC,wDAAwD,EAAEA,MAAM,qCAAqC,CAAC;YAClJ;QACF;IACF;IAEA,IAAI/C,cAAc;QAChB,IAAI+C,UAAUG,yBAAc,CAACI,qBAAqB,EAAE;YAClDN,KAAK,CACH,SACD,GAAG,CAAC,wBAAwB,EAAEF,oBAAoB,oBAAoB,CAAC;QAC1E;QACA,4CAA4C;QAC5C,sDAAsD;QACtDE,KAAK,CACH,aACD,GAAG,CAAC,4BAA4B,EAAEF,oBAAoB,sBAAsB,CAAC;IAChF;IAEA,IAAIvC,0BAA0B;QAC5ByC,KAAK,CACH,aACD,GAAG,CAAC,4BAA4B,EAAEF,oBAAoB,UAAU,CAAC;IACpE;IAEAE,KAAK,CACH,gEACD,GAAG,CAAC,uCAAuC,CAAC;IAE7C,OAAOA;AACT;AAIO,SAASnD;IACd,OAAO;QACL0D,SAASzC,QAAQC,OAAO,CAAC;QACzB,sBAAsBD,QAAQC,OAAO,CACnC;QAEF,gBAAgBD,QAAQC,OAAO,CAC7B;QAEF,iBAAiBD,QAAQC,OAAO,CAC9B;QAEF,sBAAsBD,QAAQC,OAAO,CACnC;QAEF,gCAAgCD,QAAQC,OAAO,CAC7C;QAEF,0BAA0BD,QAAQC,OAAO,CACvC;QAEF,sBAAsBD,QAAQC,OAAO,CACnC;QAEFyC,KAAK1C,QAAQC,OAAO,CAAC;IACvB;AACF;AAEA,gEAAgE;AAChE,SAASqB,6BAA6BqB,QAAkB;IACtD,MAAMC,UAAqC,CAAC;IAC5C,MAAMC,aAAa;QAAC;QAAU;KAAO;IAErC,KAAK,MAAMC,OAAOH,SAAU;QAC1B,IAAI;YACF,MAAMI,sBAAsB/C,QAAQ,CAAC,EAAE8C,IAAI,aAAa,CAAC;YACzD,MAAME,sBAAsBhD,QAAQC,OAAO,CAAC,CAAC,EAAE6C,IAAI,aAAa,CAAC;YAEjE,KAAK,MAAMG,SAASJ,WAAY;gBAC9B,IAAIE,oBAAoBG,cAAc,CAACD,QAAQ;oBAC7CL,OAAO,CAACE,MAAM,IAAI,GAAGlD,aAAI,CAACC,IAAI,CAC5BD,aAAI,CAACiC,OAAO,CAACmB,sBACbD,mBAAmB,CAACE,MAAM;oBAE5B;gBACF;YACF;QACF,EAAE,OAAM,CAAC;IACX;IAEA,OAAOL;AACT;AACA,SAASvB;IACP,OAAO;QACL,cAAc;IAChB;AACF;AACO,SAASrC;IACd,OAAO;QACL,CAACgB,QAAQC,OAAO,CAAC,aAAa,EAAED,QAAQC,OAAO,CAC7C;QAEF,qBAAqB;QACrB,CAACD,QAAQC,OAAO,CAAC,gBAAgB,EAAED,QAAQC,OAAO,CAChD;IAEJ;AACF"}