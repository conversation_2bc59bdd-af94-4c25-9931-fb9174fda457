{"version": 3, "sources": ["../../../src/build/webpack-build/index.ts"], "names": ["webpackBuild", "debug", "origDebug", "ORDERED_COMPILER_NAMES", "pluginState", "deepMerge", "target", "source", "result", "key", "Object", "keys", "Array", "isArray", "webpackBuildWithWorker", "compilerNames", "config", "telemetryPlugin", "buildSpinner", "nextBuildSpan", "prunedBuildContext", "NextBuildContext", "getWorker", "compilerName", "_worker", "Worker", "path", "join", "__dirname", "exposedMethods", "numWorkers", "maxRetries", "forkOptions", "env", "process", "NEXT_PRIVATE_BUILD_WORKER", "getStderr", "pipe", "stderr", "getStdout", "stdout", "worker", "_workerPool", "_workers", "_child", "on", "code", "signal", "console", "error", "combinedResult", "duration", "buildTraceContext", "curR<PERSON>ult", "worker<PERSON>ain", "buildContext", "end", "entriesTrace", "entryNameMap", "chunksTrace", "entryNameFilesMap", "length", "stopAndPersist", "Log", "event", "with<PERSON><PERSON>ker", "webpackBuildImpl", "require"], "mappings": ";;;;+BA+HgBA;;;eAAAA;;;6DA9HK;8BACY;4BAEV;8DACD;6DAEL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEjB,MAAMC,QAAQC,IAAAA,cAAS,EAAC;AAExB,MAAMC,yBAAyB;IAC7B;IACA;IACA;CACD;AAED,IAAIC,cAAgC,CAAC;AAErC,SAASC,UAAUC,MAAW,EAAEC,MAAW;IACzC,MAAMC,SAAS;QAAE,GAAGF,MAAM;QAAE,GAAGC,MAAM;IAAC;IACtC,KAAK,MAAME,OAAOC,OAAOC,IAAI,CAACH,QAAS;QACrCA,MAAM,CAACC,IAAI,GAAGG,MAAMC,OAAO,CAACP,MAAM,CAACG,IAAI,IAClCH,MAAM,CAACG,IAAI,GAAG;eAAIH,MAAM,CAACG,IAAI;eAAMF,MAAM,CAACE,IAAI,IAAI,EAAE;SAAE,GACvD,OAAOH,MAAM,CAACG,IAAI,IAAI,YAAY,OAAOF,MAAM,CAACE,IAAI,IAAI,WACxDJ,UAAUC,MAAM,CAACG,IAAI,EAAEF,MAAM,CAACE,IAAI,IAClCD,MAAM,CAACC,IAAI;IACjB;IACA,OAAOD;AACT;AAEA,eAAeM,uBACbC,gBAA+CZ,sBAAsB;IAErE,MAAM,EACJa,MAAM,EACNC,eAAe,EACfC,YAAY,EACZC,aAAa,EACb,GAAGC,oBACJ,GAAGC,8BAAgB;IAEpBD,mBAAmBhB,WAAW,GAAGA;IAEjC,MAAMkB,YAAY,CAACC;YAeK;QAdtB,MAAMC,UAAU,IAAIC,kBAAM,CAACC,aAAI,CAACC,IAAI,CAACC,WAAW,YAAY;YAC1DC,gBAAgB;gBAAC;aAAa;YAC9BC,YAAY;YACZC,YAAY;YACZC,aAAa;gBACXC,KAAK;oBACH,GAAGC,QAAQD,GAAG;oBACdE,2BAA2B;gBAC7B;YACF;QACF;QACAX,QAAQY,SAAS,GAAGC,IAAI,CAACH,QAAQI,MAAM;QACvCd,QAAQe,SAAS,GAAGF,IAAI,CAACH,QAAQM,MAAM;QAEvC,KAAK,MAAMC,UAAW,EAAA,sBAAA,AAACjB,QAAgBkB,WAAW,qBAA5B,oBAA8BC,QAAQ,KAAI,EAAE,CAE7D;YACHF,OAAOG,MAAM,CAACC,EAAE,CAAC,QAAQ,CAACC,MAAMC;gBAC9B,IAAID,QAASC,UAAUA,WAAW,UAAW;oBAC3CC,QAAQC,KAAK,CACX,CAAC,SAAS,EAAE1B,aAAa,gCAAgC,EAAEuB,KAAK,aAAa,EAAEC,OAAO,CAAC;gBAE3F;YACF;QACF;QAEA,OAAOvB;IACT;IAEA,MAAM0B,iBAAiB;QACrBC,UAAU;QACVC,mBAAmB,CAAC;IACtB;IAEA,KAAK,MAAM7B,gBAAgBR,cAAe;YAgBpCsC;QAfJ,MAAMZ,SAASnB,UAAUC;QAEzB,MAAM8B,YAAY,MAAMZ,OAAOa,UAAU,CAAC;YACxCC,cAAcnC;YACdG;QACF;QACA,0DAA0D;QAC1D,MAAMkB,OAAOe,GAAG;QAEhB,sBAAsB;QACtBpD,cAAcC,UAAUD,aAAaiD,UAAUjD,WAAW;QAC1DgB,mBAAmBhB,WAAW,GAAGA;QAEjC8C,eAAeC,QAAQ,IAAIE,UAAUF,QAAQ;QAE7C,KAAIE,+BAAAA,UAAUD,iBAAiB,qBAA3BC,6BAA6BI,YAAY,EAAE;gBAUzCJ;YATJ,MAAM,EAAEK,YAAY,EAAE,GAAGL,UAAUD,iBAAiB,CAACK,YAAY;YAEjE,IAAIC,cAAc;gBAChBR,eAAeE,iBAAiB,CAACK,YAAY,GAC3CJ,UAAUD,iBAAiB,CAACK,YAAY;gBAC1CP,eAAeE,iBAAiB,CAACK,YAAY,CAAEC,YAAY,GACzDA;YACJ;YAEA,KAAIL,gCAAAA,UAAUD,iBAAiB,qBAA3BC,8BAA6BM,WAAW,EAAE;gBAC5C,MAAM,EAAEC,iBAAiB,EAAE,GAAGP,UAAUD,iBAAiB,CAACO,WAAW;gBAErE,IAAIC,mBAAmB;oBACrBV,eAAeE,iBAAiB,CAACO,WAAW,GAC1CN,UAAUD,iBAAiB,CAACO,WAAW;oBAEzCT,eAAeE,iBAAiB,CAACO,WAAW,CAAEC,iBAAiB,GAC7DA;gBACJ;YACF;QACF;IACF;IAEA,IAAI7C,cAAc8C,MAAM,KAAK,GAAG;QAC9B3C,gCAAAA,aAAc4C,cAAc;QAC5BC,KAAIC,KAAK,CAAC;IACZ;IAEA,OAAOd;AACT;AAEO,SAASlD,aACdiE,UAAmB,EACnBlD,aAA6C;IAE7C,IAAIkD,YAAY;QACdhE,MAAM;QACN,OAAOa,uBAAuBC;IAChC,OAAO;QACLd,MAAM;QACN,MAAMiE,mBAAmBC,QAAQ,UAAUD,gBAAgB;QAC3D,OAAOA;IACT;AACF"}