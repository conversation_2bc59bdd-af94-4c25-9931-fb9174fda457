{"version": 3, "sources": ["../../../src/build/swc/index.ts"], "names": ["path", "pathToFileURL", "platform", "arch", "platformArchTriples", "Log", "getParserOptions", "eventSwcLoadFailure", "patchIncorrectLockfile", "downloadWasmSwc", "downloadNativeNextSwc", "isDeepStrictEqual", "getDefineEnv", "nextVersion", "process", "env", "__NEXT_VERSION", "Arch<PERSON>ame", "PlatformName", "infoLog", "args", "NEXT_PRIVATE_BUILD_WORKER", "DEBUG", "info", "getSupportedArchTriples", "darwin", "win32", "linux", "arm64", "ia32", "filter", "triple", "abi", "x64", "triples", "supportedArchTriples", "targetTriple", "rawTargetTriple", "warn", "__INTERNAL_CUSTOM_TURBOPACK_BINDINGS", "checkVersionMismatch", "pkgData", "version", "lastNativeBindingsLoadErrorCode", "undefined", "nativeBindings", "wasmBindings", "downloadWasmPromise", "pendingBindings", "swcTraceFlushGuard", "swcHeapProfilerFlushGuard", "swcCrashReporterFlushGuard", "downloadNativeBindingsPromise", "lockfilePatchPromise", "loadBindings", "stdout", "_handle", "setBlocking", "stderr", "Promise", "resolve", "_reject", "cur", "cwd", "catch", "console", "error", "attempts", "loadNative", "a", "Array", "isArray", "every", "m", "includes", "fallback<PERSON><PERSON><PERSON>", "tryLoadNativeWithFallback", "concat", "logLoadFailure", "nativeBindingsDirectory", "join", "dirname", "require", "map", "platformArchABI", "bindings", "tryLoadWasmWithFallback", "loadWasm", "wasm", "nativeBindingsErrorCode", "wasmDirectory", "href", "attempt", "loadBindingsSync", "loggingLoadFailure", "then", "finally", "exit", "createDefineEnv", "isTurbopack", "allowedRevalidateHeaderKeys", "clientRouterFilters", "config", "dev", "distDir", "fetchCacheKeyPrefix", "hasRewrites", "middlewareMatchers", "previewModeId", "defineEnv", "client", "edge", "nodejs", "variant", "Object", "keys", "rustifyEnv", "isClient", "isEdgeServer", "isNodeOrEdgeCompilation", "isNodeServer", "entries", "_", "value", "name", "bindingToApi", "binding", "_wasm", "cancel", "Cancel", "Error", "invariant", "never", "computeMessage", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fn", "nativeError", "message", "cause", "subscribe", "useBuffer", "nativeFunction", "buffer", "waiting", "canceled", "emitResult", "err", "reject", "item", "push", "iterator", "task", "length", "shift", "e", "rootTaskDispose", "return", "done", "rustifyProjectOptions", "options", "nextConfig", "serializeNextConfig", "jsConfig", "JSON", "stringify", "ProjectImpl", "constructor", "nativeProject", "_nativeProject", "update", "projectUpdate", "entrypointsSubscribe", "subscription", "callback", "projectEntrypointsSubscribe", "entrypoints", "routes", "Map", "pathname", "nativeRoute", "route", "routeType", "type", "htmlEndpoint", "EndpointImpl", "dataEndpoint", "endpoint", "rscEndpoint", "_exhaustiveCheck", "set", "napiMiddlewareToMiddleware", "middleware", "runtime", "matcher", "pagesDocumentEndpoint", "pagesAppEndpoint", "pagesErrorEndpoint", "issues", "diagnostics", "hmrEvents", "identifier", "projectHmrEvents", "hmrIdentifiersSubscribe", "projectHmrIdentifiersSubscribe", "traceSource", "stackFrame", "projectTraceSource", "getSourceForAsset", "filePath", "projectGetSourceForAsset", "updateInfoSubscribe", "projectUpdateInfoSubscribe", "nativeEndpoint", "_nativeEndpoint", "writeToDisk", "endpointWriteToDisk", "clientChanged", "clientSubscription", "endpointClientChangedSubscribe", "next", "serverChanged", "includeIssues", "serverSubscription", "endpointServerChangedSubscribe", "nextConfigSerializable", "generateBuildId", "exportPathMap", "webpack", "experimental", "turbo", "rules", "ensureLoadersHaveSerializableOptions", "modularizeImports", "fromEntries", "mod", "transform", "key", "turbopackRules", "glob", "rule", "loaderItems", "loaders", "loaderItem", "parse", "loader", "createProject", "turboEngineOptions", "projectNew", "importPath", "pkg", "pkgPath", "default", "isWasm", "src", "toString", "transformSync", "minify", "minifySync", "parseSync", "astStr", "getTargetTriple", "startTrace", "stream", "turboTasks", "rootDir", "applicationDir", "pageExtensions", "callbackFn", "streamEntrypoints", "get", "getEntrypoints", "mdx", "compile", "mdxCompile", "getMdxOptions", "compileSync", "mdxCompileSync", "code", "customBindings", "isModule", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "jsc", "parser", "syntax", "<PERSON><PERSON><PERSON><PERSON>", "initCustomTraceSubscriber", "teardownTraceSubscriber", "initHeapProfiler", "teardownHeapProfiler", "teardownCrashReporter", "nextBuild", "ret", "runTurboTracing", "exact", "createTurboTasks", "memoryLimit", "development", "jsx", "gfmStrikethroughSingleTilde", "mathTextSingleDollar", "t", "from", "parserOptions", "getBinaryMetadata", "target", "traceFileName", "flushed"], "mappings": "AAAA,0DAA0D,GAC1D,OAAOA,UAAU,OAAM;AACvB,SAASC,aAAa,QAAQ,MAAK;AACnC,SAASC,QAAQ,EAAEC,IAAI,QAAQ,KAAI;AACnC,SAASC,mBAAmB,QAAQ,sCAAqC;AACzE,YAAYC,SAAS,gBAAe;AACpC,SAASC,gBAAgB,QAAQ,YAAW;AAC5C,SAASC,mBAAmB,QAAQ,0CAAyC;AAC7E,SAASC,sBAAsB,QAAQ,qCAAoC;AAC3E,SAASC,eAAe,EAAEC,qBAAqB,QAAQ,yBAAwB;AAE/E,SAASC,iBAAiB,QAAQ,OAAM;AACxC,SAASC,YAAY,QAAQ,uCAAsC;AAGnE,MAAMC,cAAcC,QAAQC,GAAG,CAACC,cAAc;AAE9C,MAAMC,WAAWd;AACjB,MAAMe,eAAehB;AAErB,MAAMiB,UAAU,CAAC,GAAGC;IAClB,IAAIN,QAAQC,GAAG,CAACM,yBAAyB,EAAE;QACzC;IACF;IACA,IAAIP,QAAQC,GAAG,CAACO,KAAK,EAAE;QACrBjB,IAAIkB,IAAI,IAAIH;IACd;AACF;AAEA;;CAEC,GACD,OAAO,MAAMI,0BAAqD;IAChE,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAE,GAAGvB;IAEjC,OAAO;QACLqB;QACAC,OAAO;YACLE,OAAOF,MAAME,KAAK;YAClBC,MAAMH,MAAMG,IAAI,CAACC,MAAM,CACrB,CAACC,SAA4BA,OAAOC,GAAG,KAAK;YAE9CC,KAAKP,MAAMO,GAAG,CAACH,MAAM,CAAC,CAACC,SAA4BA,OAAOC,GAAG,KAAK;QACpE;QACAL,OAAO;YACL,mDAAmD;YACnDM,KAAKN,MAAMM,GAAG,CAACH,MAAM,CACnB,CAACC,SAA4BA,OAAOC,GAAG,KAAK;YAE9CJ,OAAOD,MAAMC,KAAK;QACpB;IACF;AACF,EAAC;AAED,MAAMM,UAAU,AAAC,CAAA;QAEMC,oCASC/B;IAVtB,MAAM+B,uBAAuBX;IAC7B,MAAMY,gBAAeD,qCAAAA,oBAAoB,CAACjB,aAAa,qBAAlCiB,kCAAoC,CAAClB,SAAS;IAEnE,oDAAoD;IACpD,IAAImB,cAAc;QAChB,OAAOA;IACT;IAEA,yHAAyH;IACzH,qDAAqD;IACrD,IAAIC,mBAAkBjC,oCAAAA,mBAAmB,CAACc,aAAa,qBAAjCd,iCAAmC,CAACa,SAAS;IAEnE,IAAIoB,iBAAiB;QACnBhC,IAAIiC,IAAI,CACN,CAAC,0CAA0C,EAAED,gBAAgB,kCAAkC,CAAC;IAEpG,OAAO;QACLhC,IAAIiC,IAAI,CACN,CAAC,kDAAkD,EAAEpB,aAAa,CAAC,EAAED,SAAS,CAAC;IAEnF;IAEA,OAAO,EAAE;AACX,CAAA;AAEA,4EAA4E;AAC5E,qGAAqG;AACrG,oGAAoG;AACpG,kFAAkF;AAClF,EAAE;AACF,yEAAyE;AACzE,MAAMsB,uCACJzB,QAAQC,GAAG,CAACwB,oCAAoC;AAElD,SAASC,qBAAqBC,OAAY;IACxC,MAAMC,UAAUD,QAAQC,OAAO;IAE/B,IAAIA,WAAWA,YAAY7B,aAAa;QACtCR,IAAIiC,IAAI,CACN,CAAC,yCAAyC,EAAEI,QAAQ,qBAAqB,EAAE7B,YAAY,2BAA2B,CAAC;IAEvH;AACF;AAEA,oFAAoF;AACpF,gGAAgG;AAChG,oGAAoG;AACpG,IAAI8B,kCAIYC;AAChB,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC,gCAA2DR;AAE/D,OAAO,MAAMS,uBAAgD,CAAC,EAAC;AAmC/D,OAAO,eAAeC;IACpB,IAAIN,iBAAiB;QACnB,OAAOA;IACT;IAEA,iIAAiI;IACjI,qDAAqD;IACrD,uFAAuF;IACvF,IAAIlC,QAAQyC,MAAM,CAACC,OAAO,IAAI,MAAM;QAClC,aAAa;QACb1C,QAAQyC,MAAM,CAACC,OAAO,CAACC,WAAW,CAAC;IACrC;IACA,IAAI3C,QAAQ4C,MAAM,CAACF,OAAO,IAAI,MAAM;QAClC,aAAa;QACb1C,QAAQ4C,MAAM,CAACF,OAAO,CAACC,WAAW,CAAC;IACrC;IAEAT,kBAAkB,IAAIW,QAAQ,OAAOC,SAASC;QAC5C,IAAI,CAACR,qBAAqBS,GAAG,EAAE;YAC7B,yDAAyD;YACzD,0CAA0C;YAC1CT,qBAAqBS,GAAG,GAAGtD,uBAAuBM,QAAQiD,GAAG,IAAIC,KAAK,CACpEC,QAAQC,KAAK;QAEjB;QAEA,IAAIC,WAAkB,EAAE;QAExB,4CAA4C;QAC5C,EAAE;QACF,kEAAkE;QAClE,0GAA0G;QAC1G,gHAAgH;QAChH,kHAAkH;QAClH,kDAAkD;QAClD,IAAI;YACF,OAAOP,QAAQQ;QACjB,EAAE,OAAOC,GAAG;YACV,IACEC,MAAMC,OAAO,CAACF,MACdA,EAAEG,KAAK,CAAC,CAACC,IAAMA,EAAEC,QAAQ,CAAC,0BAC1B;gBACA,IAAIC,mBAAmB,MAAMC,0BAA0BT;gBAEvD,IAAIQ,kBAAkB;oBACpB,OAAOf,QAAQe;gBACjB;YACF;YAEAR,WAAWA,SAASU,MAAM,CAACR;QAC7B;QAEAS,eAAeX;IACjB;IACA,OAAOnB;AACT;AAEA,eAAe4B,0BAA0BT,QAAuB;IAC9D,MAAMY,0BAA0B/E,KAAKgF,IAAI,CACvChF,KAAKiF,OAAO,CAACC,QAAQtB,OAAO,CAAC,uBAC7B;IAGF,IAAI,CAACR,+BAA+B;QAClCA,gCAAgC1C,sBAC9BG,aACAkE,yBACA7C,QAAQiD,GAAG,CAAC,CAACpD,SAAgBA,OAAOqD,eAAe;IAEvD;IACA,MAAMhC;IAEN,IAAI;QACF,IAAIiC,WAAWjB,WAAWW;QAC1B,OAAOM;IACT,EAAE,OAAOhB,GAAQ;QACfF,SAASU,MAAM,CAACR;IAClB;IACA,OAAOzB;AACT;AAEA,uFAAuF;AACvF,6DAA6D;AAC7D,eAAe0C,wBAAwBnB,QAAa;IAClD,IAAI;QACF,IAAIkB,WAAW,MAAME,SAAS;QAC9B,sDAAsD;QACtDhF,oBAAoB;YAClBiF,MAAM;YACNC,yBAAyB9C;QAC3B;QACA,OAAO0C;IACT,EAAE,OAAOhB,GAAG;QACVF,WAAWA,SAASU,MAAM,CAACR;IAC7B;IAEA,IAAI;QACF,2DAA2D;QAC3D,+DAA+D;QAC/D,sEAAsE;QACtE,sDAAsD;QACtD,MAAMqB,gBAAgB1F,KAAKgF,IAAI,CAC7BhF,KAAKiF,OAAO,CAACC,QAAQtB,OAAO,CAAC,uBAC7B;QAEF,IAAI,CAACb,qBAAqB;YACxBA,sBAAsBtC,gBAAgBI,aAAa6E;QACrD;QACA,MAAM3C;QACN,IAAIsC,WAAW,MAAME,SAAStF,cAAcyF,eAAeC,IAAI;QAC/D,sDAAsD;QACtDpF,oBAAoB;YAClBiF,MAAM;YACNC,yBAAyB9C;QAC3B;QAEA,4CAA4C;QAC5C,sCAAsC;QACtC,KAAK,MAAMiD,WAAWzB,SAAU;YAC9B9D,IAAIiC,IAAI,CAACsD;QACX;QACA,OAAOP;IACT,EAAE,OAAOhB,GAAG;QACVF,WAAWA,SAASU,MAAM,CAACR;IAC7B;AACF;AAEA,SAASwB;IACP,IAAI1B,WAAkB,EAAE;IACxB,IAAI;QACF,OAAOC;IACT,EAAE,OAAOC,GAAG;QACVF,WAAWA,SAASU,MAAM,CAACR;IAC7B;IAEAS,eAAeX;AACjB;AAEA,IAAI2B,qBAAqB;AAEzB,SAAShB,eAAeX,QAAa;IACnC,4DAA4D;IAC5D,IAAI2B,oBAAoB;IACxBA,qBAAqB;IAErB,KAAK,IAAIF,WAAWzB,SAAU;QAC5B9D,IAAIiC,IAAI,CAACsD;IACX;IAEA,sDAAsD;IACtDrF,oBAAoB;QAClBkF,yBAAyB9C;IAC3B,GACGoD,IAAI,CAAC,IAAM1C,qBAAqBS,GAAG,IAAIH,QAAQC,OAAO,IACtDoC,OAAO,CAAC;QACP3F,IAAI6D,KAAK,CACP,CAAC,8BAA8B,EAAEhD,aAAa,CAAC,EAAED,SAAS,yEAAyE,CAAC;QAEtIH,QAAQmF,IAAI,CAAC;IACf;AACJ;AAwDA,OAAO,SAASC,gBAAgB,EAC9BC,WAAW,EACXC,2BAA2B,EAC3BC,mBAAmB,EACnBC,MAAM,EACNC,GAAG,EACHC,OAAO,EACPC,mBAAmB,EACnBC,WAAW,EACXC,kBAAkB,EAClBC,aAAa,EAId;IACC,IAAIC,YAAuB;QACzBC,QAAQ,EAAE;QACVC,MAAM,EAAE;QACRC,QAAQ,EAAE;IACZ;IAEA,KAAK,MAAMC,WAAWC,OAAOC,IAAI,CAACN,WAA0C;QAC1EA,SAAS,CAACI,QAAQ,GAAGG,WACnBxG,aAAa;YACXuF;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAW,UAAUJ,YAAY;YACtBK,cAAcL,YAAY;YAC1BM,yBAAyBN,YAAY,YAAYA,YAAY;YAC7DO,cAAcP,YAAY;YAC1BN;YACAC;QACF;IAEJ;IAEA,OAAOC;AACT;AAuKA,SAASO,WAAWrG,GAA2B;IAC7C,OAAOmG,OAAOO,OAAO,CAAC1G,KACnBe,MAAM,CAAC,CAAC,CAAC4F,GAAGC,MAAM,GAAKA,SAAS,MAChCxC,GAAG,CAAC,CAAC,CAACyC,MAAMD,MAAM,GAAM,CAAA;YACvBC;YACAD;QACF,CAAA;AACJ;AAEA,mCAAmC;AACnC,SAASE,aAAaC,OAAY,EAAEC,KAAc;IAKhD,MAAMC,SAAS,IAAK,MAAMC,eAAeC;IAAO;IAEhD;;GAEC,GACD,SAASC,UACPC,KAAY,EACZC,cAAoC;QAEpC,MAAM,IAAIH,MAAM,CAAC,WAAW,EAAEG,eAAeD,OAAO,CAAC;IACvD;IAEA,eAAeE,eAAkBC,EAAoB;QACnD,IAAI;YACF,OAAO,MAAMA;QACf,EAAE,OAAOC,aAAkB;YACzB,MAAM,IAAIN,MAAMM,YAAYC,OAAO,EAAE;gBAAEC,OAAOF;YAAY;QAC5D;IACF;IAEA;;;;;GAKC,GACD,SAASG,UACPC,SAAkB,EAClBC,cAAiC;QAKjC,mEAAmE;QACnE,wCAAwC;QACxC,IAAIC,SAAuB,EAAE;QAC7B,sEAAsE;QACtE,qDAAqD;QACrD,IAAIC;QAMJ,IAAIC,WAAW;QAEf,0EAA0E;QAC1E,2EAA2E;QAC3E,2BAA2B;QAC3B,MAAMC,aAAa,CAACC,KAAwBvB;YAC1C,IAAIoB,SAAS;gBACX,IAAI,EAAEnF,OAAO,EAAEuF,MAAM,EAAE,GAAGJ;gBAC1BA,UAAUnG;gBACV,IAAIsG,KAAKC,OAAOD;qBACXtF,QAAQ+D;YACf,OAAO;gBACL,MAAMyB,OAAO;oBAAEF;oBAAKvB;gBAAM;gBAC1B,IAAIiB,WAAWE,OAAOO,IAAI,CAACD;qBACtBN,MAAM,CAAC,EAAE,GAAGM;YACnB;QACF;QAEA,MAAME,WAAW,AAAC;YAChB,MAAMC,OAAO,MAAMjB,eAAe,IAAMO,eAAeI;YACvD,IAAI;gBACF,MAAO,CAACD,SAAU;oBAChB,IAAIF,OAAOU,MAAM,GAAG,GAAG;wBACrB,MAAMJ,OAAON,OAAOW,KAAK;wBACzB,IAAIL,KAAKF,GAAG,EAAE,MAAME,KAAKF,GAAG;wBAC5B,MAAME,KAAKzB,KAAK;oBAClB,OAAO;wBACL,wCAAwC;wBACxC,MAAM,IAAIhE,QAAW,CAACC,SAASuF;4BAC7BJ,UAAU;gCAAEnF;gCAASuF;4BAAO;wBAC9B;oBACF;gBACF;YACF,EAAE,OAAOO,GAAG;gBACV,IAAIA,MAAM1B,QAAQ;gBAClB,MAAM0B;YACR,SAAU;gBACR5B,QAAQ6B,eAAe,CAACJ;YAC1B;QACF;QACAD,SAASM,MAAM,GAAG;YAChBZ,WAAW;YACX,IAAID,SAASA,QAAQI,MAAM,CAACnB;YAC5B,OAAO;gBAAEL,OAAO/E;gBAAWiH,MAAM;YAAK;QACxC;QACA,OAAOP;IACT;IAEA,eAAeQ,sBACbC,OAAgC;QAEhC,OAAO;YACL,GAAGA,OAAO;YACVC,YACED,QAAQC,UAAU,IAAK,MAAMC,oBAAoBF,QAAQC,UAAU;YACrEE,UAAUH,QAAQG,QAAQ,IAAIC,KAAKC,SAAS,CAACL,QAAQG,QAAQ;YAC7DnJ,KAAKgJ,QAAQhJ,GAAG,IAAIqG,WAAW2C,QAAQhJ,GAAG;YAC1C8F,WAAWkD,QAAQlD,SAAS;QAC9B;IACF;IAEA,MAAMwD;QAGJC,YAAYC,aAAwC,CAAE;YACpD,IAAI,CAACC,cAAc,GAAGD;QACxB;QAEA,MAAME,OAAOV,OAAuB,EAAE;YACpC,MAAMzB,eAAe,UACnBR,QAAQ4C,aAAa,CACnB,IAAI,CAACF,cAAc,EACnB,MAAMV,sBAAsBC;QAGlC;QAEAY,uBAAuB;YA2CrB,MAAMC,eAAejC,UACnB,OACA,OAAOkC,WACL/C,QAAQgD,2BAA2B,CAAC,IAAI,CAACN,cAAc,EAAEK;YAE7D,OAAO,AAAC;gBACN,WAAW,MAAME,eAAeH,aAAc;oBAC5C,MAAMI,SAAS,IAAIC;oBACnB,KAAK,MAAM,EAAEC,QAAQ,EAAE,GAAGC,aAAa,IAAIJ,YAAYC,MAAM,CAAE;wBAC7D,IAAII;wBACJ,MAAMC,YAAYF,YAAYG,IAAI;wBAClC,OAAQD;4BACN,KAAK;gCACHD,QAAQ;oCACNE,MAAM;oCACNC,cAAc,IAAIC,aAAaL,YAAYI,YAAY;oCACvDE,cAAc,IAAID,aAAaL,YAAYM,YAAY;gCACzD;gCACA;4BACF,KAAK;gCACHL,QAAQ;oCACNE,MAAM;oCACNI,UAAU,IAAIF,aAAaL,YAAYO,QAAQ;gCACjD;gCACA;4BACF,KAAK;gCACHN,QAAQ;oCACNE,MAAM;oCACNC,cAAc,IAAIC,aAAaL,YAAYI,YAAY;oCACvDI,aAAa,IAAIH,aAAaL,YAAYQ,WAAW;gCACvD;gCACA;4BACF,KAAK;gCACHP,QAAQ;oCACNE,MAAM;oCACNI,UAAU,IAAIF,aAAaL,YAAYO,QAAQ;gCACjD;gCACA;4BACF,KAAK;gCACHN,QAAQ;oCACNE,MAAM;gCACR;gCACA;4BACF;gCACE,MAAMM,mBAA0BP;gCAChClD,UACEgD,aACA,IAAM,CAAC,oBAAoB,EAAES,iBAAiB,CAAC;wBAErD;wBACAZ,OAAOa,GAAG,CAACX,UAAUE;oBACvB;oBACA,MAAMU,6BAA6B,CAACC,aAAgC,CAAA;4BAClEL,UAAU,IAAIF,aAAaO,WAAWL,QAAQ;4BAC9CM,SAASD,WAAWC,OAAO;4BAC3BC,SAASF,WAAWE,OAAO;wBAC7B,CAAA;oBACA,MAAMF,aAAahB,YAAYgB,UAAU,GACrCD,2BAA2Bf,YAAYgB,UAAU,IACjDnJ;oBACJ,MAAM;wBACJoI;wBACAe;wBACAG,uBAAuB,IAAIV,aACzBT,YAAYmB,qBAAqB;wBAEnCC,kBAAkB,IAAIX,aAAaT,YAAYoB,gBAAgB;wBAC/DC,oBAAoB,IAAIZ,aACtBT,YAAYqB,kBAAkB;wBAEhCC,QAAQtB,YAAYsB,MAAM;wBAC1BC,aAAavB,YAAYuB,WAAW;oBACtC;gBACF;YACF;QACF;QAEAC,UAAUC,UAAkB,EAAE;YAC5B,MAAM5B,eAAejC,UACnB,MACA,OAAOkC,WACL/C,QAAQ2E,gBAAgB,CAAC,IAAI,CAACjC,cAAc,EAAEgC,YAAY3B;YAE9D,OAAOD;QACT;QAEA8B,0BAA0B;YACxB,MAAM9B,eAAejC,UACnB,OACA,OAAOkC,WACL/C,QAAQ6E,8BAA8B,CAAC,IAAI,CAACnC,cAAc,EAAEK;YAEhE,OAAOD;QACT;QAEAgC,YACEC,UAA+B,EACM;YACrC,OAAO/E,QAAQgF,kBAAkB,CAAC,IAAI,CAACtC,cAAc,EAAEqC;QACzD;QAEAE,kBAAkBC,QAAgB,EAA0B;YAC1D,OAAOlF,QAAQmF,wBAAwB,CAAC,IAAI,CAACzC,cAAc,EAAEwC;QAC/D;QAEAE,sBAAsB;YACpB,MAAMtC,eAAejC,UACnB,MACA,OAAOkC,WACL/C,QAAQqF,0BAA0B,CAAC,IAAI,CAAC3C,cAAc,EAAEK;YAE5D,OAAOD;QACT;IACF;IAEA,MAAMY;QAGJlB,YAAY8C,cAA0C,CAAE;YACtD,IAAI,CAACC,eAAe,GAAGD;QACzB;QAEA,MAAME,cAAyD;YAC7D,OAAO,MAAMhF,eAAe,IAC1BR,QAAQyF,mBAAmB,CAAC,IAAI,CAACF,eAAe;QAEpD;QAEA,MAAMG,gBAAqE;YACzE,MAAMC,qBAAqB9E,UACzB,OACA,OAAOkC,WACL/C,QAAQ4F,8BAA8B,CACpC,MAAM,IAAI,CAACL,eAAe,EAC1BxC;YAGN,MAAM4C,mBAAmBE,IAAI;YAC7B,OAAOF;QACT;QAEA,MAAMG,cACJC,aAAsB,EAC+B;YACrD,MAAMC,qBAAqBnF,UACzB,OACA,OAAOkC,WACL/C,QAAQiG,8BAA8B,CACpC,MAAM,IAAI,CAACV,eAAe,EAC1BQ,eACAhD;YAGN,MAAMiD,mBAAmBH,IAAI;YAC7B,OAAOG;QACT;IACF;IAEA,eAAe7D,oBACbD,UAA8B;YAW1BA,gCAAAA;QATJ,IAAIgE,yBAAyBhE;QAE7BgE,uBAAuBC,eAAe,GACpC,OAAMjE,WAAWiE,eAAe,oBAA1BjE,WAAWiE,eAAe,MAA1BjE;QAER,iFAAiF;QACjFgE,uBAAuBE,aAAa,GAAG,CAAC;QACxCF,uBAAuBG,OAAO,GAAGnE,WAAWmE,OAAO,IAAI,CAAC;QAExD,KAAInE,2BAAAA,WAAWoE,YAAY,sBAAvBpE,iCAAAA,yBAAyBqE,KAAK,qBAA9BrE,+BAAgCsE,KAAK,EAAE;gBACJtE;YAArCuE,sCAAqCvE,kCAAAA,WAAWoE,YAAY,CAACC,KAAK,qBAA7BrE,gCAA+BsE,KAAK;QAC3E;QAEAN,uBAAuBQ,iBAAiB,GACtCR,uBAAuBQ,iBAAiB,GACpCtH,OAAOuH,WAAW,CAChBvH,OAAOO,OAAO,CAAMuG,uBAAuBQ,iBAAiB,EAAErJ,GAAG,CAC/D,CAAC,CAACuJ,KAAKpI,OAAO,GAAK;gBACjBoI;gBACA;oBACE,GAAGpI,MAAM;oBACTqI,WACE,OAAOrI,OAAOqI,SAAS,KAAK,WACxBrI,OAAOqI,SAAS,GAChBzH,OAAOO,OAAO,CAACnB,OAAOqI,SAAS,EAAExJ,GAAG,CAAC,CAAC,CAACyJ,KAAKjH,MAAM,GAAK;4BACrDiH;4BACAjH;yBACD;gBACT;aACD,KAGL/E;QAEN,OAAOuH,KAAKC,SAAS,CAAC4D,wBAAwB,MAAM;IACtD;IAEA,SAASO,qCACPM,cAAyC;QAEzC,KAAK,MAAM,CAACC,MAAMC,KAAK,IAAI7H,OAAOO,OAAO,CAACoH,gBAAiB;YACzD,MAAMG,cAAc1K,MAAMC,OAAO,CAACwK,QAAQA,OAAOA,KAAKE,OAAO;YAC7D,KAAK,MAAMC,cAAcF,YAAa;gBACpC,IACE,OAAOE,eAAe,YACtB,CAACvO,kBAAkBuO,YAAY/E,KAAKgF,KAAK,CAAChF,KAAKC,SAAS,CAAC8E,eACzD;oBACA,MAAM,IAAIhH,MACR,CAAC,OAAO,EAAEgH,WAAWE,MAAM,CAAC,YAAY,EAAEN,KAAK,yGAAyG,CAAC;gBAE7J;YACF;QACF;IACF;IAEA,eAAeO,cACbtF,OAAuB,EACvBuF,kBAAsC;QAEtC,OAAO,IAAIjF,YACT,MAAMvC,QAAQyH,UAAU,CACtB,MAAMzF,sBAAsBC,UAC5BuF,sBAAsB,CAAC;IAG7B;IAEA,OAAOD;AACT;AAEA,eAAe9J,SAASiK,aAAa,EAAE;IACrC,IAAI1M,cAAc;QAChB,OAAOA;IACT;IAEA,IAAIqB,WAAW,EAAE;IACjB,KAAK,IAAIsL,OAAO;QAAC;QAAyB;KAAqB,CAAE;QAC/D,IAAI;YACF,IAAIC,UAAUD;YAEd,IAAID,YAAY;gBACd,yDAAyD;gBACzDE,UAAU1P,KAAKgF,IAAI,CAACwK,YAAYC,KAAK;YACvC;YACA,IAAIpK,WAAW,MAAM,MAAM,CAACqK;YAC5B,IAAID,QAAQ,sBAAsB;gBAChCpK,WAAW,MAAMA,SAASsK,OAAO;YACnC;YACAxO,QAAQ;YAER,mEAAmE;YACnE,yCAAyC;YACzC2B,eAAe;gBACb8M,QAAQ;gBACRjB,WAAUkB,GAAW,EAAE9F,OAAY;oBACjC,oHAAoH;oBACpH,OAAO1E,CAAAA,4BAAAA,SAAUsJ,SAAS,IACtBtJ,SAASsJ,SAAS,CAACkB,IAAIC,QAAQ,IAAI/F,WACnCpG,QAAQC,OAAO,CAACyB,SAAS0K,aAAa,CAACF,IAAIC,QAAQ,IAAI/F;gBAC7D;gBACAgG,eAAcF,GAAW,EAAE9F,OAAY;oBACrC,OAAO1E,SAAS0K,aAAa,CAACF,IAAIC,QAAQ,IAAI/F;gBAChD;gBACAiG,QAAOH,GAAW,EAAE9F,OAAY;oBAC9B,OAAO1E,CAAAA,4BAAAA,SAAU2K,MAAM,IACnB3K,SAAS2K,MAAM,CAACH,IAAIC,QAAQ,IAAI/F,WAChCpG,QAAQC,OAAO,CAACyB,SAAS4K,UAAU,CAACJ,IAAIC,QAAQ,IAAI/F;gBAC1D;gBACAkG,YAAWJ,GAAW,EAAE9F,OAAY;oBAClC,OAAO1E,SAAS4K,UAAU,CAACJ,IAAIC,QAAQ,IAAI/F;gBAC7C;gBACAoF,OAAMU,GAAW,EAAE9F,OAAY;oBAC7B,OAAO1E,CAAAA,4BAAAA,SAAU8J,KAAK,IAClB9J,SAAS8J,KAAK,CAACU,IAAIC,QAAQ,IAAI/F,WAC/BpG,QAAQC,OAAO,CAACyB,SAAS6K,SAAS,CAACL,IAAIC,QAAQ,IAAI/F;gBACzD;gBACAmG,WAAUL,GAAW,EAAE9F,OAAY;oBACjC,MAAMoG,SAAS9K,SAAS6K,SAAS,CAACL,IAAIC,QAAQ,IAAI/F;oBAClD,OAAOoG;gBACT;gBACAC;oBACE,OAAOxN;gBACT;gBACAyL,OAAO;oBACLgC,YAAY;wBACVhQ,IAAI6D,KAAK,CAAC;oBACZ;oBACA6G,aAAa;wBACXuF,QAAQ,CACNC,YACAC,SACAC,gBACAC,gBACAC;4BAEA,OAAOtL,SAASuL,iBAAiB,CAC/BL,YACAC,SACAC,gBACAC,gBACAC;wBAEJ;wBACAE,KAAK,CACHN,YACAC,SACAC,gBACAC;4BAEA,OAAOrL,SAASyL,cAAc,CAC5BP,YACAC,SACAC,gBACAC;wBAEJ;oBACF;gBACF;gBACAK,KAAK;oBACHC,SAAS,CAACnB,KAAa9F,UACrB1E,SAAS4L,UAAU,CAACpB,KAAKqB,cAAcnH;oBACzCoH,aAAa,CAACtB,KAAa9F,UACzB1E,SAAS+L,cAAc,CAACvB,KAAKqB,cAAcnH;gBAC/C;YACF;YACA,OAAOjH;QACT,EAAE,OAAO4G,GAAQ;YACf,8DAA8D;YAC9D,IAAI8F,YAAY;gBACd,IAAI9F,CAAAA,qBAAAA,EAAG2H,IAAI,MAAK,wBAAwB;oBACtClN,SAASkF,IAAI,CAAC,CAAC,kBAAkB,EAAEoG,IAAI,0BAA0B,CAAC;gBACpE,OAAO;oBACLtL,SAASkF,IAAI,CACX,CAAC,kBAAkB,EAAEoG,IAAI,yBAAyB,EAAE/F,EAAEjB,OAAO,IAAIiB,EAAE,CAAC;gBAExE;YACF;QACF;IACF;IAEA,MAAMvF;AACR;AAEA,SAASC,WAAWoL,UAAmB;IACrC,IAAI3M,gBAAgB;QAClB,OAAOA;IACT;IAEA,MAAMyO,iBAAiB,CAAC,CAAC/O,uCACrB2C,QAAQ3C,wCACR;IACJ,IAAI8C;IACJ,IAAIlB,WAAkB,EAAE;IAExB,KAAK,MAAMpC,UAAUG,QAAS;QAC5B,IAAI;YACFmD,WAAWH,QAAQ,CAAC,0BAA0B,EAAEnD,OAAOqD,eAAe,CAAC,KAAK,CAAC;YAC7EjE,QAAQ;YACR;QACF,EAAE,OAAOuI,GAAG,CAAC;IACf;IAEA,IAAI,CAACrE,UAAU;QACb,KAAK,MAAMtD,UAAUG,QAAS;YAC5B,IAAIuN,MAAMD,aACNxP,KAAKgF,IAAI,CACPwK,YACA,CAAC,UAAU,EAAEzN,OAAOqD,eAAe,CAAC,CAAC,EACrC,CAAC,SAAS,EAAErD,OAAOqD,eAAe,CAAC,KAAK,CAAC,IAE3C,CAAC,UAAU,EAAErD,OAAOqD,eAAe,CAAC,CAAC;YACzC,IAAI;gBACFC,WAAWH,QAAQuK;gBACnB,IAAI,CAACD,YAAY;oBACfhN,qBAAqB0C,QAAQ,CAAC,EAAEuK,IAAI,aAAa,CAAC;gBACpD;gBACA;YACF,EAAE,OAAO/F,GAAQ;gBACf,IAAIA,CAAAA,qBAAAA,EAAG2H,IAAI,MAAK,oBAAoB;oBAClClN,SAASkF,IAAI,CAAC,CAAC,kBAAkB,EAAEoG,IAAI,0BAA0B,CAAC;gBACpE,OAAO;oBACLtL,SAASkF,IAAI,CACX,CAAC,kBAAkB,EAAEoG,IAAI,yBAAyB,EAAE/F,EAAEjB,OAAO,IAAIiB,EAAE,CAAC;gBAExE;gBACA/G,kCAAkC+G,CAAAA,qBAAAA,EAAG2H,IAAI,KAAI;YAC/C;QACF;IACF;IAEA,IAAIhM,UAAU;QACZ,+EAA+E;QAC/E,kGAAkG;QAClG,gFAAgF;QAChF,IAAI,CAAClC,4BAA4B;QAC/B,6FAA6F;QAC7F;;;;OAIC,GACH;QAEAN,iBAAiB;YACf+M,QAAQ;YACRjB,WAAUkB,GAAW,EAAE9F,OAAY;oBAO7BA;gBANJ,MAAMwH,WACJ,OAAO1B,QAAQjN,aACf,OAAOiN,QAAQ,YACf,CAAC2B,OAAOC,QAAQ,CAAC5B;gBACnB9F,UAAUA,WAAW,CAAC;gBAEtB,IAAIA,4BAAAA,eAAAA,QAAS2H,GAAG,qBAAZ3H,aAAc4H,MAAM,EAAE;oBACxB5H,QAAQ2H,GAAG,CAACC,MAAM,CAACC,MAAM,GAAG7H,QAAQ2H,GAAG,CAACC,MAAM,CAACC,MAAM,IAAI;gBAC3D;gBAEA,OAAOvM,SAASsJ,SAAS,CACvB4C,WAAWpH,KAAKC,SAAS,CAACyF,OAAOA,KACjC0B,UACAM,SAAS9H;YAEb;YAEAgG,eAAcF,GAAW,EAAE9F,OAAY;oBAajCA;gBAZJ,IAAI,OAAO8F,QAAQjN,WAAW;oBAC5B,MAAM,IAAIsF,MACR;gBAEJ,OAAO,IAAIsJ,OAAOC,QAAQ,CAAC5B,MAAM;oBAC/B,MAAM,IAAI3H,MACR;gBAEJ;gBACA,MAAMqJ,WAAW,OAAO1B,QAAQ;gBAChC9F,UAAUA,WAAW,CAAC;gBAEtB,IAAIA,4BAAAA,eAAAA,QAAS2H,GAAG,qBAAZ3H,aAAc4H,MAAM,EAAE;oBACxB5H,QAAQ2H,GAAG,CAACC,MAAM,CAACC,MAAM,GAAG7H,QAAQ2H,GAAG,CAACC,MAAM,CAACC,MAAM,IAAI;gBAC3D;gBAEA,OAAOvM,SAAS0K,aAAa,CAC3BwB,WAAWpH,KAAKC,SAAS,CAACyF,OAAOA,KACjC0B,UACAM,SAAS9H;YAEb;YAEAiG,QAAOH,GAAW,EAAE9F,OAAY;gBAC9B,OAAO1E,SAAS2K,MAAM,CAAC6B,SAAShC,MAAMgC,SAAS9H,WAAW,CAAC;YAC7D;YAEAkG,YAAWJ,GAAW,EAAE9F,OAAY;gBAClC,OAAO1E,SAAS4K,UAAU,CAAC4B,SAAShC,MAAMgC,SAAS9H,WAAW,CAAC;YACjE;YAEAoF,OAAMU,GAAW,EAAE9F,OAAY;gBAC7B,OAAO1E,SAAS8J,KAAK,CAACU,KAAKgC,SAAS9H,WAAW,CAAC;YAClD;YAEAqG,iBAAiB/K,SAAS+K,eAAe;YACzC0B,2BAA2BzM,SAASyM,yBAAyB;YAC7DC,yBAAyB1M,SAAS0M,uBAAuB;YACzDC,kBAAkB3M,SAAS2M,gBAAgB;YAC3CC,sBAAsB5M,SAAS4M,oBAAoB;YACnDC,uBAAuB7M,SAAS6M,qBAAqB;YACrD7D,OAAO;gBACL8D,WAAW,CAACpI;oBACViI;oBACA,MAAMI,MAAM,AAACd,CAAAA,kBAAkBjM,QAAO,EAAG8M,SAAS,CAACpI;oBAEnD,OAAOqI;gBACT;gBACA/B,YAAY,CAACtG,UAAU,CAAC,CAAC,EAAEwG;oBACzByB;oBACA,MAAMI,MAAM,AAACd,CAAAA,kBAAkBjM,QAAO,EAAGgN,eAAe,CACtDR,SAAS;wBAAES,OAAO;wBAAM,GAAGvI,OAAO;oBAAC,IACnCwG;oBAEF,OAAO6B;gBACT;gBACAG,kBAAkB,CAACC,cACjBnN,SAASkN,gBAAgB,CAACC;gBAC5BzH,aAAa;oBACXuF,QAAQ,CACNC,YACAC,SACAC,gBACAC,gBACAnI;wBAEA,OAAO,AAAC+I,CAAAA,kBAAkBjM,QAAO,EAAGuL,iBAAiB,CACnDL,YACAC,SACAC,gBACAC,gBACAnI;oBAEJ;oBACAsI,KAAK,CACHN,YACAC,SACAC,gBACAC;wBAEA,OAAO,AAACY,CAAAA,kBAAkBjM,QAAO,EAAGyL,cAAc,CAChDP,YACAC,SACAC,gBACAC;oBAEJ;gBACF;gBACArB,eAAexH,aAAayJ,kBAAkBjM,UAAU;YAC1D;YACA0L,KAAK;gBACHC,SAAS,CAACnB,KAAa9F,UACrB1E,SAAS4L,UAAU,CAACpB,KAAKgC,SAASX,cAAcnH;gBAClDoH,aAAa,CAACtB,KAAa9F,UACzB1E,SAAS+L,cAAc,CAACvB,KAAKgC,SAASX,cAAcnH;YACxD;QACF;QACA,OAAOlH;IACT;IAEA,MAAMsB;AACR;AAEA,2DAA2D;AAC3D,0CAA0C;AAC1C,SAAS+M,cAAcnH,UAAe,CAAC,CAAC;IACtC,MAAMqI,MAAM;QACV,GAAGrI,OAAO;QACV0I,aAAa1I,QAAQ0I,WAAW,IAAI;QACpCC,KAAK3I,QAAQ2I,GAAG,IAAI;QACpBvD,OAAOpF,QAAQoF,KAAK,IAAI;YACtBwD,6BAA6B;YAC7BC,sBAAsB;QACxB;IACF;IAEA,OAAOR;AACT;AAEA,SAASP,SAASgB,CAAM;IACtB,OAAOrB,OAAOsB,IAAI,CAAC3I,KAAKC,SAAS,CAACyI;AACpC;AAEA,OAAO,eAAejD;IACpB,IAAIvK,WAAW,MAAM/B;IACrB,OAAO+B,SAASuK,MAAM;AACxB;AAEA,OAAO,eAAejB,UAAUkB,GAAW,EAAE9F,OAAa;IACxD,IAAI1E,WAAW,MAAM/B;IACrB,OAAO+B,SAASsJ,SAAS,CAACkB,KAAK9F;AACjC;AAEA,OAAO,SAASgG,cAAcF,GAAW,EAAE9F,OAAa;IACtD,IAAI1E,WAAWQ;IACf,OAAOR,SAAS0K,aAAa,CAACF,KAAK9F;AACrC;AAEA,OAAO,eAAeiG,OAAOH,GAAW,EAAE9F,OAAY;IACpD,IAAI1E,WAAW,MAAM/B;IACrB,OAAO+B,SAAS2K,MAAM,CAACH,KAAK9F;AAC9B;AAEA,OAAO,SAASkG,WAAWJ,GAAW,EAAE9F,OAAY;IAClD,IAAI1E,WAAWQ;IACf,OAAOR,SAAS4K,UAAU,CAACJ,KAAK9F;AAClC;AAEA,OAAO,eAAeoF,MAAMU,GAAW,EAAE9F,OAAY;IACnD,IAAI1E,WAAW,MAAM/B;IACrB,IAAIyP,gBAAgBzS,iBAAiByJ;IACrC,OAAO1E,SACJ8J,KAAK,CAACU,KAAKkD,eACXhN,IAAI,CAAC,CAACoK,SAAgBhG,KAAKgF,KAAK,CAACgB;AACtC;AAEA,OAAO,SAAS6C;QASJ3N;IARV,IAAIA;IACJ,IAAI;QACFA,WAAWjB;IACb,EAAE,OAAOsF,GAAG;IACV,sEAAsE;IACxE;IAEA,OAAO;QACLuJ,MAAM,EAAE5N,6BAAAA,4BAAAA,SAAU+K,eAAe,qBAAzB/K,+BAAAA;IACV;AACF;AAEA;;;CAGC,GACD,OAAO,MAAMyM,4BAA4B,CAACoB;IACxC,IAAI,CAACjQ,oBAAoB;QACvB,6CAA6C;QAC7C,IAAIoC,WAAWjB;QACfnB,qBAAqBoC,SAASyM,yBAAyB,CAACoB;IAC1D;AACF,EAAC;AAED;;;;;CAKC,GACD,OAAO,MAAMlB,mBAAmB;IAC9B,IAAI;QACF,IAAI,CAAC9O,2BAA2B;YAC9B,IAAImC,WAAWjB;YACflB,4BAA4BmC,SAAS2M,gBAAgB;QACvD;IACF,EAAE,OAAOtK,GAAG;IACV,sEAAsE;IACxE;AACF,EAAC;AAED;;;;;CAKC,GACD,OAAO,MAAMuK,uBAAuB,AAAC,CAAA;IACnC,IAAIkB,UAAU;IACd,OAAO;QACL,IAAI,CAACA,SAAS;YACZA,UAAU;YACV,IAAI;gBACF,IAAI9N,WAAWjB;gBACf,IAAIlB,2BAA2B;oBAC7BmC,SAAS4M,oBAAoB,CAAC/O;gBAChC;YACF,EAAE,OAAOwG,GAAG;YACV,sEAAsE;YACxE;QACF;IACF;AACF,CAAA,IAAI;AAEJ;;;;;;;;CAQC,GACD,OAAO,MAAMqI,0BAA0B,AAAC,CAAA;IACtC,IAAIoB,UAAU;IACd,OAAO;QACL,IAAI,CAACA,SAAS;YACZA,UAAU;YACV,IAAI;gBACF,IAAI9N,WAAWjB;gBACf,IAAInB,oBAAoB;oBACtBoC,SAAS0M,uBAAuB,CAAC9O;gBACnC;YACF,EAAE,OAAOyG,GAAG;YACV,sEAAsE;YACxE;QACF;IACF;AACF,CAAA,IAAI;AAEJ,OAAO,MAAMwI,wBAAwB,AAAC,CAAA;IACpC,IAAIiB,UAAU;IACd,OAAO;QACL,IAAI,CAACA,SAAS;YACZA,UAAU;YACV,IAAI;gBACF,IAAI9N,WAAWjB;gBACf,IAAIjB,4BAA4B;oBAC9BkC,SAAS6M,qBAAqB,CAAC/O;gBACjC;YACF,EAAE,OAAOuG,GAAG;YACV,sEAAsE;YACxE;QACF;IACF;AACF,CAAA,IAAI"}