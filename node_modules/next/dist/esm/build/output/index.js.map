{"version": 3, "sources": ["../../../src/build/output/index.ts"], "names": ["bold", "red", "yellow", "stripAnsi", "textTable", "createStore", "formatWebpackMessages", "store", "consoleStore", "COMPILER_NAMES", "startedDevelopmentServer", "appUrl", "bindAddr", "setState", "formatAmpMessages", "amp", "output", "messages", "chalkError", "ampError", "page", "error", "push", "message", "specUrl", "chalk<PERSON>arn", "ampWarn", "warn", "errors", "warnings", "dev<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "err", "code", "filter", "length", "index", "align", "stringLength", "str", "buildStore", "client", "server", "edgeServer", "buildWasDone", "clientWasLoading", "serverWasLoading", "edgeServerWasLoading", "subscribe", "state", "trigger", "getState", "loading", "bootstrap", "partialState", "typeChecking", "totalModulesCount", "hasEdgeServer", "concat", "ampValidation", "Object", "keys", "k", "sort", "reduce", "a", "c", "newAmp", "watchCompilers", "tapCompiler", "key", "compiler", "onEvent", "hooks", "invalid", "tap", "done", "stats", "to<PERSON><PERSON>", "preset", "moduleTrace", "hasErrors", "hasWarnings", "compilation", "modules", "size", "status", "undefined", "reportTrigger"], "mappings": "AAAA,SAASA,IAAI,EAAEC,GAAG,EAAEC,MAAM,QAAQ,uBAAsB;AACxD,OAAOC,eAAe,gCAA+B;AACrD,OAAOC,eAAe,gCAA+B;AACrD,OAAOC,iBAAiB,8BAA6B;AACrD,OAAOC,2BAA2B,yDAAwD;AAC1F,SAASC,SAASC,YAAY,QAAQ,UAAS;AAG/C,SAASC,cAAc,QAAQ,6BAA4B;AAG3D,OAAO,SAASC,yBAAyBC,MAAc,EAAEC,QAAgB;IACvEJ,aAAaK,QAAQ,CAAC;QAAEF;QAAQC;IAAS;AAC3C;AAgCA,OAAO,SAASE,kBAAkBC,GAAkB;IAClD,IAAIC,SAAShB,KAAK,oBAAoB;IACtC,IAAIiB,WAAuB,EAAE;IAE7B,MAAMC,aAAajB,IAAI;IACvB,SAASkB,SAASC,IAAY,EAAEC,KAAgB;QAC9CJ,SAASK,IAAI,CAAC;YAACF;YAAMF;YAAYG,MAAME,OAAO;YAAEF,MAAMG,OAAO,IAAI;SAAG;IACtE;IAEA,MAAMC,YAAYvB,OAAO;IACzB,SAASwB,QAAQN,IAAY,EAAEO,IAAe;QAC5CV,SAASK,IAAI,CAAC;YAACF;YAAMK;YAAWE,KAAKJ,OAAO;YAAEI,KAAKH,OAAO,IAAI;SAAG;IACnE;IAEA,IAAK,MAAMJ,QAAQL,IAAK;QACtB,IAAI,EAAEa,MAAM,EAAEC,QAAQ,EAAE,GAAGd,GAAG,CAACK,KAAK;QAEpC,MAAMU,gBAAgB,CAACC,MAAmBA,IAAIC,IAAI,KAAK;QACvDJ,SAASA,OAAOK,MAAM,CAACH;QACvBD,WAAWA,SAASI,MAAM,CAACH;QAC3B,IAAI,CAAEF,CAAAA,OAAOM,MAAM,IAAIL,SAASK,MAAM,AAAD,GAAI;YAEvC;QACF;QAEA,IAAIN,OAAOM,MAAM,EAAE;YACjBf,SAASC,MAAMQ,MAAM,CAAC,EAAE;YACxB,IAAK,IAAIO,QAAQ,GAAGA,QAAQP,OAAOM,MAAM,EAAE,EAAEC,MAAO;gBAClDhB,SAAS,IAAIS,MAAM,CAACO,MAAM;YAC5B;QACF;QACA,IAAIN,SAASK,MAAM,EAAE;YACnBR,QAAQE,OAAOM,MAAM,GAAG,KAAKd,MAAMS,QAAQ,CAAC,EAAE;YAC9C,IAAK,IAAIM,QAAQ,GAAGA,QAAQN,SAASK,MAAM,EAAE,EAAEC,MAAO;gBACpDT,QAAQ,IAAIG,QAAQ,CAACM,MAAM;YAC7B;QACF;QACAlB,SAASK,IAAI,CAAC;YAAC;YAAI;YAAI;YAAI;SAAG;IAChC;IAEA,IAAI,CAACL,SAASiB,MAAM,EAAE;QACpB,OAAO;IACT;IAEAlB,UAAUZ,UAAUa,UAAU;QAC5BmB,OAAO;YAAC;YAAK;YAAK;YAAK;SAAI;QAC3BC,cAAaC,GAAW;YACtB,OAAOnC,UAAUmC,KAAKJ,MAAM;QAC9B;IACF;IAEA,OAAOlB;AACT;AAEA,MAAMuB,aAAalC,YAA8B;IAC/C,iCAAiC;IACjCmC,QAAQ,CAAC;IACT,iCAAiC;IACjCC,QAAQ,CAAC;IACT,iCAAiC;IACjCC,YAAY,CAAC;AACf;AACA,IAAIC,eAAe;AACnB,IAAIC,mBAAmB;AACvB,IAAIC,mBAAmB;AACvB,IAAIC,uBAAuB;AAE3BP,WAAWQ,SAAS,CAAC,CAACC;IACpB,MAAM,EAAEjC,GAAG,EAAEyB,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEO,OAAO,EAAE,GAAGD;IAErD,MAAM,EAAErC,MAAM,EAAE,GAAGH,aAAa0C,QAAQ;IAExC,IAAIV,OAAOW,OAAO,IAAIV,OAAOU,OAAO,KAAIT,8BAAAA,WAAYS,OAAO,GAAE;QAC3D3C,aAAaK,QAAQ,CACnB;YACEuC,WAAW;YACXzC,QAAQA;YACR,wEAAwE;YACxEwC,SAAS;YACTF;QACF,GACA;QAEFL,mBAAmB,AAAC,CAACD,gBAAgBC,oBAAqBJ,OAAOW,OAAO;QACxEN,mBAAmB,AAAC,CAACF,gBAAgBE,oBAAqBJ,OAAOU,OAAO;QACxEL,uBACE,AAAC,CAACH,gBAAgBG,wBAAyBJ,WAAWS,OAAO;QAC/DR,eAAe;QACf;IACF;IAEAA,eAAe;IAEf,IAAIU,eAAqC;QACvCD,WAAW;QACXzC,QAAQA;QACRwC,SAAS;QACTG,cAAc;QACdC,mBACE,AAACX,CAAAA,mBAAmBJ,OAAOe,iBAAiB,GAAG,CAAA,IAC9CV,CAAAA,mBAAmBJ,OAAOc,iBAAiB,GAAG,CAAA,IAC9CT,CAAAA,uBAAuBJ,CAAAA,8BAAAA,WAAYa,iBAAiB,KAAI,IAAI,CAAA;QAC/DC,eAAe,CAAC,CAACd;IACnB;IACA,IAAIF,OAAOZ,MAAM,IAAIgB,kBAAkB;QACrC,0BAA0B;QAC1BpC,aAAaK,QAAQ,CACnB;YACE,GAAGwC,YAAY;YACfzB,QAAQY,OAAOZ,MAAM;YACrBC,UAAU;QACZ,GACA;IAEJ,OAAO,IAAIY,OAAOb,MAAM,IAAIiB,kBAAkB;QAC5CrC,aAAaK,QAAQ,CACnB;YACE,GAAGwC,YAAY;YACfzB,QAAQa,OAAOb,MAAM;YACrBC,UAAU;QACZ,GACA;IAEJ,OAAO,IAAIa,WAAWd,MAAM,IAAIkB,sBAAsB;QACpDtC,aAAaK,QAAQ,CACnB;YACE,GAAGwC,YAAY;YACfzB,QAAQc,WAAWd,MAAM;YACzBC,UAAU;QACZ,GACA;IAEJ,OAAO;QACL,iCAAiC;QACjC,MAAMA,WAAW;eACXW,OAAOX,QAAQ,IAAI,EAAE;eACrBY,OAAOZ,QAAQ,IAAI,EAAE;eACrBa,WAAWb,QAAQ,IAAI,EAAE;SAC9B,CAAC4B,MAAM,CAAC3C,kBAAkBC,QAAQ,EAAE;QAErCP,aAAaK,QAAQ,CACnB;YACE,GAAGwC,YAAY;YACfzB,QAAQ;YACRC,UAAUA,SAASK,MAAM,KAAK,IAAI,OAAOL;QAC3C,GACA;IAEJ;AACF;AAEA,OAAO,SAAS6B,cACdtC,IAAY,EACZQ,MAAmB,EACnBC,QAAqB;IAErB,MAAM,EAAEd,GAAG,EAAE,GAAGwB,WAAWW,QAAQ;IACnC,IAAI,CAAEtB,CAAAA,OAAOM,MAAM,IAAIL,SAASK,MAAM,AAAD,GAAI;QACvCK,WAAW1B,QAAQ,CAAC;YAClBE,KAAK4C,OAAOC,IAAI,CAAC7C,KACdkB,MAAM,CAAC,CAAC4B,IAAMA,MAAMzC,MACpB0C,IAAI,EACL,wCAAwC;aACvCC,MAAM,CAAC,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAGlD,GAAG,CAACkD,EAAE,EAAGD,CAAAA,GAAI,CAAC;QAC7C;QACA;IACF;IAEA,MAAME,SAAwB;QAAE,GAAGnD,GAAG;QAAE,CAACK,KAAK,EAAE;YAAEQ;YAAQC;QAAS;IAAE;IACrEU,WAAW1B,QAAQ,CAAC;QAClBE,KAAK4C,OAAOC,IAAI,CAACM,QACdJ,IAAI,EACL,wCAAwC;SACvCC,MAAM,CAAC,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAGC,MAAM,CAACD,EAAE,EAAGD,CAAAA,GAAI,CAAC;IAChD;AACF;AAEA,OAAO,SAASG,eACd3B,MAAwB,EACxBC,MAAwB,EACxBC,UAA4B;IAE5BH,WAAW1B,QAAQ,CAAC;QAClB2B,QAAQ;YAAEW,SAAS;QAAK;QACxBV,QAAQ;YAAEU,SAAS;QAAK;QACxBT,YAAY;YAAES,SAAS;QAAK;QAC5BF,SAAS;IACX;IAEA,SAASmB,YACPC,GAAuB,EACvBC,QAA0B,EAC1BC,OAAwC;QAExCD,SAASE,KAAK,CAACC,OAAO,CAACC,GAAG,CAAC,CAAC,cAAc,EAAEL,IAAI,CAAC,EAAE;YACjDE,QAAQ;gBAAEpB,SAAS;YAAK;QAC1B;QAEAmB,SAASE,KAAK,CAACG,IAAI,CAACD,GAAG,CAAC,CAAC,WAAW,EAAEL,IAAI,CAAC,EAAE,CAACO;YAC5CrC,WAAW1B,QAAQ,CAAC;gBAAEE,KAAK,CAAC;YAAE;YAE9B,MAAM,EAAEa,MAAM,EAAEC,QAAQ,EAAE,GAAGvB,sBAC3BsE,MAAMC,MAAM,CAAC;gBACXC,QAAQ;gBACRC,aAAa;YACf;YAGF,MAAMC,YAAY,CAAC,EAACpD,0BAAAA,OAAQM,MAAM;YAClC,MAAM+C,cAAc,CAAC,EAACpD,4BAAAA,SAAUK,MAAM;YAEtCqC,QAAQ;gBACNpB,SAAS;gBACTI,mBAAmBqB,MAAMM,WAAW,CAACC,OAAO,CAACC,IAAI;gBACjDxD,QAAQoD,YAAYpD,SAAS;gBAC7BC,UAAUoD,cAAcpD,WAAW;YACrC;QACF;IACF;IAEAuC,YAAY3D,eAAe+B,MAAM,EAAEA,QAAQ,CAAC6C;QAC1C,IACE,CAACA,OAAOlC,OAAO,IACf,CAACZ,WAAWW,QAAQ,GAAGT,MAAM,CAACU,OAAO,IACrC,CAACZ,WAAWW,QAAQ,GAAGR,UAAU,CAACS,OAAO,IACzCkC,OAAO9B,iBAAiB,GAAG,GAC3B;YACAhB,WAAW1B,QAAQ,CAAC;gBAClB2B,QAAQ6C;gBACRpC,SAASqC;YACX;QACF,OAAO;YACL/C,WAAW1B,QAAQ,CAAC;gBAClB2B,QAAQ6C;YACV;QACF;IACF;IACAjB,YAAY3D,eAAegC,MAAM,EAAEA,QAAQ,CAAC4C;QAC1C,IACE,CAACA,OAAOlC,OAAO,IACf,CAACZ,WAAWW,QAAQ,GAAGV,MAAM,CAACW,OAAO,IACrC,CAACZ,WAAWW,QAAQ,GAAGR,UAAU,CAACS,OAAO,IACzCkC,OAAO9B,iBAAiB,GAAG,GAC3B;YACAhB,WAAW1B,QAAQ,CAAC;gBAClB4B,QAAQ4C;gBACRpC,SAASqC;YACX;QACF,OAAO;YACL/C,WAAW1B,QAAQ,CAAC;gBAClB4B,QAAQ4C;YACV;QACF;IACF;IACAjB,YAAY3D,eAAeiC,UAAU,EAAEA,YAAY,CAAC2C;QAClD,IACE,CAACA,OAAOlC,OAAO,IACf,CAACZ,WAAWW,QAAQ,GAAGV,MAAM,CAACW,OAAO,IACrC,CAACZ,WAAWW,QAAQ,GAAGT,MAAM,CAACU,OAAO,IACrCkC,OAAO9B,iBAAiB,GAAG,GAC3B;YACAhB,WAAW1B,QAAQ,CAAC;gBAClB6B,YAAY2C;gBACZpC,SAASqC;YACX;QACF,OAAO;YACL/C,WAAW1B,QAAQ,CAAC;gBAClB6B,YAAY2C;YACd;QACF;IACF;AACF;AAEA,OAAO,SAASE,cAActC,OAAe;IAC3CV,WAAW1B,QAAQ,CAAC;QAClBoC;IACF;AACF"}