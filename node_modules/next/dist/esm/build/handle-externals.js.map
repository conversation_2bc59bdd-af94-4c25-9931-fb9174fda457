{"version": 3, "sources": ["../../src/build/handle-externals.ts"], "names": ["WEBPACK_LAYERS", "defaultOverrides", "BARREL_OPTIMIZATION_PREFIX", "path", "NODE_BASE_ESM_RESOLVE_OPTIONS", "NODE_BASE_RESOLVE_OPTIONS", "NODE_ESM_RESOLVE_OPTIONS", "NODE_RESOLVE_OPTIONS", "isWebpackAppLayer", "isWebpackServerLayer", "reactPackagesRegex", "pathSeparators", "optionalEsmPart", "externalFileEnd", "nextDist", "externalPattern", "RegExp", "isResourceInPackages", "resource", "packageNames", "packageDirMapping", "some", "p", "has", "startsWith", "get", "sep", "includes", "join", "replace", "resolveExternal", "dir", "esmExternalsConfig", "context", "request", "isEsmRequested", "getResolve", "isLocalCallback", "baseResolveCheck", "esmResolveOptions", "nodeResolveOptions", "baseEsmResolveOptions", "baseResolveOptions", "esmExternals", "looseEsmExternals", "res", "isEsm", "preferEsmOptions", "preferEsm", "resolve", "err", "localRes", "baseRes", "baseIsEsm", "baseResolve", "makeExternalHandler", "config", "optOutBundlingPackageRegex", "resolvedExternalPackageDirs", "experimental", "handleExternals", "dependencyType", "layer", "isLocal", "posix", "isAbsolute", "process", "platform", "win32", "isApp<PERSON><PERSON>er", "test", "notExternalModules", "resolveNextExternal", "isExternal", "serverSideRendering", "isRelative", "fullRequest", "resolveResult", "undefined", "Error", "externalType", "transpilePackages", "Map", "pkg", "pkgRes", "set", "dirname", "shouldBeBundled", "bundlePagesExternals"], "mappings": "AAAA,SAASA,cAAc,QAAQ,mBAAkB;AAEjD,SAASC,gBAAgB,QAAQ,yBAAwB;AACzD,SAASC,0BAA0B,QAAQ,0BAAyB;AACpE,OAAOC,UAAU,gCAA+B;AAChD,SACEC,6BAA6B,EAC7BC,yBAAyB,EACzBC,wBAAwB,EACxBC,oBAAoB,QACf,mBAAkB;AACzB,SAASC,iBAAiB,EAAEC,oBAAoB,QAAQ,WAAU;AAElE,MAAMC,qBAAqB;AAE3B,MAAMC,iBAAiB;AACvB,MAAMC,kBAAkB,CAAC,EAAE,EAAED,eAAe,KAAK,EAAEA,eAAe,CAAC,CAAC;AACpE,MAAME,kBAAkB;AACxB,MAAMC,WAAW,CAAC,IAAI,EAAEH,eAAe,IAAI,CAAC;AAE5C,MAAMI,kBAAkB,IAAIC,OAC1B,CAAC,EAAEF,SAAS,EAAEF,gBAAgB,EAAE,EAAEC,gBAAgB,CAAC;AAGrD,OAAO,SAASI,qBACdC,QAAgB,EAChBC,YAAuB,EACvBC,iBAAuC;IAEvC,OAAOD,gCAAAA,aAAcE,IAAI,CAAC,CAACC,IACzBF,qBAAqBA,kBAAkBG,GAAG,CAACD,KACvCJ,SAASM,UAAU,CAACJ,kBAAkBK,GAAG,CAACH,KAAMnB,KAAKuB,GAAG,IACxDR,SAASS,QAAQ,CACfxB,KAAKuB,GAAG,GACNvB,KAAKyB,IAAI,CAAC,gBAAgBN,EAAEO,OAAO,CAAC,OAAO1B,KAAKuB,GAAG,KACnDvB,KAAKuB,GAAG;AAGpB;AAEA,OAAO,eAAeI,gBACpBC,GAAW,EACXC,kBAAsE,EACtEC,OAAe,EACfC,OAAe,EACfC,cAAuB,EACvBC,UAKsC,EACtCC,eAAsC,EACtCC,mBAAmB,IAAI,EACvBC,oBAAyBjC,wBAAwB,EACjDkC,qBAA0BjC,oBAAoB,EAC9CkC,wBAA6BrC,6BAA6B,EAC1DsC,qBAA0BrC,yBAAyB;IAEnD,MAAMsC,eAAe,CAAC,CAACX;IACvB,MAAMY,oBAAoBZ,uBAAuB;IAEjD,IAAIa,MAAqB;IACzB,IAAIC,QAAiB;IAErB,IAAIC,mBACFJ,gBAAgBR,iBAAiB;QAAC;QAAM;KAAM,GAAG;QAAC;KAAM;IAE1D,KAAK,MAAMa,aAAaD,iBAAkB;QACxC,MAAME,UAAUb,WACdY,YAAYT,oBAAoBC;QAGlC,6DAA6D;QAC7D,4DAA4D;QAC5D,SAAS;QACT,IAAI;YACD,CAACK,KAAKC,MAAM,GAAG,MAAMG,QAAQhB,SAASC;QACzC,EAAE,OAAOgB,KAAK;YACZL,MAAM;QACR;QAEA,IAAI,CAACA,KAAK;YACR;QACF;QAEA,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,CAACV,kBAAkBW,SAAS,CAACF,mBAAmB;YAClD;QACF;QAEA,IAAIP,iBAAiB;YACnB,OAAO;gBAAEc,UAAUd,gBAAgBQ;YAAK;QAC1C;QAEA,mEAAmE;QACnE,mEAAmE;QACnE,kEAAkE;QAClE,gEAAgE;QAChE,IAAIP,kBAAkB;YACpB,IAAIc;YACJ,IAAIC;YACJ,IAAI;gBACF,MAAMC,cAAclB,WAClBU,QAAQL,wBAAwBC;gBAEjC,CAACU,SAASC,UAAU,GAAG,MAAMC,YAAYvB,KAAKG;YACjD,EAAE,OAAOgB,KAAK;gBACZE,UAAU;gBACVC,YAAY;YACd;YAEA,8DAA8D;YAC9D,iEAAiE;YACjE,yBAAyB;YACzB,2EAA2E;YAC3E,wDAAwD;YACxD,IAAID,YAAYP,OAAOC,UAAUO,WAAW;gBAC1CR,MAAM;gBACN;YACF;QACF;QACA;IACF;IACA,OAAO;QAAEA;QAAKC;IAAM;AACtB;AAEA,OAAO,SAASS,oBAAoB,EAClCC,MAAM,EACNC,0BAA0B,EAC1B1B,GAAG,EAKJ;QAE2ByB;IAD1B,IAAIE;IACJ,MAAMd,oBAAoBY,EAAAA,uBAAAA,OAAOG,YAAY,qBAAnBH,qBAAqBb,YAAY,MAAK;IAEhE,OAAO,eAAeiB,gBACpB3B,OAAe,EACfC,OAAe,EACf2B,cAAsB,EACtBC,KAA8B,EAC9B1B,UAKsC;QAEtC,iEAAiE;QACjE,kBAAkB;QAClB,MAAM2B,UACJ7B,QAAQV,UAAU,CAAC,QACnB,yDAAyD;QACzD,uBAAuB;QACvBrB,KAAK6D,KAAK,CAACC,UAAU,CAAC/B,YACtB,8DAA8D;QAC9D,kBAAkB;QACjBgC,QAAQC,QAAQ,KAAK,WAAWhE,KAAKiE,KAAK,CAACH,UAAU,CAAC/B;QAEzD,wDAAwD;QACxD,sBAAsB;QACtB,IAAIA,YAAY,QAAQ;YACtB,OAAO,CAAC,0CAA0C,CAAC;QACrD;QAEA,MAAMmC,aAAa7D,kBAAkBsD;QAErC,+DAA+D;QAC/D,wDAAwD;QACxD,kEAAkE;QAClE,mEAAmE;QACnE,IAAI,CAACC,SAAS;YACZ,IAAI,aAAaO,IAAI,CAACpC,UAAU;gBAC9B,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IAAIxB,mBAAmB4D,IAAI,CAACpC,YAAY,CAACmC,YAAY;gBACnD,OAAO,CAAC,SAAS,EAAEnC,QAAQ,CAAC;YAC9B;YAEA,MAAMqC,qBACJ;YACF,IAAIA,mBAAmBD,IAAI,CAACpC,UAAU;gBACpC;YACF;QACF;QAEA,kDAAkD;QAClD,sDAAsD;QACtD,IAAIA,QAAQP,QAAQ,CAAC,iBAAiB;YACpC;QACF;QAEA,uEAAuE;QACvE,2EAA2E;QAC3E,IAAIO,QAAQV,UAAU,CAACtB,6BAA6B;YAClD;QACF;QAEA,gEAAgE;QAChE,yBAAyB;QACzB,kDAAkD;QAClD,MAAMiC,iBAAiB0B,mBAAmB;QAE1C;;;;;;KAMC,GACD,MAAMW,sBAAsB,CAACrB;YAC3B,MAAMsB,aAAa1D,gBAAgBuD,IAAI,CAACnB;YAExC,sFAAsF;YACtF,sGAAsG;YACtG,IAAIsB,YAAY;gBACd,oGAAoG;gBACpG,oCAAoC;gBACpC,OAAO,CAAC,SAAS,EAAEtB,SAAStB,OAAO,CAAC,oBAAoB,aAAa,CAAC;YACxE;QACF;QAEA,4DAA4D;QAC5D,yFAAyF;QACzF,IACEpB,qBAAqBqD,UACrB5B,YAAY,+CACZ;YACA,OAAO,CAAC,OAAO,EAAEA,QAAQ,CAAC;QAC5B;QAEA,uDAAuD;QACvD,+CAA+C;QAC/C,IAAIA,QAAQV,UAAU,CAAC,eAAe;YACpC,2CAA2C;YAC3C,sCAAsC;YACtC,IAAI,qDAAqD8C,IAAI,CAACpC,UAAU;gBACtE;YACF;YAEA,IAAI,8CAA8CoC,IAAI,CAACpC,UAAU;gBAC/D,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IACE,8DAA8DoC,IAAI,CAChEpC,YAEF,4CAA4CoC,IAAI,CAACpC,UACjD;gBACA,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IACE,sEAAsEoC,IAAI,CACxEpC,YAEF,2CAA2CoC,IAAI,CAACpC,UAChD;gBACA,OAAO,CAAC,OAAO,EAAEA,QAAQ,CAAC;YAC5B;YAEA,OAAOsC,oBAAoBtC;QAC7B;QAEA,gFAAgF;QAChF,qEAAqE;QACrE,oDAAoD;QACpD,IAAI4B,UAAU9D,eAAe0E,mBAAmB,EAAE;YAChD,MAAMC,aAAazC,QAAQV,UAAU,CAAC;YACtC,MAAMoD,cAAcD,aAChBxE,KAAKyB,IAAI,CAACK,SAASC,SAASL,OAAO,CAAC,OAAO,OAC3CK;YACJ,OAAOsC,oBAAoBI;QAC7B;QAEA,6FAA6F;QAC7F,MAAMC,gBAAgB,MAAM/C,gBAC1BC,KACAyB,OAAOG,YAAY,CAAChB,YAAY,EAChCV,SACAC,SACAC,gBACAC,YACA2B,UAAUS,sBAAsBM;QAGlC,IAAI,cAAcD,eAAe;YAC/B,OAAOA,cAAc1B,QAAQ;QAC/B;QAEA,wDAAwD;QACxD,mEAAmE;QACnE,IAAIjB,YAAY,oBAAoB;YAClC2C,cAAchC,GAAG,GAAG5C,gBAAgB,CAAC,mBAAmB;QAC1D;QAEA,MAAM,EAAE4C,GAAG,EAAEC,KAAK,EAAE,GAAG+B;QAEvB,oDAAoD;QACpD,0DAA0D;QAC1D,IAAI,CAAChC,KAAK;YACR;QACF;QAEA,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,CAACV,kBAAkBW,SAAS,CAACF,mBAAmB;YAClD,MAAM,IAAImC,MACR,CAAC,cAAc,EAAE7C,QAAQ,2HAA2H,CAAC;QAEzJ;QAEA,MAAM8C,eAAelC,QAAQ,WAAW;QAExC,sCAAsC;QACtC,IACE,qEAAqE;QACrE,2CAA2CwB,IAAI,CAACzB,MAChD;YACA;QACF;QAEA,wFAAwF;QACxF,IACE,2BAA2ByB,IAAI,CAACzB,QAChC,8BAA8ByB,IAAI,CAACzB,MACnC;YACA;QACF;QAEA,4EAA4E;QAC5E,yEAAyE;QACzE,IAAIW,OAAOyB,iBAAiB,IAAI,CAACvB,6BAA6B;YAC5DA,8BAA8B,IAAIwB;YAClC,8DAA8D;YAC9D,KAAK,MAAMC,OAAO3B,OAAOyB,iBAAiB,CAAE;gBAC1C,MAAMG,SAAS,MAAMtD,gBACnBC,KACAyB,OAAOG,YAAY,CAAChB,YAAY,EAChCV,SACAkD,MAAM,iBACNhD,gBACAC,YACA2B,UAAUS,sBAAsBM;gBAElC,IAAIM,OAAOvC,GAAG,EAAE;oBACda,4BAA4B2B,GAAG,CAACF,KAAKhF,KAAKmF,OAAO,CAACF,OAAOvC,GAAG;gBAC9D;YACF;QACF;QAEA,sFAAsF;QACtF,gFAAgF;QAChF,wEAAwE;QACxE,MAAM0C,kBACJtE,qBACE4B,KACAW,OAAOyB,iBAAiB,EACxBvB,gCAEDZ,SAASuB,cACT,CAACA,cAAcb,OAAOG,YAAY,CAAC6B,oBAAoB;QAE1D,IAAI,gCAAgClB,IAAI,CAACzB,MAAM;YAC7C,IAAIpC,qBAAqBqD,QAAQ;gBAC/B,gFAAgF;gBAChF,gEAAgE;gBAEhE,IAAIL,2BAA2Ba,IAAI,CAACzB,MAAM;oBACxC,OAAO,CAAC,EAAEmC,aAAa,CAAC,EAAE9C,QAAQ,CAAC;gBACrC;gBAEA;YACF;YAEA,IAAIqD,iBAAiB;YAErB,kEAAkE;YAClE,uBAAuB;YACvB,OAAO,CAAC,EAAEP,aAAa,CAAC,EAAE9C,QAAQ,CAAC;QACrC;QAEA,IAAIqD,iBAAiB;IAErB,qCAAqC;IACvC;AACF"}