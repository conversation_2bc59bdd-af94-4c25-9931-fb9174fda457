{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/app-paths.ts"], "names": ["ensureLeadingSlash", "isGroupSegment", "parse", "format", "normalizeAppPath", "route", "split", "reduce", "pathname", "segment", "index", "segments", "length", "normalizeRscURL", "url", "replace", "normalizePostponedURL", "parsed", "startsWith", "substring"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,uCAAsC;AACzE,SAASC,cAAc,QAAQ,gBAAe;AAC9C,SAASC,KAAK,EAAEC,MAAM,QAAQ,MAAK;AAEnC;;;;;;;;;;;;;;;;;;CAkBC,GACD,OAAO,SAASC,iBAAiBC,KAAa;IAC5C,OAAOL,mBACLK,MAAMC,KAAK,CAAC,KAAKC,MAAM,CAAC,CAACC,UAAUC,SAASC,OAAOC;QACjD,8BAA8B;QAC9B,IAAI,CAACF,SAAS;YACZ,OAAOD;QACT;QAEA,sBAAsB;QACtB,IAAIP,eAAeQ,UAAU;YAC3B,OAAOD;QACT;QAEA,iCAAiC;QACjC,IAAIC,OAAO,CAAC,EAAE,KAAK,KAAK;YACtB,OAAOD;QACT;QAEA,uDAAuD;QACvD,IACE,AAACC,CAAAA,YAAY,UAAUA,YAAY,OAAM,KACzCC,UAAUC,SAASC,MAAM,GAAG,GAC5B;YACA,OAAOJ;QACT;QAEA,OAAO,AAAGA,WAAS,MAAGC;IACxB,GAAG;AAEP;AAEA;;;CAGC,GACD,OAAO,SAASI,gBAAgBC,GAAW;IACzC,OAAOA,IAAIC,OAAO,CAChB,eACA,8BAA8B;IAC9B;AAEJ;AAEA;;;;CAIC,GACD,OAAO,SAASC,sBAAsBF,GAAW;IAC/C,MAAMG,SAASf,MAAMY;IACrB,IAAI,EAAEN,QAAQ,EAAE,GAAGS;IACnB,IAAIT,YAAYA,SAASU,UAAU,CAAC,qBAAqB;QACvDV,WAAWA,SAASW,SAAS,CAAC,mBAAmBP,MAAM,KAAK;QAE5D,OAAOT,OAAO;YAAE,GAAGc,MAAM;YAAET;QAAS;IACtC;IAEA,OAAOM;AACT"}