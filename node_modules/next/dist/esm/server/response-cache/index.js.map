{"version": 3, "sources": ["../../../src/server/response-cache/index.ts"], "names": ["RenderResult", "<PERSON><PERSON>", "scheduleOnNextTick", "ResponseCache", "constructor", "minimalMode", "batcher", "create", "cacheKeyFn", "key", "isOnDemandRevalidate", "schedulerFn", "minimalModeKey", "get", "responseGenerator", "context", "incrementalCache", "batch", "cache<PERSON>ey", "resolve", "previousCacheItem", "expiresAt", "Date", "now", "entry", "resolved", "cachedResponse", "value", "kind", "Error", "isStale", "revalidate", "curRevalidate", "html", "fromStatic", "pageData", "postponed", "headers", "status", "isPrefetch", "cacheEntry", "resolveValue", "isMiss", "set", "toUnchunkedString", "undefined", "err", "Math", "min", "max", "console", "error"], "mappings": "AAOA,OAAOA,kBAAkB,mBAAkB;AAC3C,SAASC,OAAO,QAAQ,oBAAmB;AAC3C,SAASC,kBAAkB,QAAQ,sBAAqB;AAExD,cAAc,UAAS;AAEvB,eAAe,MAAMC;IAwBnBC,YAAYC,WAAoB,CAAE;aAvBjBC,UAAUL,QAAQM,MAAM,CAIvC;YACA,0EAA0E;YAC1E,4EAA4E;YAC5EC,YAAY,CAAC,EAAEC,GAAG,EAAEC,oBAAoB,EAAE,GACxC,CAAC,EAAED,IAAI,CAAC,EAAEC,uBAAuB,MAAM,IAAI,CAAC;YAC9C,sEAAsE;YACtE,uEAAuE;YACvE,oDAAoD;YACpDC,aAAaT;QACf;QAWE,4EAA4E;QAC5E,qEAAqE;QACrE,MAAMU,iBAAiB;QACvB,IAAI,CAACA,eAAe,GAAGP;IACzB;IAEOQ,IACLJ,GAAkB,EAClBK,iBAAoC,EACpCC,OAIC,EACmC;QACpC,0EAA0E;QAC1E,6DAA6D;QAC7D,IAAI,CAACN,KAAK,OAAOK,kBAAkB,OAAO;QAE1C,MAAM,EAAEE,gBAAgB,EAAEN,uBAAuB,KAAK,EAAE,GAAGK;QAE3D,OAAO,IAAI,CAACT,OAAO,CAACW,KAAK,CACvB;YAAER;YAAKC;QAAqB,GAC5B,OAAOQ,UAAUC;gBAKb;YAJF,+DAA+D;YAC/D,iDAAiD;YACjD,IACE,IAAI,CAACd,WAAW,IAChB,EAAA,0BAAA,IAAI,CAACe,iBAAiB,qBAAtB,wBAAwBX,GAAG,MAAKS,YAChC,IAAI,CAACE,iBAAiB,CAACC,SAAS,GAAGC,KAAKC,GAAG,IAC3C;gBACA,OAAO,IAAI,CAACH,iBAAiB,CAACI,KAAK;YACrC;YAEA,IAAIC,WAAW;YACf,IAAIC,iBAAuC;YAC3C,IAAI;gBACFA,iBAAiB,CAAC,IAAI,CAACrB,WAAW,GAC9B,MAAMW,iBAAiBH,GAAG,CAACJ,OAC3B;gBAEJ,IAAIiB,kBAAkB,CAAChB,sBAAsB;wBACvCgB,uBAUAA;oBAVJ,IAAIA,EAAAA,wBAAAA,eAAeC,KAAK,qBAApBD,sBAAsBE,IAAI,MAAK,SAAS;wBAC1C,MAAM,IAAIC,MACR,CAAC,oEAAoE,CAAC;oBAE1E;oBAEAV,QAAQ;wBACNW,SAASJ,eAAeI,OAAO;wBAC/BC,YAAYL,eAAeM,aAAa;wBACxCL,OACED,EAAAA,yBAAAA,eAAeC,KAAK,qBAApBD,uBAAsBE,IAAI,MAAK,SAC3B;4BACEA,MAAM;4BACNK,MAAMjC,aAAakC,UAAU,CAACR,eAAeC,KAAK,CAACM,IAAI;4BACvDE,UAAUT,eAAeC,KAAK,CAACQ,QAAQ;4BACvCC,WAAWV,eAAeC,KAAK,CAACS,SAAS;4BACzCC,SAASX,eAAeC,KAAK,CAACU,OAAO;4BACrCC,QAAQZ,eAAeC,KAAK,CAACW,MAAM;wBACrC,IACAZ,eAAeC,KAAK;oBAC5B;oBACAF,WAAW;oBAEX,IAAI,CAACC,eAAeI,OAAO,IAAIf,QAAQwB,UAAU,EAAE;wBACjD,oDAAoD;wBACpD,oBAAoB;wBACpB,OAAO;oBACT;gBACF;gBAEA,MAAMC,aAAa,MAAM1B,kBAAkBW,UAAUC;gBACrD,MAAMe,eACJD,eAAe,OACX,OACA;oBACE,GAAGA,UAAU;oBACbE,QAAQ,CAAChB;gBACX;gBAEN,+DAA+D;gBAC/D,yBAAyB;gBACzB,IAAI,CAAChB,wBAAwB,CAACe,UAAU;oBACtCN,QAAQsB;oBACRhB,WAAW;gBACb;gBAEA,IAAIe,cAAc,OAAOA,WAAWT,UAAU,KAAK,aAAa;oBAC9D,IAAI,IAAI,CAAC1B,WAAW,EAAE;wBACpB,IAAI,CAACe,iBAAiB,GAAG;4BACvBX,KAAKS;4BACLM,OAAOgB;4BACPnB,WAAWC,KAAKC,GAAG,KAAK;wBAC1B;oBACF,OAAO;4BAGHiB;wBAFF,MAAMxB,iBAAiB2B,GAAG,CACxBlC,KACA+B,EAAAA,oBAAAA,WAAWb,KAAK,qBAAhBa,kBAAkBZ,IAAI,MAAK,SACvB;4BACEA,MAAM;4BACNK,MAAMO,WAAWb,KAAK,CAACM,IAAI,CAACW,iBAAiB;4BAC7CR,WAAWI,WAAWb,KAAK,CAACS,SAAS;4BACrCD,UAAUK,WAAWb,KAAK,CAACQ,QAAQ;4BACnCE,SAASG,WAAWb,KAAK,CAACU,OAAO;4BACjCC,QAAQE,WAAWb,KAAK,CAACW,MAAM;wBACjC,IACAE,WAAWb,KAAK,EACpB;4BACEI,YAAYS,WAAWT,UAAU;wBACnC;oBAEJ;gBACF,OAAO;oBACL,IAAI,CAACX,iBAAiB,GAAGyB;gBAC3B;gBAEA,OAAOJ;YACT,EAAE,OAAOK,KAAK;gBACZ,qEAAqE;gBACrE,sEAAsE;gBACtE,IAAIpB,gBAAgB;oBAClB,MAAMV,iBAAiB2B,GAAG,CAAClC,KAAKiB,eAAeC,KAAK,EAAE;wBACpDI,YAAYgB,KAAKC,GAAG,CAClBD,KAAKE,GAAG,CAACvB,eAAeK,UAAU,IAAI,GAAG,IACzC;oBAEJ;gBACF;gBAEA,qEAAqE;gBACrE,kDAAkD;gBAClD,IAAIN,UAAU;oBACZyB,QAAQC,KAAK,CAACL;oBACd,OAAO;gBACT;gBAEA,gEAAgE;gBAChE,MAAMA;YACR;QACF;IAEJ;AACF"}