{"version": 3, "sources": ["../../../src/server/app-render/create-error-handler.tsx"], "names": ["DYNAMIC_ERROR_CODE", "stringHash", "formatServerError", "isNotFoundError", "isRedirectError", "NEXT_DYNAMIC_NO_SSR_CODE", "SpanStatusCode", "getTracer", "isAbortError", "createErrorHandler", "_source", "dev", "isNextExport", "errorLogger", "capturedErrors", "allCapturedErrors", "err", "push", "digest", "message", "includes", "span", "getActiveScopeSpan", "recordException", "setStatus", "code", "ERROR", "catch", "process", "env", "NODE_ENV", "logAppDirError", "require", "console", "error", "stack", "toString"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,+CAA8C;AACjF,OAAOC,gBAAgB,iCAAgC;AACvD,SAASC,iBAAiB,QAAQ,gCAA+B;AACjE,SAASC,eAAe,QAAQ,oCAAmC;AACnE,SAASC,eAAe,QAAQ,mCAAkC;AAClE,SAASC,wBAAwB,QAAQ,6CAA4C;AACrF,SAASC,cAAc,EAAEC,SAAS,QAAQ,sBAAqB;AAC/D,SAASC,YAAY,QAAQ,mBAAkB;AAI/C;;;;CAIC,GACD,OAAO,SAASC,mBAAmB,EACjC;;GAEC,GACDC,OAAO,EACPC,GAAG,EACHC,YAAY,EACZC,WAAW,EACXC,cAAc,EACdC,iBAAiB,EAQlB;IACC,OAAO,CAACC;YA0BFA;QAzBJ,IAAID,mBAAmBA,kBAAkBE,IAAI,CAACD;QAE9C,IACEA,OACCA,CAAAA,IAAIE,MAAM,KAAKlB,sBACdG,gBAAgBa,QAChBA,IAAIE,MAAM,KAAKb,4BACfD,gBAAgBY,IAAG,GACrB;YACA,OAAOA,IAAIE,MAAM;QACnB;QAEA,8DAA8D;QAC9D,IAAIV,aAAaQ,MAAM;QAEvB,yEAAyE;QACzE,IAAIL,KAAK;YACPT,kBAAkBc;QACpB;QACA,kCAAkC;QAClC,8BAA8B;QAC9B,+CAA+C;QAC/C,IACE,CACEJ,CAAAA,iBACAI,wBAAAA,eAAAA,IAAKG,OAAO,qBAAZH,aAAcI,QAAQ,CACpB,4FACF,GAEF;YACA,oDAAoD;YACpD,MAAMC,OAAOd,YAAYe,kBAAkB;YAC3C,IAAID,MAAM;gBACRA,KAAKE,eAAe,CAACP;gBACrBK,KAAKG,SAAS,CAAC;oBACbC,MAAMnB,eAAeoB,KAAK;oBAC1BP,SAASH,IAAIG,OAAO;gBACtB;YACF;YAEA,IAAIN,aAAa;gBACfA,YAAYG,KAAKW,KAAK,CAAC,KAAO;YAChC,OAAO;gBACL,kEAAkE;gBAClE,mCAAmC;gBACnC,mEAAmE;gBACnE,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;oBACzC,MAAM,EAAEC,cAAc,EAAE,GACtBC,QAAQ;oBACVD,eAAef;gBACjB;gBACA,IAAIY,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;oBACzCG,QAAQC,KAAK,CAAClB;gBAChB;YACF;QACF;QAEAF,eAAeG,IAAI,CAACD;QACpB,+EAA+E;QAC/E,OAAOf,WAAWe,IAAIG,OAAO,GAAGH,IAAImB,KAAK,GAAInB,CAAAA,IAAIE,MAAM,IAAI,EAAC,GAAIkB,QAAQ;IAC1E;AACF"}