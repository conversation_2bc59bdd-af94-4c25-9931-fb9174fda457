{"version": 3, "sources": ["../../../src/server/app-render/create-component-tree.tsx"], "names": ["React", "isClientReference", "getLayoutOrPageModule", "interopDefault", "preloadComponent", "addSearchParamsIfPageSegment", "parseLoaderTree", "createComponentStylesAndScripts", "getLayerAssets", "hasLoadingComponentInTree", "createComponentTree", "createSegmentPath", "loaderTree", "tree", "parentParams", "firstItem", "rootLayoutIncluded", "injectedCSS", "injectedJS", "injectedFontPreloadTags", "asNotFound", "metadataOutlet", "ctx", "renderOpts", "nextConfigOutput", "staticGenerationStore", "componentMod", "staticGenerationBailout", "NotFoundBoundary", "LayoutRouter", "RenderFromTemplateContext", "StaticGenerationSearchParamsBailoutProvider", "serverHooks", "DynamicServerError", "pagePath", "getDynamicParamFromSegment", "query", "isPrefetch", "searchParamsProps", "page", "layoutOrPagePath", "segment", "components", "parallelRoutes", "layout", "template", "error", "loading", "notFound", "injectedCSSWithCurrentLayout", "Set", "injectedJSWithCurrentLayout", "injectedFontPreloadTagsWithCurrentLayout", "layerAssets", "Template", "templateStyles", "templateScripts", "filePath", "getComponent", "Fragment", "ErrorComponent", "errorStyles", "errorScripts", "Loading", "loadingStyles", "loadingScripts", "isLayout", "isPage", "layoutOrPageMod", "rootLayoutAtThisLevel", "rootLayoutIncludedAtThisLevelOrAbove", "NotFound", "notFoundStyles", "dynamic", "forceDynamic", "dynamicShouldError", "link", "forceStatic", "fetchCache", "revalidate", "defaultRevalidate", "isStaticGeneration", "dynamicUsageDescription", "dynamicUsageErr", "experimental", "ppr", "LayoutOrPage", "undefined", "Component", "parallelKeys", "Object", "keys", "hasSlot<PERSON>ey", "length", "componentProps", "NotFoundComponent", "RootLayoutComponent", "process", "env", "NODE_ENV", "isValidElementType", "require", "Error", "segmentParam", "currentParams", "value", "param", "actualSegment", "treeSegment", "parallelRouteMap", "Promise", "all", "map", "parallelRouteKey", "isChildrenRoute<PERSON>ey", "currentSegmentPath", "parallelRoute", "childSegment", "childSegmentParam", "notFoundComponent", "getParallelRoutePair", "currentChildProp", "currentStyles", "parallel<PERSON><PERSON>er<PERSON>ey", "segmentPath", "hasLoading", "Boolean", "childProp", "styles", "childElement", "childPropSegment", "ChildComponent", "childComponentStyles", "child", "current", "parallelRouteComponents", "reduce", "list", "Comp", "children", "isClientComponent", "meta", "name", "content", "props", "params", "resolve", "then", "propsForComponent"], "mappings": "AACA,OAAOA,WAAW,QAAO;AACzB,SAASC,iBAAiB,QAAQ,6BAA4B;AAC9D,SAASC,qBAAqB,QAAQ,wBAAuB;AAE7D,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,gBAAgB,QAAQ,sBAAqB;AACtD,SAASC,4BAA4B,QAAQ,gDAA+C;AAC5F,SAASC,eAAe,QAAQ,sBAAqB;AAErD,SAASC,+BAA+B,QAAQ,wCAAuC;AACvF,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,yBAAyB,QAAQ,kCAAiC;AAE3E;;CAEC,GAED,OAAO,eAAeC,oBAAoB,EACxCC,iBAAiB,EACjBC,YAAYC,IAAI,EAChBC,YAAY,EACZC,SAAS,EACTC,kBAAkB,EAClBC,WAAW,EACXC,UAAU,EACVC,uBAAuB,EACvBC,UAAU,EACVC,cAAc,EACdC,GAAG,EAaJ;IAIC,MAAM,EACJC,YAAY,EAAEC,gBAAgB,EAAE,EAChCC,qBAAqB,EACrBC,cAAc,EACZC,uBAAuB,EACvBC,gBAAgB,EAChBC,YAAY,EACZC,yBAAyB,EACzBC,2CAA2C,EAC3CC,aAAa,EAAEC,kBAAkB,EAAE,EACpC,EACDC,QAAQ,EACRC,0BAA0B,EAC1BC,KAAK,EACLC,UAAU,EACVC,iBAAiB,EAClB,GAAGhB;IAEJ,MAAM,EAAEiB,IAAI,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,UAAU,EAAEC,cAAc,EAAE,GACnErC,gBAAgBO;IAElB,MAAM,EAAE+B,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAE,aAAaC,QAAQ,EAAE,GAAGN;IAEpE,MAAMO,+BAA+B,IAAIC,IAAIjC;IAC7C,MAAMkC,8BAA8B,IAAID,IAAIhC;IAC5C,MAAMkC,2CAA2C,IAAIF,IACnD/B;IAGF,MAAMkC,cAAc7C,eAAe;QACjCc;QACAkB;QACAvB,aAAagC;QACb/B,YAAYiC;QACZhC,yBAAyBiC;IAC3B;IAEA,MAAM,CAACE,UAAUC,gBAAgBC,gBAAgB,GAAGX,WAChD,MAAMtC,gCAAgC;QACpCe;QACAmC,UAAUZ,QAAQ,CAAC,EAAE;QACrBa,cAAcb,QAAQ,CAAC,EAAE;QACzB5B,aAAagC;QACb/B,YAAYiC;IACd,KACA;QAACnD,MAAM2D,QAAQ;KAAC;IAEpB,MAAM,CAACC,gBAAgBC,aAAaC,aAAa,GAAGhB,QAChD,MAAMvC,gCAAgC;QACpCe;QACAmC,UAAUX,KAAK,CAAC,EAAE;QAClBY,cAAcZ,KAAK,CAAC,EAAE;QACtB7B,aAAagC;QACb/B,YAAYiC;IACd,KACA,EAAE;IAEN,MAAM,CAACY,SAASC,eAAeC,eAAe,GAAGlB,UAC7C,MAAMxC,gCAAgC;QACpCe;QACAmC,UAAUV,OAAO,CAAC,EAAE;QACpBW,cAAcX,OAAO,CAAC,EAAE;QACxB9B,aAAagC;QACb/B,YAAYiC;IACd,KACA,EAAE;IAEN,MAAMe,WAAW,OAAOtB,WAAW;IACnC,MAAMuB,SAAS,OAAO5B,SAAS;IAC/B,MAAM,CAAC6B,gBAAgB,GAAG,MAAMlE,sBAAsBW;IAEtD;;GAEC,GACD,MAAMwD,wBAAwBH,YAAY,CAAClD;IAC3C;;GAEC,GACD,MAAMsD,uCACJtD,sBAAsBqD;IAExB,MAAM,CAACE,UAAUC,eAAe,GAAGxB,WAC/B,MAAMzC,gCAAgC;QACpCe;QACAmC,UAAUT,QAAQ,CAAC,EAAE;QACrBU,cAAcV,QAAQ,CAAC,EAAE;QACzB/B,aAAagC;QACb/B,YAAYiC;IACd,KACA,EAAE;IAEN,IAAIsB,UAAUL,mCAAAA,gBAAiBK,OAAO;IAEtC,IAAIjD,qBAAqB,UAAU;QACjC,IAAI,CAACiD,WAAWA,YAAY,QAAQ;YAClCA,UAAU;QACZ,OAAO,IAAIA,YAAY,iBAAiB;YACtChD,sBAAsBiD,YAAY,GAAG;YACrCjD,sBAAsBkD,kBAAkB,GAAG;YAC3ChD,wBAAwB,CAAC,cAAc,CAAC,EAAE;gBACxC8C;gBACAG,MAAM;YACR;QACF;IACF;IAEA,IAAI,OAAOH,YAAY,UAAU;QAC/B,sDAAsD;QACtD,sDAAsD;QACtD,YAAY;QACZ,IAAIA,YAAY,SAAS;YACvBhD,sBAAsBkD,kBAAkB,GAAG;QAC7C,OAAO,IAAIF,YAAY,iBAAiB;YACtChD,sBAAsBiD,YAAY,GAAG;YACrC/C,wBAAwB,CAAC,aAAa,CAAC,EAAE;gBAAE8C;YAAQ;QACrD,OAAO;YACLhD,sBAAsBkD,kBAAkB,GAAG;YAC3C,IAAIF,YAAY,gBAAgB;gBAC9BhD,sBAAsBoD,WAAW,GAAG;YACtC,OAAO;gBACLpD,sBAAsBoD,WAAW,GAAG;YACtC;QACF;IACF;IAEA,IAAI,QAAOT,mCAAAA,gBAAiBU,UAAU,MAAK,UAAU;QACnDrD,sBAAsBqD,UAAU,GAAGV,mCAAAA,gBAAiBU,UAAU;IAChE;IAEA,IAAI,QAAOV,mCAAAA,gBAAiBW,UAAU,MAAK,UAAU;QACnDzD,IAAI0D,iBAAiB,GAAGZ,gBAAgBW,UAAU;QAElD,IACE,OAAOtD,sBAAsBsD,UAAU,KAAK,eAC3C,OAAOtD,sBAAsBsD,UAAU,KAAK,YAC3CtD,sBAAsBsD,UAAU,GAAGzD,IAAI0D,iBAAiB,EAC1D;YACAvD,sBAAsBsD,UAAU,GAAGzD,IAAI0D,iBAAiB;QAC1D;QAEA,IACEvD,sBAAsBwD,kBAAkB,IACxC3D,IAAI0D,iBAAiB,KAAK,GAC1B;YACA,MAAME,0BAA0B,CAAC,yBAAyB,EAAEzC,QAAQ,CAAC;YACrEhB,sBAAsByD,uBAAuB,GAAGA;YAEhD,MAAM,IAAIjD,mBAAmBiD;QAC/B;IACF;IAEA,IACEzD,CAAAA,yCAAAA,sBAAuB0D,eAAe,KACtC,CAAC1D,sBAAsB2D,YAAY,CAACC,GAAG,EACvC;QACA,MAAM5D,sBAAsB0D,eAAe;IAC7C;IAEA,MAAMG,eAAelB,kBACjBjE,eAAeiE,mBACfmB;IAEJ;;GAEC,GACD,IAAIC,YAAYF;IAChB,MAAMG,eAAeC,OAAOC,IAAI,CAAChD;IACjC,MAAMiD,aAAaH,aAAaI,MAAM,GAAG;IAEzC,IAAID,cAAcvB,uBAAuB;QACvCmB,YAAY,CAACM;YACX,MAAMC,oBAAoBxB;YAC1B,MAAMyB,sBAAsBV;YAC5B,qBACE,oBAAC1D;gBACCoB,wBACE,0CACGK,2BACD,oBAAC2C,2BACExB,8BACD,oBAACuB;6BAKP,oBAACC,qBAAwBF;QAG/B;IACF;IAEA,IAAIG,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,kBAAkB,EAAE,GAAGC,QAAQ;QACvC,IACE,AAAClC,CAAAA,UAAU,OAAOqB,cAAc,WAAU,KAC1C,CAACY,mBAAmBZ,YACpB;YACA,MAAM,IAAIc,MACR,CAAC,sDAAsD,EAAEpE,SAAS,CAAC,CAAC;QAExE;QAEA,IACE,OAAO0B,mBAAmB,eAC1B,CAACwC,mBAAmBxC,iBACpB;YACA,MAAM,IAAI0C,MACR,CAAC,8DAA8D,EAAE7D,QAAQ,CAAC;QAE9E;QAEA,IAAI,OAAOsB,YAAY,eAAe,CAACqC,mBAAmBrC,UAAU;YAClE,MAAM,IAAIuC,MACR,CAAC,0DAA0D,EAAE7D,QAAQ,CAAC;QAE1E;QAEA,IAAI,OAAO8B,aAAa,eAAe,CAAC6B,mBAAmB7B,WAAW;YACpE,MAAM,IAAI+B,MACR,CAAC,2DAA2D,EAAE7D,QAAQ,CAAC;QAE3E;IACF;IAEA,iCAAiC;IACjC,MAAM8D,eAAepE,2BAA2BM;IAChD;;GAEC,GACD,MAAM+D,gBACJ,mDAAmD;IACnDD,gBAAgBA,aAAaE,KAAK,KAAK,OACnC;QACE,GAAG3F,YAAY;QACf,CAACyF,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;IAC1C,IAEA3F;IACN,4BAA4B;IAC5B,MAAM6F,gBAAgBJ,eAAeA,aAAaK,WAAW,GAAGnE;IAEhE,mHAAmH;IACnH,MAAMoE,mBAAmB,MAAMC,QAAQC,GAAG,CACxCrB,OAAOC,IAAI,CAAChD,gBAAgBqE,GAAG,CAC7B,OAAOC;QACL,MAAMC,qBAAqBD,qBAAqB;QAChD,MAAME,qBAAwCpG,YAC1C;YAACkG;SAAiB,GAClB;YAACN;YAAeM;SAAiB;QAErC,MAAMG,gBAAgBzE,cAAc,CAACsE,iBAAiB;QAEtD,MAAMI,eAAeD,aAAa,CAAC,EAAE;QACrC,MAAME,oBAAoBnF,2BAA2BkF;QACrD,MAAME,oBACJhD,YAAY2C,mCAAqB,oBAAC3C,kBAAcgB;QAElD,SAASiC,qBACPC,gBAA2B,EAC3BC,aAA8B;YAE9B,4CAA4C;YAC5C,OAAO;gBACLT;8BACA,oBAACpF;oBACC8F,mBAAmBV;oBACnBW,aAAajH,kBAAkBwG;oBAC/BpE,SAASgB,wBAAU,oBAACA,iBAAawB;oBACjCvB,eAAeA;oBACfC,gBAAgBA;oBAChB,sKAAsK;oBACtK4D,YAAYC,QAAQ/D;oBACpBjB,OAAOc;oBACPC,aAAaA;oBACbC,cAAcA;oBACdjB,wBACE,oBAACS,8BACC,oBAACxB;oBAGLyB,gBAAgBA;oBAChBC,iBAAiBA;oBACjBR,UAAUuE;oBACV/C,gBAAgBA;oBAChBuD,WAAWN;oBACXO,QAAQN;;aAEX;QACH;QAEA,yEAAyE;QACzE,gDAAgD;QAChD,wEAAwE;QACxE,IAAIA,gBAAgBnC;QACpB,IAAI0C,eAAe;QACnB,MAAMC,mBAAmB7H,6BACvBiH,oBAAoBA,kBAAkBV,WAAW,GAAGS,cACpDjF;QAEF,IACE,CACEC,CAAAA,cACC0B,CAAAA,WAAW,CAACtD,0BAA0B2G,cAAa,CAAC,GAEvD;YACA,6BAA6B;YAC7B,MAAM,EAAE5B,WAAW2C,cAAc,EAAEH,QAAQI,oBAAoB,EAAE,GAC/D,MAAM1H,oBAAoB;gBACxBC,mBAAmB,CAAC0H;oBAClB,OAAO1H,kBAAkB;2BAAIwG;2BAAuBkB;qBAAM;gBAC5D;gBACAzH,YAAYwG;gBACZtG,cAAc0F;gBACdxF,oBAAoBsD;gBACpBrD,aAAagC;gBACb/B,YAAYiC;gBACZhC,yBAAyBiC;gBACzBhC;gBACAC;gBACAC;YACF;YAEFoG,gBAAgBU;YAChBH,6BAAe,oBAACE;QAClB;QAEA,MAAMJ,YAAuB;YAC3BO,SAASL;YACTxF,SAASyF;QACX;QAEA,OAAOV,qBAAqBO,WAAWL;IACzC;IAIJ,uFAAuF;IACvF,MAAMa,0BAA0B1B,iBAAiB2B,MAAM,CACrD,CAACC,MAAM,CAACxB,kBAAkByB,KAAK;QAC7BD,IAAI,CAACxB,iBAAiB,GAAGyB;QACzB,OAAOD;IACT,GACA,CAAC;IAGH,wIAAwI;IACxI,IAAI,CAACjD,WAAW;QACd,OAAO;YACLA,WAAW,kBAAM,0CAAG+C,wBAAwBI,QAAQ;YACpDX,QAAQ3E;QACV;IACF;IAEA,MAAMuF,oBAAoB3I,kBAAkBmE;IAE5C,oEAAoE;IACpE,iEAAiE;IACjE,IAAImD,oBAAoB,CAAC;IACzB,IACEhD,YACAnD,cACA,2GAA2G;IAC3G,6DAA6D;IAC7D,CAACyF,iBAAiBhB,MAAM,EACxB;QACA0B,oBAAoB;YAClBoB,wBACE,wDACE,oBAACE;gBAAKC,MAAK;gBAASC,SAAQ;gBAC3B9C,QAAQC,GAAG,CAACC,QAAQ,KAAK,+BACxB,oBAAC0C;gBAAKC,MAAK;gBAAaC,SAAQ;gBAEjCvE,8BACD,oBAACD;QAGP;IACF;IAEA,MAAMyE,QAAQ;QACZ,GAAGT,uBAAuB;QAC1B,GAAGhB,iBAAiB;QACpB,8GAA8G;QAC9G,gEAAgE;QAChE,+GAA+G;QAC/G0B,QAAQzC;QACR,iCAAiC;QACjC,GAAG,AAAC,CAAA;YACF,IAAIoC,qBAAqBnH,sBAAsBwD,kBAAkB,EAAE;gBACjE,OAAO,CAAC;YACV;YAEA,IAAId,QAAQ;gBACV,OAAO7B;YACT;QACF,CAAA,GAAI;IACN;IAEA,kEAAkE;IAClE,IAAI,CAACsG,mBAAmB;QACtBpD,YAAY,MAAMsB,QAAQoC,OAAO,GAAGC,IAAI,CAAC,IACvC/I,iBAAiBoF,WAAWwD;IAEhC;IAEA,OAAO;QACLxD,WAAW;YACT,qBACE,0CACGrB,SAAS9C,iBAAiB,MAE1B8C,UAAUyE,kCACT,oBAAC7G;gBACCqH,mBAAmBJ;gBACnBxD,WAAWA;gBACXP,oBAAoBxD,sBAAsBwD,kBAAkB;+BAG9D,oBAACO,WAAcwD,QAUhB;QAGP;QACAhB,QAAQ3E;IACV;AACF"}