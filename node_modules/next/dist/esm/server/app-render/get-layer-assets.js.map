{"version": 3, "sources": ["../../../src/server/app-render/get-layer-assets.tsx"], "names": ["React", "getLinkAndScriptTags", "getPreloadableFonts", "getAssetQueryString", "getLayerAssets", "ctx", "layoutOrPagePath", "injectedCSS", "injectedCSSWithCurrentLayout", "injectedJS", "injectedJSWithCurrentLayout", "injectedFontPreloadTags", "injectedFontPreloadTagsWithCurrentLayout", "styles", "styleTags", "scripts", "scriptTags", "clientReferenceManifest", "preloadedFontFiles", "renderOpts", "nextFontManifest", "length", "i", "fontFilename", "ext", "exec", "type", "href", "assetPrefix", "componentMod", "preloadFont", "crossOrigin", "url", "URL", "preconnect", "origin", "error", "map", "index", "fullHref", "precedence", "process", "env", "NODE_ENV", "preloadStyle", "link", "rel", "key", "fullSrc", "script", "src", "async"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AACzB,SAASC,oBAAoB,QAAQ,8BAA6B;AAClE,SAASC,mBAAmB,QAAQ,0BAAyB;AAE7D,SAASC,mBAAmB,QAAQ,2BAA0B;AAE9D,OAAO,SAASC,eAAe,EAC7BC,GAAG,EACHC,gBAAgB,EAChBC,aAAaC,4BAA4B,EACzCC,YAAYC,2BAA2B,EACvCC,yBAAyBC,wCAAwC,EAOlE;IACC,MAAM,EAAEC,QAAQC,SAAS,EAAEC,SAASC,UAAU,EAAE,GAAGV,mBAC/CL,qBACEI,IAAIY,uBAAuB,EAC3BX,kBACAE,8BACAE,6BACA,QAEF;QAAEG,QAAQ,EAAE;QAAEE,SAAS,EAAE;IAAC;IAE9B,MAAMG,qBAAqBZ,mBACvBJ,oBACEG,IAAIc,UAAU,CAACC,gBAAgB,EAC/Bd,kBACAM,4CAEF;IAEJ,IAAIM,oBAAoB;QACtB,IAAIA,mBAAmBG,MAAM,EAAE;YAC7B,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,mBAAmBG,MAAM,EAAEC,IAAK;gBAClD,MAAMC,eAAeL,kBAAkB,CAACI,EAAE;gBAC1C,MAAME,MAAM,8BAA8BC,IAAI,CAACF,aAAc,CAAC,EAAE;gBAChE,MAAMG,OAAO,CAAC,KAAK,EAAEF,IAAI,CAAC;gBAC1B,MAAMG,OAAO,CAAC,EAAEtB,IAAIuB,WAAW,CAAC,OAAO,EAAEL,aAAa,CAAC;gBACvDlB,IAAIwB,YAAY,CAACC,WAAW,CAACH,MAAMD,MAAMrB,IAAIc,UAAU,CAACY,WAAW;YACrE;QACF,OAAO;YACL,IAAI;gBACF,IAAIC,MAAM,IAAIC,IAAI5B,IAAIuB,WAAW;gBACjCvB,IAAIwB,YAAY,CAACK,UAAU,CAACF,IAAIG,MAAM,EAAE;YAC1C,EAAE,OAAOC,OAAO;gBACd,mEAAmE;gBACnE,8CAA8C;gBAC9C/B,IAAIwB,YAAY,CAACK,UAAU,CAAC,KAAK;YACnC;QACF;IACF;IAEA,MAAMrB,SAASC,YACXA,UAAUuB,GAAG,CAAC,CAACV,MAAMW;QACnB,iEAAiE;QACjE,kDAAkD;QAClD,mDAAmD;QACnD,mEAAmE;QACnE,mEAAmE;QACnE,cAAc;QACd,MAAMC,WAAW,CAAC,EAAElC,IAAIuB,WAAW,CAAC,OAAO,EAAED,KAAK,EAAExB,oBAClDE,KACA,MACA,CAAC;QAEH,gEAAgE;QAChE,oEAAoE;QACpE,2DAA2D;QAC3D,iEAAiE;QACjE,0DAA0D;QAC1D,+CAA+C;QAC/C,MAAMmC,aACJC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,UAAUhB,OAAO;QAE5DtB,IAAIwB,YAAY,CAACe,YAAY,CAACL,UAAUlC,IAAIc,UAAU,CAACY,WAAW;QAElE,qBACE,oBAACc;YACCC,KAAI;YACJnB,MAAMY;YACN,aAAa;YACbC,YAAYA;YACZT,aAAa1B,IAAIc,UAAU,CAACY,WAAW;YACvCgB,KAAKT;;IAGX,KACA,EAAE;IAEN,MAAMvB,UAAUC,aACZA,WAAWqB,GAAG,CAAC,CAACV,MAAMW;QACpB,MAAMU,UAAU,CAAC,EAAE3C,IAAIuB,WAAW,CAAC,OAAO,EAAED,KAAK,CAAC;QAElD,qBAAO,oBAACsB;YAAOC,KAAKF;YAASG,OAAO;YAAMJ,KAAKT;;IACjD,KACA,EAAE;IAEN,OAAOzB,OAAOQ,MAAM,IAAIN,QAAQM,MAAM,GAAG;WAAIR;WAAWE;KAAQ,GAAG;AACrE"}