{"version": 3, "sources": ["../../../../../src/server/future/normalizers/request/rsc.ts"], "names": ["RSCPathnameNormalizer", "constructor", "hasAppDir", "match", "pathname", "endsWith", "normalize", "matched", "substring", "length"], "mappings": "AAEA,OAAO,MAAMA;IACXC,YAA6BC,UAAoB;yBAApBA;IAAqB;IAE3CC,MAAMC,QAAgB,EAAE;QAC7B,+CAA+C;QAC/C,IAAI,CAAC,IAAI,CAACF,SAAS,EAAE,OAAO;QAE5B,yDAAyD;QACzD,IAAI,CAACE,SAASC,QAAQ,CAAC,SAAS,OAAO;QAEvC,OAAO;IACT;IAEOC,UAAUF,QAAgB,EAAEG,OAAiB,EAAU;QAC5D,2DAA2D;QAC3D,IAAI,CAAC,IAAI,CAACL,SAAS,EAAE,OAAOE;QAE5B,uEAAuE;QACvE,IAAI,CAACG,WAAW,CAAC,IAAI,CAACJ,KAAK,CAACC,WAAW,OAAOA;QAE9C,OAAOA,SAASI,SAAS,CAAC,GAAGJ,SAASK,MAAM,GAAG;IACjD;AACF"}