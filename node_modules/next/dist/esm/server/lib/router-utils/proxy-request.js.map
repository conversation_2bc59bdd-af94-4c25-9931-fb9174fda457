{"version": 3, "sources": ["../../../../src/server/lib/router-utils/proxy-request.ts"], "names": ["url", "stringifyQuery", "proxyRequest", "req", "res", "parsedUrl", "upgradeHead", "reqBody", "proxyTimeout", "query", "search", "target", "format", "HttpProxy", "require", "proxy", "<PERSON><PERSON><PERSON><PERSON>", "ignore<PERSON><PERSON>", "xfwd", "ws", "undefined", "Promise", "proxyResolve", "proxyReject", "finished", "on", "proxyReq", "destroy", "proxyRes", "destroyed", "innerReq", "innerRes", "cleanup", "err", "removeListener", "once", "console", "error", "statusCode", "end", "web", "buffer"], "mappings": "AAGA,OAAOA,SAAS,MAAK;AACrB,SAASC,cAAc,QAAQ,2BAA0B;AAEzD,OAAO,eAAeC,aACpBC,GAAoB,EACpBC,GAAmB,EACnBC,SAAiC,EACjCC,WAAiB,EACjBC,OAAa,EACbC,YAA4B;IAE5B,MAAM,EAAEC,KAAK,EAAE,GAAGJ;IAClB,OAAO,AAACA,UAAkBI,KAAK;IAC/BJ,UAAUK,MAAM,GAAGT,eAAeE,KAAYM;IAE9C,MAAME,SAASX,IAAIY,MAAM,CAACP;IAC1B,MAAMQ,YACJC,QAAQ;IAEV,MAAMC,QAAQ,IAAIF,UAAU;QAC1BF;QACAK,cAAc;QACdC,YAAY;QACZC,MAAM;QACNC,IAAI;QACJ,4DAA4D;QAC5D,yDAAyD;QACzDX,cAAcA,iBAAiB,OAAOY,YAAYZ,gBAAgB;IACpE;IAEA,MAAM,IAAIa,QAAQ,CAACC,cAAcC;QAC/B,IAAIC,WAAW;QAEf,mEAAmE;QACnE,sEAAsE;QACtE,qEAAqE;QACrE,uEAAuE;QACvE,uEAAuE;QACvE,kEAAkE;QAClE,cAAc;QACdT,MAAMU,EAAE,CAAC,YAAY,CAACC;YACpBtB,IAAIqB,EAAE,CAAC,SAAS,IAAMC,SAASC,OAAO;QACxC;QACAZ,MAAMU,EAAE,CAAC,YAAY,CAACG;YACpB,IAAIxB,IAAIyB,SAAS,EAAE;gBACjBD,SAASD,OAAO;YAClB,OAAO;gBACLvB,IAAIqB,EAAE,CAAC,SAAS,IAAMG,SAASD,OAAO;YACxC;QACF;QAEAZ,MAAMU,EAAE,CAAC,YAAY,CAACG,UAAUE,UAAUC;YACxC,MAAMC,UAAU,CAACC;gBACf,4DAA4D;gBAC5DL,SAASM,cAAc,CAAC,SAASF;gBACjCJ,SAASM,cAAc,CAAC,SAASF;gBACjCD,SAASG,cAAc,CAAC,SAASF;gBACjCD,SAASG,cAAc,CAAC,SAASF;gBAEjC,oEAAoE;gBACpEF,SAASH,OAAO,CAACM;gBACjBL,SAASD,OAAO,CAACM;YACnB;YAEAL,SAASO,IAAI,CAAC,SAASH;YACvBJ,SAASO,IAAI,CAAC,SAASH;YACvBD,SAASI,IAAI,CAAC,SAASH;YACvBD,SAASI,IAAI,CAAC,SAASH;QACzB;QAEAjB,MAAMU,EAAE,CAAC,SAAS,CAACQ;YACjBG,QAAQC,KAAK,CAAC,CAAC,gBAAgB,EAAE1B,OAAO,CAAC,EAAEsB;YAC3C,IAAI,CAACT,UAAU;gBACbA,WAAW;gBACXD,YAAYU;gBAEZ,IAAI,CAAC7B,IAAIyB,SAAS,EAAE;oBAClBzB,IAAIkC,UAAU,GAAG;oBACjBlC,IAAImC,GAAG,CAAC;gBACV;YACF;QACF;QAEA,wDAAwD;QACxD,IAAIjC,aAAa;YACfS,MAAMU,EAAE,CAAC,cAAc,CAACC;gBACtBA,SAASD,EAAE,CAAC,SAAS;oBACnB,IAAI,CAACD,UAAU;wBACbA,WAAW;wBACXF,aAAa;oBACf;gBACF;YACF;YACAP,MAAMI,EAAE,CAAChB,KAA+BC,KAAKE;YAC7CgB,aAAa;QACf,OAAO;YACLP,MAAMU,EAAE,CAAC,YAAY,CAACC;gBACpBA,SAASD,EAAE,CAAC,SAAS;oBACnB,IAAI,CAACD,UAAU;wBACbA,WAAW;wBACXF,aAAa;oBACf;gBACF;YACF;YACAP,MAAMyB,GAAG,CAACrC,KAAKC,KAAK;gBAClBqC,QAAQlC;YACV;QACF;IACF;AACF"}