{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/fetch-cache.ts"], "names": ["L<PERSON><PERSON><PERSON>", "CACHE_ONE_YEAR", "NEXT_CACHE_SOFT_TAGS_HEADER", "rateLimitedUntil", "memoryCache", "CACHE_TAGS_HEADER", "CACHE_HEADERS_HEADER", "CACHE_STATE_HEADER", "CACHE_REVALIDATE_HEADER", "CACHE_FETCH_URL_HEADER", "CACHE_CONTROL_VALUE_HEADER", "<PERSON><PERSON><PERSON><PERSON>", "isAvailable", "ctx", "_requestHeaders", "process", "env", "SUSPENSE_CACHE_URL", "constructor", "debug", "NEXT_PRIVATE_DEBUG_CACHE", "headers", "newHeaders", "JSON", "parse", "k", "scHost", "sc<PERSON><PERSON><PERSON><PERSON>", "SUSPENSE_CACHE_BASEPATH", "SUSPENSE_CACHE_AUTH_TOKEN", "cacheEndpoint", "console", "log", "maxMemoryCacheSize", "max", "length", "value", "kind", "stringify", "props", "Error", "data", "body", "html", "pageData", "revalidateTag", "tag", "Date", "now", "res", "fetch", "method", "next", "internal", "status", "retryAfter", "get", "parseInt", "ok", "err", "warn", "key", "tags", "softTags", "fetchCache", "fetchIdx", "fetchUrl", "lastModified", "undefined", "start", "fetchParams", "fetchType", "join", "error", "text", "cached", "json", "cacheState", "age", "Object", "keys", "set", "revalidate", "toString"], "mappings": "AAEA,OAAOA,cAAc,+BAA8B;AACnD,SACEC,cAAc,EACdC,2BAA2B,QACtB,yBAAwB;AAE/B,IAAIC,mBAAmB;AACvB,IAAIC;AASJ,MAAMC,oBAAoB;AAC1B,MAAMC,uBAAuB;AAC7B,MAAMC,qBAAqB;AAC3B,MAAMC,0BAA0B;AAChC,MAAMC,yBAAyB;AAC/B,MAAMC,6BAA6B;AAEnC,eAAe,MAAMC;IAKnB,OAAOC,YAAYC,GAElB,EAAE;QACD,OAAO,CAAC,CACNA,CAAAA,IAAIC,eAAe,CAAC,mBAAmB,IAAIC,QAAQC,GAAG,CAACC,kBAAkB,AAAD;IAE5E;IAEAC,YAAYL,GAAwB,CAAE;QACpC,IAAI,CAACM,KAAK,GAAG,CAAC,CAACJ,QAAQC,GAAG,CAACI,wBAAwB;QACnD,IAAI,CAACC,OAAO,GAAG,CAAC;QAChB,IAAI,CAACA,OAAO,CAAC,eAAe,GAAG;QAE/B,IAAIf,wBAAwBO,IAAIC,eAAe,EAAE;YAC/C,MAAMQ,aAAaC,KAAKC,KAAK,CAC3BX,IAAIC,eAAe,CAACR,qBAAqB;YAE3C,IAAK,MAAMmB,KAAKH,WAAY;gBAC1B,IAAI,CAACD,OAAO,CAACI,EAAE,GAAGH,UAAU,CAACG,EAAE;YACjC;YACA,OAAOZ,IAAIC,eAAe,CAACR,qBAAqB;QAClD;QACA,MAAMoB,SACJb,IAAIC,eAAe,CAAC,mBAAmB,IAAIC,QAAQC,GAAG,CAACC,kBAAkB;QAE3E,MAAMU,aACJd,IAAIC,eAAe,CAAC,uBAAuB,IAC3CC,QAAQC,GAAG,CAACY,uBAAuB;QAErC,IAAIb,QAAQC,GAAG,CAACa,yBAAyB,EAAE;YACzC,IAAI,CAACR,OAAO,CACV,gBACD,GAAG,CAAC,OAAO,EAAEN,QAAQC,GAAG,CAACa,yBAAyB,CAAC,CAAC;QACvD;QAEA,IAAIH,QAAQ;YACV,IAAI,CAACI,aAAa,GAAG,CAAC,QAAQ,EAAEJ,OAAO,EAAEC,cAAc,GAAG,CAAC;YAC3D,IAAI,IAAI,CAACR,KAAK,EAAE;gBACdY,QAAQC,GAAG,CAAC,wBAAwB,IAAI,CAACF,aAAa;YACxD;QACF,OAAO,IAAI,IAAI,CAACX,KAAK,EAAE;YACrBY,QAAQC,GAAG,CAAC;QACd;QAEA,IAAInB,IAAIoB,kBAAkB,EAAE;YAC1B,IAAI,CAAC7B,aAAa;gBAChB,IAAI,IAAI,CAACe,KAAK,EAAE;oBACdY,QAAQC,GAAG,CAAC;gBACd;gBAEA5B,cAAc,IAAIJ,SAAS;oBACzBkC,KAAKrB,IAAIoB,kBAAkB;oBAC3BE,QAAO,EAAEC,KAAK,EAAE;4BAcSb;wBAbvB,IAAI,CAACa,OAAO;4BACV,OAAO;wBACT,OAAO,IAAIA,MAAMC,IAAI,KAAK,YAAY;4BACpC,OAAOd,KAAKe,SAAS,CAACF,MAAMG,KAAK,EAAEJ,MAAM;wBAC3C,OAAO,IAAIC,MAAMC,IAAI,KAAK,SAAS;4BACjC,MAAM,IAAIG,MAAM;wBAClB,OAAO,IAAIJ,MAAMC,IAAI,KAAK,SAAS;4BACjC,OAAOd,KAAKe,SAAS,CAACF,MAAMK,IAAI,IAAI,IAAIN,MAAM;wBAChD,OAAO,IAAIC,MAAMC,IAAI,KAAK,SAAS;4BACjC,OAAOD,MAAMM,IAAI,CAACP,MAAM;wBAC1B;wBACA,wCAAwC;wBACxC,OACEC,MAAMO,IAAI,CAACR,MAAM,GAAIZ,CAAAA,EAAAA,kBAAAA,KAAKe,SAAS,CAACF,MAAMQ,QAAQ,sBAA7BrB,gBAAgCY,MAAM,KAAI,CAAA;oBAEnE;gBACF;YACF;QACF,OAAO;YACL,IAAI,IAAI,CAAChB,KAAK,EAAE;gBACdY,QAAQC,GAAG,CAAC;YACd;QACF;IACF;IAEA,MAAaa,cAAcC,GAAW,EAAE;QACtC,IAAI,IAAI,CAAC3B,KAAK,EAAE;YACdY,QAAQC,GAAG,CAAC,iBAAiBc;QAC/B;QAEA,IAAIC,KAAKC,GAAG,KAAK7C,kBAAkB;YACjC,IAAI,IAAI,CAACgB,KAAK,EAAE;gBACdY,QAAQC,GAAG,CAAC,iBAAiB7B;YAC/B;YACA;QACF;QAEA,IAAI;YACF,MAAM8C,MAAM,MAAMC,MAChB,CAAC,EAAE,IAAI,CAACpB,aAAa,CAAC,mCAAmC,EAAEgB,IAAI,CAAC,EAChE;gBACEK,QAAQ;gBACR9B,SAAS,IAAI,CAACA,OAAO;gBACrB,sCAAsC;gBACtC+B,MAAM;oBAAEC,UAAU;gBAAK;YACzB;YAGF,IAAIJ,IAAIK,MAAM,KAAK,KAAK;gBACtB,MAAMC,aAAaN,IAAI5B,OAAO,CAACmC,GAAG,CAAC,kBAAkB;gBACrDrD,mBAAmB4C,KAAKC,GAAG,KAAKS,SAASF;YAC3C;YAEA,IAAI,CAACN,IAAIS,EAAE,EAAE;gBACX,MAAM,IAAIlB,MAAM,CAAC,2BAA2B,EAAES,IAAIK,MAAM,CAAC,CAAC,CAAC;YAC7D;QACF,EAAE,OAAOK,KAAK;YACZ5B,QAAQ6B,IAAI,CAAC,CAAC,yBAAyB,EAAEd,IAAI,CAAC,EAAEa;QAClD;IACF;IAEA,MAAaH,IACXK,GAAW,EACXhD,GAMC,EACD;QACA,MAAM,EAAEiD,IAAI,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,EAAE,GAAGrD;QAE3D,IAAI,CAACmD,YAAY,OAAO;QAExB,IAAIjB,KAAKC,GAAG,KAAK7C,kBAAkB;YACjC,IAAI,IAAI,CAACgB,KAAK,EAAE;gBACdY,QAAQC,GAAG,CAAC;YACd;YACA,OAAO;QACT;QAEA,IAAIS,OAAOrC,+BAAAA,YAAaoD,GAAG,CAACK;QAE5B,0DAA0D;QAC1D,wDAAwD;QACxD,IAAId,KAAKC,GAAG,KAAMP,CAAAA,CAAAA,wBAAAA,KAAM0B,YAAY,KAAI,CAAA,IAAK,MAAM;YACjD1B,OAAO2B;QACT;QAEA,4BAA4B;QAC5B,IAAI,CAAC3B,QAAQ,IAAI,CAACX,aAAa,EAAE;YAC/B,IAAI;gBACF,MAAMuC,QAAQtB,KAAKC,GAAG;gBACtB,MAAMsB,cAAoC;oBACxCjB,UAAU;oBACVkB,WAAW;oBACXL,UAAUA;oBACVD;gBACF;gBACA,MAAMhB,MAAM,MAAMC,MAChB,CAAC,EAAE,IAAI,CAACpB,aAAa,CAAC,mBAAmB,EAAE+B,IAAI,CAAC,EAChD;oBACEV,QAAQ;oBACR9B,SAAS;wBACP,GAAG,IAAI,CAACA,OAAO;wBACf,CAACZ,uBAAuB,EAAEyD;wBAC1B,CAAC7D,kBAAkB,EAAEyD,CAAAA,wBAAAA,KAAMU,IAAI,CAAC,SAAQ;wBACxC,CAACtE,4BAA4B,EAAE6D,CAAAA,4BAAAA,SAAUS,IAAI,CAAC,SAAQ;oBACxD;oBACApB,MAAMkB;gBACR;gBAGF,IAAIrB,IAAIK,MAAM,KAAK,KAAK;oBACtB,MAAMC,aAAaN,IAAI5B,OAAO,CAACmC,GAAG,CAAC,kBAAkB;oBACrDrD,mBAAmB4C,KAAKC,GAAG,KAAKS,SAASF;gBAC3C;gBAEA,IAAIN,IAAIK,MAAM,KAAK,KAAK;oBACtB,IAAI,IAAI,CAACnC,KAAK,EAAE;wBACdY,QAAQC,GAAG,CACT,CAAC,yBAAyB,EAAE6B,IAAI,YAAY,EAC1Cd,KAAKC,GAAG,KAAKqB,MACd,EAAE,CAAC;oBAER;oBACA,OAAO;gBACT;gBAEA,IAAI,CAACpB,IAAIS,EAAE,EAAE;oBACX3B,QAAQ0C,KAAK,CAAC,MAAMxB,IAAIyB,IAAI;oBAC5B,MAAM,IAAIlC,MAAM,CAAC,4BAA4B,EAAES,IAAIK,MAAM,CAAC,CAAC;gBAC7D;gBAEA,MAAMqB,SAAS,MAAM1B,IAAI2B,IAAI;gBAE7B,IAAI,CAACD,UAAUA,OAAOtC,IAAI,KAAK,SAAS;oBACtC,IAAI,CAAClB,KAAK,IAAIY,QAAQC,GAAG,CAAC;wBAAE2C;oBAAO;oBACnC,MAAM,IAAInC,MAAM,CAAC,mBAAmB,CAAC;gBACvC;gBAEA,MAAMqC,aAAa5B,IAAI5B,OAAO,CAACmC,GAAG,CAACjD;gBACnC,MAAMuE,MAAM7B,IAAI5B,OAAO,CAACmC,GAAG,CAAC;gBAE5Bf,OAAO;oBACLL,OAAOuC;oBACP,qDAAqD;oBACrD,uCAAuC;oBACvCR,cACEU,eAAe,UACX9B,KAAKC,GAAG,KAAK/C,iBACb8C,KAAKC,GAAG,KAAKS,SAASqB,OAAO,KAAK,MAAM;gBAChD;gBAEA,IAAI,IAAI,CAAC3D,KAAK,EAAE;oBACdY,QAAQC,GAAG,CACT,CAAC,0BAA0B,EAAE6B,IAAI,YAAY,EAC3Cd,KAAKC,GAAG,KAAKqB,MACd,UAAU,EACTU,OAAOC,IAAI,CAACL,QAAQxC,MAAM,CAC3B,eAAe,EAAE0C,WAAW,OAAO,EAAEf,wBAAAA,KAAMU,IAAI,CAC9C,KACA,WAAW,EAAET,4BAAAA,SAAUS,IAAI,CAAC,KAAK,CAAC;gBAExC;gBAEA,IAAI/B,MAAM;oBACRrC,+BAAAA,YAAa6E,GAAG,CAACpB,KAAKpB;gBACxB;YACF,EAAE,OAAOkB,KAAK;gBACZ,sCAAsC;gBACtC,IAAI,IAAI,CAACxC,KAAK,EAAE;oBACdY,QAAQ0C,KAAK,CAAC,CAAC,8BAA8B,CAAC,EAAEd;gBAClD;YACF;QACF;QAEA,OAAOlB,QAAQ;IACjB;IAEA,MAAawC,IACXpB,GAAW,EACXpB,IAAgC,EAChC,EACEuB,UAAU,EACVC,QAAQ,EACRC,QAAQ,EACRJ,IAAI,EAML,EACD;QACA,IAAI,CAACE,YAAY;QAEjB,IAAIjB,KAAKC,GAAG,KAAK7C,kBAAkB;YACjC,IAAI,IAAI,CAACgB,KAAK,EAAE;gBACdY,QAAQC,GAAG,CAAC;YACd;YACA;QACF;QAEA5B,+BAAAA,YAAa6E,GAAG,CAACpB,KAAK;YACpBzB,OAAOK;YACP0B,cAAcpB,KAAKC,GAAG;QACxB;QAEA,IAAI,IAAI,CAAClB,aAAa,EAAE;YACtB,IAAI;gBACF,MAAMuC,QAAQtB,KAAKC,GAAG;gBACtB,IAAIP,SAAS,QAAQ,gBAAgBA,MAAM;oBACzC,IAAI,CAACpB,OAAO,CAACb,wBAAwB,GAAGiC,KAAKyC,UAAU,CAACC,QAAQ;gBAClE;gBACA,IACE,CAAC,IAAI,CAAC9D,OAAO,CAACb,wBAAwB,IACtCiC,SAAS,QACT,UAAUA,MACV;oBACA,IAAI,CAACpB,OAAO,CAACX,2BAA2B,GACtC+B,KAAKA,IAAI,CAACpB,OAAO,CAAC,gBAAgB;gBACtC;gBACA,MAAMqB,OAAOnB,KAAKe,SAAS,CAAC;oBAC1B,GAAGG,IAAI;oBACP,yCAAyC;oBACzC,sBAAsB;oBACtBqB,MAAMM;gBACR;gBAEA,IAAI,IAAI,CAACjD,KAAK,EAAE;oBACdY,QAAQC,GAAG,CAAC,aAAa6B;gBAC3B;gBACA,MAAMS,cAAoC;oBACxCjB,UAAU;oBACVkB,WAAW;oBACXL;oBACAD;gBACF;gBACA,MAAMhB,MAAM,MAAMC,MAChB,CAAC,EAAE,IAAI,CAACpB,aAAa,CAAC,mBAAmB,EAAE+B,IAAI,CAAC,EAChD;oBACEV,QAAQ;oBACR9B,SAAS;wBACP,GAAG,IAAI,CAACA,OAAO;wBACf,CAACZ,uBAAuB,EAAEyD,YAAY;wBACtC,CAAC7D,kBAAkB,EAAEyD,CAAAA,wBAAAA,KAAMU,IAAI,CAAC,SAAQ;oBAC1C;oBACA9B,MAAMA;oBACNU,MAAMkB;gBACR;gBAGF,IAAIrB,IAAIK,MAAM,KAAK,KAAK;oBACtB,MAAMC,aAAaN,IAAI5B,OAAO,CAACmC,GAAG,CAAC,kBAAkB;oBACrDrD,mBAAmB4C,KAAKC,GAAG,KAAKS,SAASF;gBAC3C;gBAEA,IAAI,CAACN,IAAIS,EAAE,EAAE;oBACX,IAAI,CAACvC,KAAK,IAAIY,QAAQC,GAAG,CAAC,MAAMiB,IAAIyB,IAAI;oBACxC,MAAM,IAAIlC,MAAM,CAAC,iBAAiB,EAAES,IAAIK,MAAM,CAAC,CAAC;gBAClD;gBAEA,IAAI,IAAI,CAACnC,KAAK,EAAE;oBACdY,QAAQC,GAAG,CACT,CAAC,oCAAoC,EAAE6B,IAAI,YAAY,EACrDd,KAAKC,GAAG,KAAKqB,MACd,UAAU,EAAE3B,KAAKP,MAAM,CAAC,CAAC;gBAE9B;YACF,EAAE,OAAOwB,KAAK;gBACZ,+BAA+B;gBAC/B,IAAI,IAAI,CAACxC,KAAK,EAAE;oBACdY,QAAQ0C,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAEd;gBAChD;YACF;QACF;QACA;IACF;AACF"}