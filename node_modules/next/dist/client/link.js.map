{"version": 3, "sources": ["../../src/client/link.tsx"], "names": ["prefetched", "Set", "prefetch", "router", "href", "as", "options", "appOptions", "isAppRouter", "window", "isLocalURL", "bypassPrefetchedCheck", "locale", "undefined", "prefetched<PERSON><PERSON>", "has", "add", "prefetchPromise", "Promise", "resolve", "catch", "err", "process", "env", "NODE_ENV", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "replace", "shallow", "scroll", "prefetchEnabled", "nodeName", "isAnchorNodeName", "toUpperCase", "preventDefault", "navigate", "routerScroll", "forceOptimisticNavigation", "React", "startTransition", "formatStringOrUrl", "urlObjOrString", "formatUrl", "Link", "forwardRef", "LinkComponent", "props", "forwardedRef", "children", "hrefProp", "asProp", "childrenProp", "prefetchProp", "passHref", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "restProps", "a", "pagesRouter", "useContext", "RouterContext", "appRouter", "AppRouterContext", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "createPropError", "args", "Error", "key", "expected", "actual", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "hasWarned", "useRef", "current", "console", "warn", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "resolvedAs", "resolveHref", "previousHref", "previousAs", "child", "Children", "only", "type", "childRef", "ref", "setIntersectionRef", "isVisible", "resetVisible", "useIntersection", "rootMargin", "setRef", "useCallback", "el", "useEffect", "kind", "childProps", "defaultPrevented", "priority", "isAbsoluteUrl", "cur<PERSON><PERSON><PERSON>", "localeDomain", "isLocaleDomain", "getDomainLocale", "locales", "domainLocales", "addBasePath", "addLocale", "defaultLocale", "cloneElement"], "mappings": "AAAA;;;;;+BAsvBA;;;eAAA;;;;gEA/uBkB;6BAEU;4BACD;2BACD;uBACI;2BACJ;4CACI;+CACG;iCAKD;iCACA;6BACJ;oCACC;AA0F7B,MAAMA,aAAa,IAAIC;AAUvB,SAASC,SACPC,MAAsC,EACtCC,IAAY,EACZC,EAAU,EACVC,OAAwB,EACxBC,UAAoC,EACpCC,WAAoB;IAEpB,IAAI,OAAOC,WAAW,aAAa;QACjC;IACF;IAEA,gJAAgJ;IAChJ,IAAI,CAACD,eAAe,CAACE,IAAAA,sBAAU,EAACN,OAAO;QACrC;IACF;IAEA,4EAA4E;IAC5E,YAAY;IACZ,IAAI,CAACE,QAAQK,qBAAqB,EAAE;QAClC,MAAMC,SACJ,iEAAiE;QACjE,OAAON,QAAQM,MAAM,KAAK,cACtBN,QAAQM,MAAM,GAEhB,YAAYT,SACVA,OAAOS,MAAM,GACbC;QAEN,MAAMC,gBAAgBV,OAAO,MAAMC,KAAK,MAAMO;QAE9C,kEAAkE;QAClE,IAAIZ,WAAWe,GAAG,CAACD,gBAAgB;YACjC;QACF;QAEA,+BAA+B;QAC/Bd,WAAWgB,GAAG,CAACF;IACjB;IAEA,MAAMG,kBAAkBT,cACpB,AAACL,OAA6BD,QAAQ,CAACE,MAAMG,cAC7C,AAACJ,OAAsBD,QAAQ,CAACE,MAAMC,IAAIC;IAE9C,uDAAuD;IACvD,0DAA0D;IAC1D,sDAAsD;IACtD,yDAAyD;IACzDY,QAAQC,OAAO,CAACF,iBAAiBG,KAAK,CAAC,CAACC;QACtC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,qCAAqC;YACrC,MAAMH;QACR;IACF;AACF;AAEA,SAASI,gBAAgBC,KAAuB;IAC9C,MAAMC,cAAcD,MAAME,aAAa;IACvC,MAAMC,SAASF,YAAYG,YAAY,CAAC;IACxC,OACE,AAACD,UAAUA,WAAW,WACtBH,MAAMK,OAAO,IACbL,MAAMM,OAAO,IACbN,MAAMO,QAAQ,IACdP,MAAMQ,MAAM,IAAI,6BAA6B;IAC5CR,MAAMS,WAAW,IAAIT,MAAMS,WAAW,CAACC,KAAK,KAAK;AAEtD;AAEA,SAASC,YACPC,CAAmB,EACnBnC,MAAsC,EACtCC,IAAY,EACZC,EAAU,EACVkC,OAAiB,EACjBC,OAAiB,EACjBC,MAAgB,EAChB7B,MAAuB,EACvBJ,WAAqB,EACrBkC,eAAyB;IAEzB,MAAM,EAAEC,QAAQ,EAAE,GAAGL,EAAEV,aAAa;IAEpC,kDAAkD;IAClD,MAAMgB,mBAAmBD,SAASE,WAAW,OAAO;IAEpD,IACED,oBACCnB,CAAAA,gBAAgBa,MACf,gJAAgJ;IAC/I,CAAC9B,eAAe,CAACE,IAAAA,sBAAU,EAACN,KAAK,GACpC;QACA,8CAA8C;QAC9C;IACF;IAEAkC,EAAEQ,cAAc;IAEhB,MAAMC,WAAW;QACf,wEAAwE;QACxE,MAAMC,eAAeP,iBAAAA,SAAU;QAC/B,IAAI,oBAAoBtC,QAAQ;YAC9BA,MAAM,CAACoC,UAAU,YAAY,OAAO,CAACnC,MAAMC,IAAI;gBAC7CmC;gBACA5B;gBACA6B,QAAQO;YACV;QACF,OAAO;YACL7C,MAAM,CAACoC,UAAU,YAAY,OAAO,CAAClC,MAAMD,MAAM;gBAC/C6C,2BAA2B,CAACP;gBAC5BD,QAAQO;YACV;QACF;IACF;IAEA,IAAIxC,aAAa;QACf0C,cAAK,CAACC,eAAe,CAACJ;IACxB,OAAO;QACLA;IACF;AACF;AAOA,SAASK,kBAAkBC,cAAkC;IAC3D,IAAI,OAAOA,mBAAmB,UAAU;QACtC,OAAOA;IACT;IAEA,OAAOC,IAAAA,oBAAS,EAACD;AACnB;AAEA;;CAEC,GACD,MAAME,qBAAOL,cAAK,CAACM,UAAU,CAC3B,SAASC,cAAcC,KAAK,EAAEC,YAAY;IACxC,IAAIC;IAEJ,MAAM,EACJxD,MAAMyD,QAAQ,EACdxD,IAAIyD,MAAM,EACVF,UAAUG,YAAY,EACtB7D,UAAU8D,eAAe,IAAI,EAC7BC,QAAQ,EACR1B,OAAO,EACPC,OAAO,EACPC,MAAM,EACN7B,MAAM,EACNsD,OAAO,EACPC,cAAcC,gBAAgB,EAC9BC,cAAcC,gBAAgB,EAC9BC,iBAAiB,KAAK,EACtB,GAAGC,WACJ,GAAGd;IAEJE,WAAWG;IAEX,IACEQ,kBACC,CAAA,OAAOX,aAAa,YAAY,OAAOA,aAAa,QAAO,GAC5D;QACAA,yBAAW,6BAACa,WAAGb;IACjB;IAEA,MAAMc,cAAcxB,cAAK,CAACyB,UAAU,CAACC,yCAAa;IAClD,MAAMC,YAAY3B,cAAK,CAACyB,UAAU,CAACG,+CAAgB;IACnD,MAAM3E,SAASuE,sBAAAA,cAAeG;IAE9B,0DAA0D;IAC1D,MAAMrE,cAAc,CAACkE;IAErB,MAAMhC,kBAAkBsB,iBAAiB;IACzC;;;;;KAKC,GACD,MAAMe,kBACJf,iBAAiB,OAAOgB,gCAAY,CAACC,IAAI,GAAGD,gCAAY,CAACE,IAAI;IAE/D,IAAI5D,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,SAAS2D,gBAAgBC,IAIxB;YACC,OAAO,IAAIC,MACT,AAAC,iCAA+BD,KAAKE,GAAG,GAAC,iBAAeF,KAAKG,QAAQ,GAAC,4BAA4BH,KAAKI,MAAM,GAAC,eAC3G,CAAA,OAAO/E,WAAW,cACf,qEACA,EAAC;QAEX;QAEA,sCAAsC;QACtC,MAAMgF,qBAAsD;YAC1DrF,MAAM;QACR;QACA,MAAMsF,gBAAqCC,OAAOC,IAAI,CACpDH;QAEFC,cAAcG,OAAO,CAAC,CAACP;YACrB,IAAIA,QAAQ,QAAQ;gBAClB,IACE5B,KAAK,CAAC4B,IAAI,IAAI,QACb,OAAO5B,KAAK,CAAC4B,IAAI,KAAK,YAAY,OAAO5B,KAAK,CAAC4B,IAAI,KAAK,UACzD;oBACA,MAAMH,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQ9B,KAAK,CAAC4B,IAAI,KAAK,OAAO,SAAS,OAAO5B,KAAK,CAAC4B,IAAI;oBAC1D;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMQ,IAAWR;YACnB;QACF;QAEA,sCAAsC;QACtC,MAAMS,qBAAsD;YAC1D1F,IAAI;YACJkC,SAAS;YACTE,QAAQ;YACRD,SAAS;YACTyB,UAAU;YACV/D,UAAU;YACVU,QAAQ;YACRsD,SAAS;YACTC,cAAc;YACdE,cAAc;YACdE,gBAAgB;QAClB;QACA,MAAMyB,gBAAqCL,OAAOC,IAAI,CACpDG;QAEFC,cAAcH,OAAO,CAAC,CAACP;YACrB,MAAMW,UAAU,OAAOvC,KAAK,CAAC4B,IAAI;YAEjC,IAAIA,QAAQ,MAAM;gBAChB,IAAI5B,KAAK,CAAC4B,IAAI,IAAIW,YAAY,YAAYA,YAAY,UAAU;oBAC9D,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IAAIX,QAAQ,UAAU;gBAC3B,IAAI5B,KAAK,CAAC4B,IAAI,IAAIW,YAAY,UAAU;oBACtC,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IACLX,QAAQ,aACRA,QAAQ,kBACRA,QAAQ,gBACR;gBACA,IAAI5B,KAAK,CAAC4B,IAAI,IAAIW,YAAY,YAAY;oBACxC,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IACLX,QAAQ,aACRA,QAAQ,YACRA,QAAQ,aACRA,QAAQ,cACRA,QAAQ,cACRA,QAAQ,kBACR;gBACA,IAAI5B,KAAK,CAAC4B,IAAI,IAAI,QAAQW,YAAY,WAAW;oBAC/C,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMH,IAAWR;YACnB;QACF;QAEA,4FAA4F;QAC5F,sDAAsD;QACtD,MAAMY,YAAYhD,cAAK,CAACiD,MAAM,CAAC;QAC/B,IAAIzC,MAAMxD,QAAQ,IAAI,CAACgG,UAAUE,OAAO,IAAI,CAAC5F,aAAa;YACxD0F,UAAUE,OAAO,GAAG;YACpBC,QAAQC,IAAI,CACV;QAEJ;IACF;IAEA,IAAIhF,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,IAAIhB,eAAe,CAACsD,QAAQ;YAC1B,IAAI1D;YACJ,IAAI,OAAOyD,aAAa,UAAU;gBAChCzD,OAAOyD;YACT,OAAO,IACL,OAAOA,aAAa,YACpB,OAAOA,SAAS0C,QAAQ,KAAK,UAC7B;gBACAnG,OAAOyD,SAAS0C,QAAQ;YAC1B;YAEA,IAAInG,MAAM;gBACR,MAAMoG,oBAAoBpG,KACvBqG,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UAAYA,QAAQC,UAAU,CAAC,QAAQD,QAAQE,QAAQ,CAAC;gBAEjE,IAAIL,mBAAmB;oBACrB,MAAM,IAAInB,MACR,AAAC,mBAAiBjF,OAAK;gBAE3B;YACF;QACF;IACF;IAEA,MAAM,EAAEA,IAAI,EAAEC,EAAE,EAAE,GAAG6C,cAAK,CAAC4D,OAAO,CAAC;QACjC,IAAI,CAACpC,aAAa;YAChB,MAAMqC,eAAe3D,kBAAkBS;YACvC,OAAO;gBACLzD,MAAM2G;gBACN1G,IAAIyD,SAASV,kBAAkBU,UAAUiD;YAC3C;QACF;QAEA,MAAM,CAACA,cAAcC,WAAW,GAAGC,IAAAA,wBAAW,EAC5CvC,aACAb,UACA;QAGF,OAAO;YACLzD,MAAM2G;YACN1G,IAAIyD,SACAmD,IAAAA,wBAAW,EAACvC,aAAaZ,UACzBkD,cAAcD;QACpB;IACF,GAAG;QAACrC;QAAab;QAAUC;KAAO;IAElC,MAAMoD,eAAehE,cAAK,CAACiD,MAAM,CAAS/F;IAC1C,MAAM+G,aAAajE,cAAK,CAACiD,MAAM,CAAS9F;IAExC,oFAAoF;IACpF,IAAI+G;IACJ,IAAI7C,gBAAgB;QAClB,IAAIjD,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,IAAI0C,SAAS;gBACXmC,QAAQC,IAAI,CACV,AAAC,oDAAoDzC,WAAS;YAElE;YACA,IAAIO,kBAAkB;gBACpBiC,QAAQC,IAAI,CACV,AAAC,yDAAyDzC,WAAS;YAEvE;YACA,IAAI;gBACFuD,QAAQlE,cAAK,CAACmE,QAAQ,CAACC,IAAI,CAAC1D;YAC9B,EAAE,OAAOvC,KAAK;gBACZ,IAAI,CAACuC,UAAU;oBACb,MAAM,IAAIyB,MACR,AAAC,uDAAuDxB,WAAS;gBAErE;gBACA,MAAM,IAAIwB,MACR,AAAC,6DAA6DxB,WAAS,8FACpE,CAAA,OAAOpD,WAAW,cACf,sEACA,EAAC;YAEX;QACF,OAAO;YACL2G,QAAQlE,cAAK,CAACmE,QAAQ,CAACC,IAAI,CAAC1D;QAC9B;IACF,OAAO;QACL,IAAItC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,IAAI,CAACoC,4BAAD,AAACA,SAAkB2D,IAAI,MAAK,KAAK;gBACnC,MAAM,IAAIlC,MACR;YAEJ;QACF;IACF;IAEA,MAAMmC,WAAgBjD,iBAClB6C,SAAS,OAAOA,UAAU,YAAYA,MAAMK,GAAG,GAC/C9D;IAEJ,MAAM,CAAC+D,oBAAoBC,WAAWC,aAAa,GAAGC,IAAAA,gCAAe,EAAC;QACpEC,YAAY;IACd;IAEA,MAAMC,SAAS7E,cAAK,CAAC8E,WAAW,CAC9B,CAACC;QACC,4EAA4E;QAC5E,IAAId,WAAWf,OAAO,KAAK/F,MAAM6G,aAAad,OAAO,KAAKhG,MAAM;YAC9DwH;YACAT,WAAWf,OAAO,GAAG/F;YACrB6G,aAAad,OAAO,GAAGhG;QACzB;QAEAsH,mBAAmBO;QACnB,IAAIT,UAAU;YACZ,IAAI,OAAOA,aAAa,YAAYA,SAASS;iBACxC,IAAI,OAAOT,aAAa,UAAU;gBACrCA,SAASpB,OAAO,GAAG6B;YACrB;QACF;IACF,GACA;QAAC5H;QAAImH;QAAUpH;QAAMwH;QAAcF;KAAmB;IAGxD,2DAA2D;IAC3DxE,cAAK,CAACgF,SAAS,CAAC;QACd,gHAAgH;QAChH,IAAI5G,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC;QACF;QAEA,IAAI,CAACrB,QAAQ;YACX;QACF;QAEA,2DAA2D;QAC3D,IAAI,CAACwH,aAAa,CAACjF,iBAAiB;YAClC;QACF;QAEA,oBAAoB;QACpBxC,SACEC,QACAC,MACAC,IACA;YAAEO;QAAO,GACT;YACEuH,MAAMpD;QACR,GACAvE;IAEJ,GAAG;QACDH;QACAD;QACAuH;QACA/G;QACA8B;QACAgC,+BAAAA,YAAa9D,MAAM;QACnBT;QACAK;QACAuE;KACD;IAED,MAAMqD,aAMF;QACFX,KAAKM;QACL7D,SAAQ5B,CAAC;YACP,IAAIhB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzC,IAAI,CAACc,GAAG;oBACN,MAAM,IAAI+C,MACP;gBAEL;YACF;YAEA,IAAI,CAACd,kBAAkB,OAAOL,YAAY,YAAY;gBACpDA,QAAQ5B;YACV;YAEA,IACEiC,kBACA6C,MAAM1D,KAAK,IACX,OAAO0D,MAAM1D,KAAK,CAACQ,OAAO,KAAK,YAC/B;gBACAkD,MAAM1D,KAAK,CAACQ,OAAO,CAAC5B;YACtB;YAEA,IAAI,CAACnC,QAAQ;gBACX;YACF;YAEA,IAAImC,EAAE+F,gBAAgB,EAAE;gBACtB;YACF;YAEAhG,YACEC,GACAnC,QACAC,MACAC,IACAkC,SACAC,SACAC,QACA7B,QACAJ,aACAkC;QAEJ;QACAyB,cAAa7B,CAAC;YACZ,IAAI,CAACiC,kBAAkB,OAAOH,qBAAqB,YAAY;gBAC7DA,iBAAiB9B;YACnB;YAEA,IACEiC,kBACA6C,MAAM1D,KAAK,IACX,OAAO0D,MAAM1D,KAAK,CAACS,YAAY,KAAK,YACpC;gBACAiD,MAAM1D,KAAK,CAACS,YAAY,CAAC7B;YAC3B;YAEA,IAAI,CAACnC,QAAQ;gBACX;YACF;YAEA,IACE,AAAC,CAAA,CAACuC,mBAAmBpB,QAAQC,GAAG,CAACC,QAAQ,KAAK,aAAY,KAC1DhB,aACA;gBACA;YACF;YAEAN,SACEC,QACAC,MACAC,IACA;gBACEO;gBACA0H,UAAU;gBACV,gGAAgG;gBAChG3H,uBAAuB;YACzB,GACA;gBACEwH,MAAMpD;YACR,GACAvE;QAEJ;QACA6D,cAAa/B,CAAC;YACZ,IAAI,CAACiC,kBAAkB,OAAOD,qBAAqB,YAAY;gBAC7DA,iBAAiBhC;YACnB;YAEA,IACEiC,kBACA6C,MAAM1D,KAAK,IACX,OAAO0D,MAAM1D,KAAK,CAACW,YAAY,KAAK,YACpC;gBACA+C,MAAM1D,KAAK,CAACW,YAAY,CAAC/B;YAC3B;YAEA,IAAI,CAACnC,QAAQ;gBACX;YACF;YAEA,IAAI,CAACuC,mBAAmBlC,aAAa;gBACnC;YACF;YAEAN,SACEC,QACAC,MACAC,IACA;gBACEO;gBACA0H,UAAU;gBACV,gGAAgG;gBAChG3H,uBAAuB;YACzB,GACA;gBACEwH,MAAMpD;YACR,GACAvE;QAEJ;IACF;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,oFAAoF;IACpF,IAAI+H,IAAAA,oBAAa,EAAClI,KAAK;QACrB+H,WAAWhI,IAAI,GAAGC;IACpB,OAAO,IACL,CAACkE,kBACDN,YACCmD,MAAMG,IAAI,KAAK,OAAO,CAAE,CAAA,UAAUH,MAAM1D,KAAK,AAAD,GAC7C;QACA,MAAM8E,YACJ,OAAO5H,WAAW,cAAcA,SAAS8D,+BAAAA,YAAa9D,MAAM;QAE9D,uEAAuE;QACvE,uEAAuE;QACvE,MAAM6H,eACJ/D,CAAAA,+BAAAA,YAAagE,cAAc,KAC3BC,IAAAA,gCAAe,EACbtI,IACAmI,WACA9D,+BAAAA,YAAakE,OAAO,EACpBlE,+BAAAA,YAAamE,aAAa;QAG9BT,WAAWhI,IAAI,GACbqI,gBACAK,IAAAA,wBAAW,EAACC,IAAAA,oBAAS,EAAC1I,IAAImI,WAAW9D,+BAAAA,YAAasE,aAAa;IACnE;IAEA,OAAOzE,+BACLrB,cAAK,CAAC+F,YAAY,CAAC7B,OAAOgB,4BAE1B,6BAAC3D;QAAG,GAAGD,SAAS;QAAG,GAAG4D,UAAU;OAC7BxE;AAGP;MAGF,WAAeL"}