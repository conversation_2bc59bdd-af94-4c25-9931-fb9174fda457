{"version": 3, "sources": ["../../src/lib/create-client-router-filter.ts"], "names": ["createClientRouterFilter", "paths", "redirects", "allowedErrorRate", "staticPaths", "Set", "dynamicPaths", "path", "isDynamicRoute", "subPath", "pathParts", "split", "i", "length", "cur<PERSON><PERSON>", "startsWith", "add", "redirect", "source", "removeTrailingSlash", "tokens", "tryToParsePath", "every", "token", "staticFilter", "<PERSON><PERSON><PERSON><PERSON>", "from", "dynamicFilter", "data", "export"], "mappings": ";;;;+BAOgBA;;;eAAAA;;;6BANY;uBACG;qCACK;gCAEL;AAExB,SAASA,yBACdC,KAAe,EACfC,SAAqB,EACrBC,gBAAyB;IAKzB,MAAMC,cAAc,IAAIC;IACxB,MAAMC,eAAe,IAAID;IAEzB,KAAK,MAAME,QAAQN,MAAO;QACxB,IAAIO,IAAAA,qBAAc,EAACD,OAAO;YACxB,IAAIE,UAAU;YACd,MAAMC,YAAYH,KAAKI,KAAK,CAAC;YAE7B,uDAAuD;YACvD,kDAAkD;YAClD,IAAK,IAAIC,IAAI,GAAGA,IAAIF,UAAUG,MAAM,GAAG,GAAGD,IAAK;gBAC7C,MAAME,UAAUJ,SAAS,CAACE,EAAE;gBAE5B,IAAIE,QAAQC,UAAU,CAAC,MAAM;oBAC3B;gBACF;gBACAN,UAAU,CAAC,EAAEA,QAAQ,CAAC,EAAEK,QAAQ,CAAC;YACnC;YAEA,IAAIL,SAAS;gBACXH,aAAaU,GAAG,CAACP;YACnB;QACF,OAAO;YACLL,YAAYY,GAAG,CAACT;QAClB;IACF;IAEA,KAAK,MAAMU,YAAYf,UAAW;QAChC,MAAM,EAAEgB,MAAM,EAAE,GAAGD;QACnB,MAAMV,OAAOY,IAAAA,wCAAmB,EAACD;QACjC,IAAIE,SAAkB,EAAE;QAExB,IAAI;YACFA,SAASC,IAAAA,8BAAc,EAACH,QAAQE,MAAM,IAAI,EAAE;QAC9C,EAAE,OAAM,CAAC;QAET,IAAIA,OAAOE,KAAK,CAAC,CAACC,QAAU,OAAOA,UAAU,WAAW;YACtD,0CAA0C;YAC1CnB,YAAYY,GAAG,CAACT;QAClB;IACF;IAEA,MAAMiB,eAAeC,wBAAW,CAACC,IAAI,CAAC;WAAItB;KAAY,EAAED;IAExD,MAAMwB,gBAAgBF,wBAAW,CAACC,IAAI,CAAC;WAAIpB;KAAa,EAAEH;IAC1D,MAAMyB,OAAO;QACXJ,cAAcA,aAAaK,MAAM;QACjCF,eAAeA,cAAcE,MAAM;IACrC;IACA,OAAOD;AACT"}